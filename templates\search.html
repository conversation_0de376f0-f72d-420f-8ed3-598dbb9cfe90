{% extends "base.html" %}

{% block title %}البحث المتقدم - نظام اجتماعات المدير{% endblock %}

{% block extra_css %}
<style>
    .search-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }
    
    .search-header {
        background: var(--jaf-gradient);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
    }
    
    .search-body {
        padding: 2rem;
    }
    
    .search-tabs {
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 2rem;
    }
    
    .search-tab {
        background: none;
        border: none;
        padding: 1rem 2rem;
        color: #6c757d;
        font-weight: 500;
        border-bottom: 3px solid transparent;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .search-tab.active {
        color: var(--jaf-primary);
        border-bottom-color: var(--jaf-primary);
        background-color: rgba(27, 67, 50, 0.05);
    }
    
    .search-tab:hover {
        color: var(--jaf-primary);
        background-color: rgba(27, 67, 50, 0.05);
    }
    
    .search-form {
        display: none;
    }
    
    .search-form.active {
        display: block;
        animation: fadeInUp 0.5s ease;
    }
    
    .results-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .results-header {
        background: var(--jaf-secondary);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .results-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.9rem;
    }
    
    .result-item {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .result-item:hover {
        background-color: rgba(27, 67, 50, 0.05);
        transform: translateX(-3px);
    }
    
    .result-item:last-child {
        border-bottom: none;
    }
    
    .result-title {
        font-weight: 600;
        color: var(--jaf-primary);
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }
    
    .result-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .result-detail {
        display: flex;
        align-items: center;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .result-detail i {
        margin-left: 0.5rem;
        width: 16px;
        text-align: center;
    }
    
    .result-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .btn-result-action {
        padding: 0.375rem 1rem;
        border-radius: 20px;
        border: none;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }
    
    .btn-view {
        background-color: var(--jaf-info);
        color: white;
    }
    
    .btn-edit {
        background-color: var(--jaf-warning);
        color: white;
    }
    
    .btn-print {
        background-color: var(--jaf-secondary);
        color: white;
    }
    
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .no-results i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* تحسينات النتائج الجديدة */
    .result-item {
        border-bottom: 1px solid #e9ecef;
        padding: 1.5rem 2rem;
        transition: all 0.3s ease;
    }

    .result-item:hover {
        background-color: #f8f9fa;
    }

    .result-item:last-child {
        border-bottom: none;
    }

    .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .result-title {
        font-size: 1.1rem;
        color: #1B4332;
    }

    .result-actions {
        display: flex;
        gap: 0.5rem;
    }

    .result-details {
        margin-bottom: 1rem;
    }

    .detail-item {
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .result-notes {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
        color: #856404;
    }

    .result-actions-bottom {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .advanced-options {
        background: rgba(27, 67, 50, 0.05);
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 1rem;
    }
    
    .date-range-inputs {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 1rem;
        align-items: center;
    }

    /* إصلاح عرض حقول التاريخ مع Date Picker محسن */
    input[type="date"] {
        direction: ltr !important;
        text-align: left !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        unicode-bidi: bidi-override !important;
        padding: 12px 16px !important;
        border: 2px solid #e0e0e0 !important;
        border-radius: 8px !important;
        background-color: #ffffff !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
        min-height: 50px !important;
    }

    /* تحسين مظهر Date Picker عند التركيز */
    input[type="date"]:focus {
        border-color: #1B4332 !important;
        box-shadow: 0 0 0 3px rgba(27, 67, 50, 0.1) !important;
        outline: none !important;
    }

    /* تحسين مظهر Date Picker عند التمرير */
    input[type="date"]:hover {
        border-color: #2D5A3D !important;
        background-color: #f8f9fa !important;
    }

    /* إصلاح عرض تسميات التاريخ - حل جديد */
    .form-floating label {
        direction: rtl !important;
        text-align: right !important;
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
        width: auto !important;
        max-width: none !important;
    }

    .form-floating label span {
        direction: rtl !important;
        unicode-bidi: embed !important;
        display: inline-block !important;
        width: auto !important;
        white-space: nowrap !important;
    }

    /* منع تقطيع النص في التسميات */
    .form-floating > label {
        word-break: keep-all !important;
        word-wrap: normal !important;
        hyphens: none !important;
        overflow-wrap: normal !important;
    }

    /* تحسين أيقونة التقويم */
    input[type="date"]::-webkit-calendar-picker-indicator {
        opacity: 1 !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%231B4332' d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e") !important;
        background-size: 20px 20px !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        width: 30px !important;
        height: 30px !important;
        cursor: pointer !important;
        margin-left: 8px !important;
        border-radius: 4px !important;
        transition: all 0.2s ease !important;
    }

    /* تأثير عند التمرير على أيقونة التقويم */
    input[type="date"]::-webkit-calendar-picker-indicator:hover {
        background-color: rgba(27, 67, 50, 0.1) !important;
        transform: translateY(-50%) scale(1.1) !important;
    }

    /* إصلاح خاص لصفحة البحث */
    .form-floating input[type="date"] {
        position: relative !important;
        padding-right: 40px !important;
    }

    .form-floating input[type="date"] + label {
        position: absolute !important;
        top: 0 !important;
        right: 16px !important;
        transform: translateY(0) scale(0.85) !important;
        transform-origin: right top !important;
        background: white !important;
        padding: 0 8px !important;
        z-index: 15 !important;
        color: #1B4332 !important;
    }

    /* إخفاء الأزرار الافتراضية */
    input[type="date"]::-webkit-inner-spin-button,
    input[type="date"]::-webkit-outer-spin-button {
        -webkit-appearance: none !important;
        margin: 0 !important;
        display: none !important;
    }
    
    .search-suggestions {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
        display: none;
    }
    
    .suggestion-item {
        padding: 0.75rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
    }
    
    .suggestion-item:hover {
        background-color: rgba(27, 67, 50, 0.05);
    }
    
    .suggestion-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-primary mb-1">
                <i class="fas fa-search me-2"></i>
                البحث المتقدم
            </h2>
            <p class="text-muted mb-0">البحث في الاجتماعات بمعايير متعددة</p>
        </div>
        <div>
            <button type="button" class="btn btn-outline-secondary" onclick="clearAllSearches()">
                <i class="fas fa-eraser me-1"></i>
                مسح جميع البحوث
            </button>
        </div>
    </div>
    
    <!-- Search Section -->
    <div class="search-section fade-in-up">
        <div class="search-header">
            <i class="fas fa-filter me-2"></i>
            معايير البحث
        </div>
        
        <div class="search-body">
            <!-- Search Tabs -->
            <div class="search-tabs">
                <button class="search-tab active" onclick="switchSearchTab('book-number')">
                    <i class="fas fa-file-alt me-2"></i>
                    رقم الكتاب
                </button>
                <button class="search-tab" onclick="switchSearchTab('inviting-party')">
                    <i class="fas fa-building me-2"></i>
                    جهة الدعوة
                </button>
                <button class="search-tab" onclick="switchSearchTab('subject')">
                    <i class="fas fa-comment-alt me-2"></i>
                    الموضوع
                </button>
                <button class="search-tab" onclick="switchSearchTab('date')">
                    <i class="fas fa-calendar me-2"></i>
                    التاريخ
                </button>
                <button class="search-tab" onclick="switchSearchTab('type')">
                    <i class="fas fa-tags me-2"></i>
                    نوع الفعالية
                </button>
                <button class="search-tab" onclick="switchSearchTab('advanced')">
                    <i class="fas fa-cogs me-2"></i>
                    بحث متقدم
                </button>
            </div>
            
            <!-- Book Number Search -->
            <div id="book-number-form" class="search-form active">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-floating position-relative">
                            <input type="number" class="form-control" id="bookNumberInput"
                                   placeholder="مثال: 123" min="1" max="999999" autocomplete="off">
                            <label for="bookNumberInput">
                                <i class="fas fa-file-alt me-2"></i>رقم الكتاب
                            </label>
                            <div class="form-text">أدخل رقم الكتاب فقط (أرقام فقط)</div>
                            <div class="search-suggestions" id="bookNumberSuggestions"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-jaf-primary w-100 h-100" onclick="searchByBookNumber()">
                            <i class="fas fa-search me-2"></i>
                            البحث
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Inviting Party Search -->
            <div id="inviting-party-form" class="search-form">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-floating position-relative">
                            <input type="text" class="form-control" id="invitingPartyInput" 
                                   placeholder="اسم الجهة أو المؤسسة" autocomplete="off">
                            <label for="invitingPartyInput">
                                <i class="fas fa-building me-2"></i>جهة الدعوة
                            </label>
                            <div class="search-suggestions" id="invitingPartySuggestions"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-jaf-primary w-100 h-100" onclick="searchByInvitingParty()">
                            <i class="fas fa-search me-2"></i>
                            البحث
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Subject Search -->
            <div id="subject-form" class="search-form">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="subjectInput" 
                                   placeholder="كلمات مفتاحية من موضوع الاجتماع">
                            <label for="subjectInput">
                                <i class="fas fa-comment-alt me-2"></i>موضوع الاجتماع
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-jaf-primary w-100 h-100" onclick="searchBySubject()">
                            <i class="fas fa-search me-2"></i>
                            البحث
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Date Search -->
            <div id="date-form" class="search-form">
                <div class="professional-section">
                    <div class="section-title">
                        <i class="fas fa-search text-primary me-2"></i>
                        <h5 class="mb-0">🔍 البحث بالتاريخ</h5>
                    </div>
                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="professional-input-group">
                                <label class="professional-label">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    من تاريخ
                                </label>
                                <div class="input-wrapper">
                                    <input type="date" class="form-control professional-input" id="dateFromInput" dir="ltr">
                                    <div class="input-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="professional-input-group">
                                <label class="professional-label">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    إلى تاريخ
                                </label>
                                <div class="input-wrapper">
                                    <input type="date" class="form-control professional-input" id="dateToInput" dir="ltr">
                                    <div class="input-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="professional-input-group">
                                <label class="professional-label">
                                    <i class="fas fa-clock me-2"></i>
                                    فترات سريعة
                                </label>
                                <div class="input-wrapper">
                                    <select class="form-select professional-input" id="datePresetSelect">
                                        <option value="">اختر فترة محددة</option>
                                        <option value="today">📅 اليوم</option>
                                        <option value="week">📅 هذا الأسبوع</option>
                                        <option value="month">📅 هذا الشهر</option>
                                        <option value="quarter">📅 هذا الربع</option>
                                        <option value="year">📅 هذا العام</option>
                                    </select>
                                    <div class="input-icon">
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="professional-input-group">
                                <label class="professional-label">
                                    <i class="fas fa-search me-2"></i>
                                    تنفيذ البحث
                                </label>
                                <div class="input-wrapper">
                                    <button type="button" class="btn btn-jaf-primary professional-input w-100" onclick="searchByDate()">
                                        <i class="fas fa-search me-2"></i>
                                        البحث بالتاريخ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Type Search -->
            <div id="type-form" class="search-form">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-floating">
                            <select class="form-select" id="typeSelect">
                                <option value="">اختر نوع الفعالية</option>
                                <option value="اجتماع">اجتماع</option>
                                <option value="دعوة">دعوة</option>
                                <option value="زيارة">زيارة</option>
                                <option value="إيجاز">إيجاز</option>
                                <option value="تمرين">تمرين</option>
                                <option value="اتصال مرئي">اتصال مرئي</option>
                            </select>
                            <label for="typeSelect">
                                <i class="fas fa-tags me-2"></i>نوع الفعالية
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-jaf-primary w-100 h-100" onclick="searchByType()">
                            <i class="fas fa-search me-2"></i>
                            البحث
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Advanced Search -->
            <div id="advanced-form" class="search-form">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="advancedSubject">
                            <label for="advancedSubject">موضوع الاجتماع</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="advancedLocation">
                            <label for="advancedLocation">مكان الاجتماع</label>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-floating">
                            <select class="form-select" id="advancedType">
                                <option value="">جميع الأنواع</option>
                                <option value="اجتماع">اجتماع</option>
                                <option value="دعوة">دعوة</option>
                                <option value="زيارة">زيارة</option>
                                <option value="إيجاز">إيجاز</option>
                                <option value="تمرين">تمرين</option>
                                <option value="اتصال مرئي">اتصال مرئي</option>
                            </select>
                            <label for="advancedType">نوع الفعالية</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating position-relative">
                            <input type="date" class="form-control" id="advancedDateFrom" dir="ltr" placeholder=" ">
                            <label for="advancedDateFrom" style="direction: rtl; text-align: right;">
                                <i class="fas fa-calendar-alt me-2" style="color: #1B4332;"></i>من تاريخ
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating position-relative">
                            <input type="date" class="form-control" id="advancedDateTo" dir="ltr" placeholder=" ">
                            <label for="advancedDateTo" style="direction: rtl; text-align: right;">
                                <i class="fas fa-calendar-alt me-2" style="color: #1B4332;"></i>إلى تاريخ
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="advanced-options">
                    <h6 class="fw-bold mb-3">خيارات متقدمة</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeNotes">
                                <label class="form-check-label" for="includeNotes">
                                    البحث في الملاحظات
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="exactMatch">
                                <label class="form-check-label" for="exactMatch">
                                    مطابقة تامة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="caseSensitive">
                                <label class="form-check-label" for="caseSensitive">
                                    حساس لحالة الأحرف
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-jaf-primary btn-lg" onclick="performAdvancedSearch()">
                        <i class="fas fa-search me-2"></i>
                        بحث متقدم
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Results Section -->
    {% if search_performed %}
    <div id="resultsSection" class="results-section fade-in-up" style="display: block;">
        <div class="results-header">
            <div>
                <i class="fas fa-list me-2"></i>
                نتائج البحث
                {% if search_type and search_value %}
                    <small style="opacity: 0.8; margin-right: 10px;">
                        (البحث: {{ search_value }})
                    </small>
                {% endif %}
            </div>
            <div class="results-count" id="resultsCount">{{ results|length }} نتيجة</div>
        </div>

        <div id="resultsContainer">
            {% if results %}
                {% for meeting in results %}
                <div class="result-item">
                    <div class="result-header">
                        <div class="result-title">
                            <i class="fas fa-calendar-check me-2 text-primary"></i>
                            <strong>{{ meeting.subject or 'بدون موضوع' }}</strong>
                        </div>
                        <div class="result-actions">
                            <span class="badge bg-primary">{{ meeting.meeting_type or 'غير محدد' }}</span>
                            {% if meeting.is_cancelled %}
                                <span class="badge bg-danger">ملغي</span>
                            {% else %}
                                <span class="badge bg-success">نشط</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="result-details">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="detail-item">
                                    <i class="fas fa-calendar text-primary me-2"></i>
                                    <strong>التاريخ:</strong> {{ meeting.meeting_date.strftime('%Y/%m/%d') }}
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <strong>الوقت:</strong> {{ meeting.meeting_time | twelve_hour_time }}
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <strong>المكان:</strong> {{ meeting.location or 'غير محدد' }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="detail-item">
                                    <i class="fas fa-building text-primary me-2"></i>
                                    <strong>جهة الدعوة:</strong> {{ meeting.inviting_party or 'غير محدد' }}
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-book text-primary me-2"></i>
                                    <strong>رقم الكتاب:</strong> {{ meeting.book_number or 'غير محدد' }}
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <strong>تاريخ الكتاب:</strong> {{ meeting.book_date or 'غير محدد' }}
                                </div>
                            </div>
                        </div>

                        {% if meeting.notes %}
                        <div class="result-notes">
                            <i class="fas fa-sticky-note text-warning me-2"></i>
                            <strong>ملاحظات:</strong> {{ meeting.notes }}
                        </div>
                        {% endif %}

                        <div class="result-actions-bottom">
                            <a href="/edit_meetings" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </a>
                            <button class="btn btn-sm btn-outline-info" onclick="printMeeting({{ meeting.id }})">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h4>لا توجد نتائج</h4>
                    <p>لم يتم العثور على اجتماعات تطابق معايير البحث المحددة</p>
                    <button class="btn btn-primary" onclick="clearSearch()">
                        <i class="fas fa-redo me-2"></i>بحث جديد
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/search.js') }}"></script>

<!-- تحسين Date Picker -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعداد التواريخ الافتراضية
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // تعيين التواريخ الافتراضية
    const dateFromInput = document.getElementById('dateFromInput');
    const dateToInput = document.getElementById('dateToInput');
    const advancedDateFrom = document.getElementById('advancedDateFrom');
    const advancedDateTo = document.getElementById('advancedDateTo');

    if (dateFromInput && !dateFromInput.value) {
        dateFromInput.value = firstDayOfMonth.toISOString().split('T')[0];
    }
    if (dateToInput && !dateToInput.value) {
        dateToInput.value = today.toISOString().split('T')[0];
    }
    if (advancedDateFrom && !advancedDateFrom.value) {
        advancedDateFrom.value = firstDayOfMonth.toISOString().split('T')[0];
    }
    if (advancedDateTo && !advancedDateTo.value) {
        advancedDateTo.value = today.toISOString().split('T')[0];
    }

    // تحسين تفاعل Date Picker
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            // تأثير بصري عند اختيار التاريخ
            this.style.borderColor = '#28a745';
            this.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.1)';

            // إزالة التأثير بعد ثانية
            setTimeout(() => {
                this.style.borderColor = '#1B4332';
                this.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';
            }, 1000);

            // إظهار رسالة تأكيد
            showDateSelectedMessage(this);
        });

        // فتح Date Picker عند التركيز
        input.addEventListener('focus', function() {
            if (this.showPicker) {
                this.showPicker();
            }
        });
    });
});

// إظهار رسالة تأكيد اختيار التاريخ
function showDateSelectedMessage(input) {
    const selectedDate = new Date(input.value);
    const formattedDate = selectedDate.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    // إنشاء رسالة تأكيد
    const message = document.createElement('div');
    message.className = 'alert alert-success alert-dismissible fade show position-fixed';
    message.style.cssText = `
        top: 20px;
        left: 20px;
        z-index: 9999;
        min-width: 250px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    message.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        تم اختيار التاريخ: ${formattedDate}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(message);

    // إزالة الرسالة تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (message.parentNode) {
            message.remove();
        }
    }, 3000);
}

// طباعة اجتماع محدد باستخدام القالب الموحد
function printMeeting(meetingId) {
    console.log('🖨️ طباعة الاجتماع رقم:', meetingId);

    const meetingElement = event.target.closest('.result-item');
    if (!meetingElement) {
        console.error('❌ لم يتم العثور على عنصر الاجتماع');
        return;
    }

    // محاولة الحصول على البيانات من API أولاً
    fetchMeetingDataFromAPI(meetingId)
        .then(apiData => {
            if (apiData) {
                console.log('✅ تم الحصول على البيانات من API:', apiData);
                printMeetingReport(apiData);
            } else {
                // استخراج بيانات الاجتماع من العنصر كبديل
                const meetingData = extractMeetingDataFromElement(meetingElement);
                console.log('📄 استخراج البيانات من العنصر:', meetingData);

                // استخدام القالب الموحد إذا كان متوفراً
                if (typeof printMeetingReport === 'function') {
                    printMeetingReport(meetingData);
                } else {
                    // الطريقة القديمة كاحتياطي
                    printMeetingOldWay(meetingElement);
                }
            }
        })
        .catch(error => {
            console.warn('⚠️ فشل في الحصول على البيانات من API، استخدام البيانات المحلية:', error);
            // استخراج بيانات الاجتماع من العنصر كبديل
            const meetingData = extractMeetingDataFromElement(meetingElement);

            // استخدام القالب الموحد إذا كان متوفراً
            if (typeof printMeetingReport === 'function') {
                printMeetingReport(meetingData);
            } else {
                // الطريقة القديمة كاحتياطي
                printMeetingOldWay(meetingElement);
            }
        });
}

// محاولة الحصول على بيانات الاجتماع من API
async function fetchMeetingDataFromAPI(meetingId) {
    try {
        const response = await fetch(`/api/meeting/${meetingId}`);
        if (response.ok) {
            const data = await response.json();
            return data.meeting || data;
        }
        return null;
    } catch (error) {
        console.warn('⚠️ خطأ في API:', error);
        return null;
    }
}

// استخراج بيانات الاجتماع من العنصر المحدث لشاشة البحث
function extractMeetingDataFromElement(element) {
    const getTextContent = (selector) => {
        const el = element.querySelector(selector);
        return el ? el.textContent.trim() : '';
    };

    // استخراج البيانات من بنية HTML الخاصة بشاشة البحث
    const subject = getTextContent('.result-title strong') || 'غير محدد';
    const meetingType = element.querySelector('.badge.bg-primary')?.textContent.trim() || 'اجتماع';

    // استخراج حالة الاجتماع من الشارات
    let status = 'نشط';
    let postponeReason = '';
    let cancellationReason = '';
    let newDate = '';
    let newTime = '';

    // البحث عن شارة الحالة
    const statusBadges = element.querySelectorAll('.badge');
    statusBadges.forEach(badge => {
        const badgeText = badge.textContent.trim();
        if (badge.classList.contains('bg-warning') || badgeText.includes('مؤجل')) {
            status = 'مؤجل';
            // محاولة استخراج تاريخ التأجيل من النص
            const postponeMatch = badgeText.match(/مؤجل.*?(\d{4}-\d{2}-\d{2})/);
            if (postponeMatch) {
                newDate = postponeMatch[1];
            }
        } else if (badge.classList.contains('bg-danger') || badgeText.includes('ملغي')) {
            status = 'ملغي';
        }
    });

    // استخراج التاريخ والوقت من detail-item
    const dateText = getTextContent('.detail-item:has(.fa-calendar)') || '';
    const timeText = getTextContent('.detail-item:has(.fa-clock)') || '';
    const locationText = getTextContent('.detail-item:has(.fa-map-marker-alt)') || '';
    const invitingPartyText = getTextContent('.detail-item:has(.fa-building)') || '';
    const bookNumberText = getTextContent('.detail-item:has(.fa-book)') || '';
    const bookDateText = getTextContent('.detail-item:has(.fa-calendar-alt)') || '';
    const arrivalTimeText = getTextContent('.detail-item:has(.fa-user-clock)') || '';
    const dressCodeText = getTextContent('.detail-item:has(.fa-user-tie)') || '';
    const notesText = getTextContent('.result-notes') || '';

    // تنظيف النصوص لاستخراج القيم فقط
    const extractValue = (text, prefix) => {
        if (!text) return '';
        const parts = text.split(prefix);
        return parts.length > 1 ? parts[1].trim() : '';
    };

    // استخراج وقت الحضور
    const arrivalTime = extractValue(arrivalTimeText, 'الحضور قبل:') ||
                       extractValue(arrivalTimeText, 'قبل الموعد بـ') || '15';
    const arrivalTimeNumber = arrivalTime.replace(/[^\d]/g, '') || '15';

    return {
        subject: subject,
        meeting_type: meetingType,
        status: status,
        meeting_date: extractValue(dateText, 'التاريخ:') || '',
        meeting_time: extractValue(timeText, 'الوقت:') || '',
        location: extractValue(locationText, 'المكان:') || '',
        inviting_party: extractValue(invitingPartyText, 'جهة الدعوة:') || '',
        arrival_time_before: arrivalTimeNumber,
        book_number: extractValue(bookNumberText, 'رقم الكتاب:') || '',
        book_date: extractValue(bookDateText, 'تاريخ الكتاب:') || '',
        dress_code: extractValue(dressCodeText, 'نوع اللباس:') || 'رسمي',
        notes: extractValue(notesText, 'ملاحظات:') || '',
        // معلومات التأجيل والإلغاء
        new_date: newDate,
        new_time: newTime,
        postpone_reason: postponeReason,
        cancellation_reason: cancellationReason
    };
}

// الطريقة القديمة للطباعة (احتياطي)
function printMeetingOldWay(meetingElement) {
    const printWindow = window.open('', '_blank');

    // نسخ المحتوى وتنظيفه للطباعة
    const meetingContent = meetingElement.cloneNode(true);

    // إزالة الأزرار من المحتوى المنسوخ
    const actionsBottom = meetingContent.querySelector('.result-actions-bottom');
    if (actionsBottom) {
        actionsBottom.remove();
    }

    const printContent = meetingContent.innerHTML;

        const htmlContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>طباعة الاجتماع</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            direction: rtl;
                            line-height: 1.6;
                        }
                        .result-item {
                            border: 1px solid #ddd;
                            padding: 20px;
                            border-radius: 10px;
                        }
                        .result-header {
                            border-bottom: 2px solid #1B4332;
                            padding-bottom: 10px;
                            margin-bottom: 15px;
                        }
                        .result-title {
                            font-size: 18px;
                            font-weight: bold;
                            color: #1B4332;
                        }
                        .detail-item {
                            margin: 8px 0;
                        }
                        .result-notes {
                            background: #fff3cd;
                            padding: 10px;
                            border-radius: 5px;
                            margin: 10px 0;
                        }
                        .result-actions-bottom {
                            display: none;
                        }
                        .badge {
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                        }
                        .bg-primary {
                            background-color: #1B4332 !important;
                            color: white;
                        }
                        .bg-success {
                            background-color: #28a745 !important;
                            color: white;
                        }
                        .bg-danger {
                            background-color: #dc3545 !important;
                            color: white;
                        }
                        @media print {
                            body { margin: 0; }
                            .result-item { border: none; }
                        }
                    </style>
                </head>
                <body>
                    <h2 style="text-align: center; color: #1B4332;">تفاصيل الاجتماع</h2>
                    <div class="result-item">${printContent}</div>
                    <script>
                        window.onload = function() {
                            window.print();
                            setTimeout(function() {
                                window.close();
                            }, 1000);
                        };
                    </script>
                </body>
            </html>
        `;

<!-- تم نقل جميع وظائف JavaScript إلى ملف منفصل -->
</script>
<script src="{{ url_for('static', filename='js/search-simple.js') }}"></script>
<!-- قالب الطباعة الموحد -->
<script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>
{% endblock %}
