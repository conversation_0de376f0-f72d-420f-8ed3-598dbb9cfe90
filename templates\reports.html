{% extends "base.html" %}

{% block title %}التقارير والإحصائيات{% endblock %}

{% block extra_css %}
<style>
    /* تصميم البطاقات الجديدة */
    .chart-card, .monthly-table-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .chart-card:hover, .monthly-table-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .chart-card::before, .monthly-table-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #28a745, #007bff, #dc3545);
    }

    /* رأس البطاقات */
    .chart-header, .table-header {
        padding: 20px 25px;
        background: linear-gradient(135deg, #1b4332 0%, #2d5a3d 100%);
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chart-header h4, .table-header h4 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
    }

    .chart-controls {
        display: flex;
        gap: 8px;
    }

    .chart-controls .btn {
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        transition: all 0.3s ease;
    }

    .chart-controls .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    /* جسم البطاقات */
    .chart-body, .table-body {
        padding: 25px;
    }

    /* الرسم البياني ثلاثي الأبعاد */
    .chart-container-3d {
        position: relative;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: radial-gradient(circle at center, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.5) 100%);
        border-radius: 15px;
        margin-bottom: 20px;
        overflow: hidden;
    }

    .chart-container-3d::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, rgba(40,167,69,0.1), rgba(0,123,255,0.1), rgba(220,53,69,0.1), rgba(40,167,69,0.1));
        animation: rotate 30s linear infinite;
        pointer-events: none;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    #meetings3DChart {
        position: relative;
        z-index: 2;
        filter: drop-shadow(0 8px 16px rgba(0,0,0,0.15));
    }

    /* الأسطورة الحديثة */
    .chart-legend-modern {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin-top: 20px;
    }

    .legend-item-modern {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px;
        background: rgba(255,255,255,0.8);
        border-radius: 12px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .legend-item-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    }

    .legend-item-modern.completed {
        border-color: #28a745;
    }

    .legend-item-modern.upcoming {
        border-color: #007bff;
    }

    .legend-item-modern.cancelled {
        border-color: #dc3545;
    }

    .legend-dot {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-bottom: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .legend-item-modern.completed .legend-dot {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .legend-item-modern.upcoming .legend-dot {
        background: linear-gradient(135deg, #007bff, #6610f2);
    }

    .legend-item-modern.cancelled .legend-dot {
        background: linear-gradient(135deg, #dc3545, #e83e8c);
    }

    .legend-item-modern span {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 5px;
    }

    .legend-item-modern strong {
        font-size: 1.4rem;
        font-weight: 700;
        color: #333;
    }

    /* الجدول الشهري */
    .year-selector select {
        background: rgba(255,255,255,0.9);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 8px;
        color: white;
        padding: 5px 15px;
    }

    .monthly-stats-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table-header-row {
        display: grid;
        grid-template-columns: 2fr 1fr 2fr 1fr;
        gap: 15px;
        padding: 15px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }

    .table-rows {
        max-height: 300px;
        overflow-y: auto;
    }

    .table-row {
        display: grid;
        grid-template-columns: 2fr 1fr 2fr 1fr;
        gap: 15px;
        padding: 15px 20px;
        border-bottom: 1px solid #f1f3f4;
        transition: all 0.3s ease;
        align-items: center;
    }

    .table-row:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        transform: translateX(5px);
    }

    .table-row.high-performance {
        background: linear-gradient(135deg, rgba(40,167,69,0.1) 0%, rgba(40,167,69,0.05) 100%);
        border-left: 4px solid #28a745;
    }

    .table-row.low-performance {
        background: linear-gradient(135deg, rgba(220,53,69,0.1) 0%, rgba(220,53,69,0.05) 100%);
        border-left: 4px solid #dc3545;
    }

    .col-month {
        font-weight: 600;
        color: #333;
    }

    .count-badge {
        display: inline-block;
        background: linear-gradient(135deg, #1b4332, #2d5a3d);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .percentage-bar {
        position: relative;
        background: #e9ecef;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
    }

    .percentage-fill {
        height: 100%;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 10px;
        transition: width 0.8s ease;
        position: relative;
    }

    .percentage-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 0.8rem;
        font-weight: 600;
        color: #333;
        z-index: 2;
    }

    .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-badge.completed {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    /* تحسينات الاستجابة */
    @media (max-width: 768px) {
        .chart-legend-modern {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .legend-item-modern {
            flex-direction: row;
            justify-content: space-between;
            padding: 12px;
        }

        .legend-dot {
            margin-bottom: 0;
            margin-left: 10px;
        }

        .table-header-row, .table-row {
            grid-template-columns: 1fr;
            gap: 10px;
            text-align: center;
        }

        .chart-container-3d {
            height: 300px;
        }

        #meetings3DChart {
            width: 300px !important;
            height: 300px !important;
        }
    }

    @media (max-width: 576px) {
        .chart-body, .table-body {
            padding: 15px;
        }

        .chart-header, .table-header {
            padding: 15px 20px;
        }

        .chart-container-3d {
            height: 250px;
        }

        #meetings3DChart {
            width: 250px !important;
            height: 250px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-primary mb-1">
                <i class="fas fa-chart-bar me-2"></i>
                التقارير والإحصائيات
            </h2>
            <p class="text-muted mb-0">تقارير شاملة وإحصائيات تفصيلية للاجتماعات</p>
        </div>
    </div>

    <!-- التصميم الجديد المحسن - رسم بياني ثلاثي الأبعاد وجدول شهري -->
    <div class="row mb-4">
        <!-- الرسم البياني الدائري ثلاثي الأبعاد -->
        <div class="col-lg-6 mb-3">
            <div class="chart-card">
                <div class="chart-header">
                    <h4><i class="fas fa-chart-pie me-2"></i>الإحصائيات الشهرية</h4>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="printChart()" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="exportChartToPDF()" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-body">
                    <div class="chart-container-3d">
                        <canvas id="meetings3DChart" width="400" height="400"></canvas>
                    </div>
                    <div class="chart-legend-modern">
                        <div class="legend-item-modern completed">
                            <div class="legend-dot"></div>
                            <span>مكتملة</span>
                            <strong id="completedCount">{{ completed_meetings or 0 }}</strong>
                        </div>
                        <div class="legend-item-modern upcoming">
                            <div class="legend-dot"></div>
                            <span>قادمة</span>
                            <strong id="upcomingCount">{{ upcoming_meetings or 0 }}</strong>
                        </div>
                        <div class="legend-item-modern cancelled">
                            <div class="legend-dot"></div>
                            <span>ملغية</span>
                            <strong id="cancelledCount">{{ cancelled_meetings or 0 }}</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجدول الشهري المفصل -->
        <div class="col-lg-6 mb-3">
            <div class="monthly-table-card">
                <div class="table-header">
                    <h4><i class="fas fa-calendar-alt me-2"></i>التقرير الشهري المفصل</h4>
                    <div class="year-selector">
                        <select id="yearSelect" class="form-select form-select-sm" onchange="updateMonthlyData()">
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                        </select>
                    </div>
                </div>
                <div class="table-body">
                    <div class="monthly-stats-table">
                        <div class="table-header-row">
                            <div class="col-month">الشهر</div>
                            <div class="col-count">العدد</div>
                            <div class="col-percentage">النسبة</div>
                            <div class="col-status">الحالة</div>
                        </div>
                        <div class="table-rows" id="monthlyTableRows">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    console.log('🔥 بدء تحميل التصميم الجديد للتقارير');

    // بيانات حقيقية من قاعدة البيانات
    const meetingsData = {
        completed: {{ completed_meetings or 0 }},
        upcoming: {{ upcoming_meetings or 0 }},
        cancelled: {{ cancelled_meetings or 0 }}
    };

    // بيانات شهرية تجريبية (يجب استبدالها ببيانات حقيقية من قاعدة البيانات)
    const monthlyData = {
        2024: [
            { month: 'يناير', completed: 5, upcoming: 2, cancelled: 1 },
            { month: 'فبراير', completed: 8, upcoming: 3, cancelled: 0 },
            { month: 'مارس', completed: 12, upcoming: 4, cancelled: 2 },
            { month: 'أبريل', completed: 7, upcoming: 5, cancelled: 1 },
            { month: 'مايو', completed: 15, upcoming: 6, cancelled: 3 },
            { month: 'يونيو', completed: 10, upcoming: 2, cancelled: 1 },
            { month: 'يوليو', completed: 9, upcoming: 4, cancelled: 2 },
            { month: 'أغسطس', completed: 11, upcoming: 3, cancelled: 1 },
            { month: 'سبتمبر', completed: 13, upcoming: 5, cancelled: 2 },
            { month: 'أكتوبر', completed: 8, upcoming: 7, cancelled: 1 },
            { month: 'نوفمبر', completed: 6, upcoming: 3, cancelled: 0 },
            { month: 'ديسمبر', completed: 4, upcoming: 2, cancelled: 1 }
        ]
    };

    // دالة لتفتيح اللون
    function lightenColor(color, amount) {
        const usePound = color[0] === '#';
        const col = usePound ? color.slice(1) : color;
        const num = parseInt(col, 16);
        let r = (num >> 16) + amount * 255;
        let g = (num >> 8 & 0x00FF) + amount * 255;
        let b = (num & 0x0000FF) + amount * 255;
        r = r > 255 ? 255 : r < 0 ? 0 : r;
        g = g > 255 ? 255 : g < 0 ? 0 : g;
        b = b > 255 ? 255 : b < 0 ? 0 : b;
        return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    }

    // رسم الرسم البياني ثلاثي الأبعاد
    function draw3DChart() {
        const canvas = document.getElementById('meetings3DChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const centerX = 200;
        const centerY = 180;
        const radius = 120;
        const depth = 25;

        // مسح الكانفاس
        ctx.clearRect(0, 0, 400, 400);

        // حساب الإجمالي
        const total = meetingsData.completed + meetingsData.upcoming + meetingsData.cancelled;

        if (total === 0) {
            // رسم دائرة فارغة
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fillStyle = '#f8f9fa';
            ctx.fill();
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 3;
            ctx.stroke();

            ctx.fillStyle = '#6c757d';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('لا توجد بيانات', centerX, centerY);
            return;
        }

        // بيانات الشرائح
        const slices = [
            { value: meetingsData.completed, color: '#28a745', darkColor: '#1e7e34', label: 'مكتملة' },
            { value: meetingsData.upcoming, color: '#007bff', darkColor: '#0056b3', label: 'قادمة' },
            { value: meetingsData.cancelled, color: '#dc3545', darkColor: '#a71e2a', label: 'ملغية' }
        ].filter(slice => slice.value > 0);

        let currentAngle = -Math.PI / 2;

        // رسم الظلال (الجانب السفلي)
        slices.forEach(slice => {
            const sliceAngle = (slice.value / total) * 2 * Math.PI;

            // رسم الجانب السفلي
            ctx.beginPath();
            ctx.moveTo(centerX, centerY + depth);
            ctx.arc(centerX, centerY + depth, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fillStyle = slice.darkColor;
            ctx.fill();

            currentAngle += sliceAngle;
        });

        // إعادة تعيين الزاوية للرسم العلوي
        currentAngle = -Math.PI / 2;

        // رسم الشرائح العلوية مع التدرج
        slices.forEach(slice => {
            const sliceAngle = (slice.value / total) * 2 * Math.PI;
            const percent = Math.round((slice.value / total) * 100);

            // رسم الشريحة العلوية
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();

            // تدرج لوني للتأثير ثلاثي الأبعاد
            const gradient = ctx.createRadialGradient(
                centerX - radius * 0.3, centerY - radius * 0.3, 0,
                centerX, centerY, radius
            );
            gradient.addColorStop(0, lightenColor(slice.color, 0.4));
            gradient.addColorStop(0.7, slice.color);
            gradient.addColorStop(1, slice.darkColor);

            ctx.fillStyle = gradient;
            ctx.fill();

            // رسم الحدود
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 4;
            ctx.stroke();

            // رسم النسبة المئوية داخل الشريحة
            if (sliceAngle > 0.3) {
                const textAngle = currentAngle + sliceAngle / 2;
                const textX = centerX + Math.cos(textAngle) * (radius * 0.7);
                const textY = centerY + Math.sin(textAngle) * (radius * 0.7);

                // ظل النص
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(percent + '%', textX + 1, textY + 1);

                // النص الأساسي
                ctx.fillStyle = '#fff';
                ctx.fillText(percent + '%', textX, textY);

                // رسم العدد تحت النسبة
                ctx.font = 'bold 12px Arial';
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.fillText('(' + slice.value + ')', textX + 1, textY + 21);
                ctx.fillStyle = '#fff';
                ctx.fillText('(' + slice.value + ')', textX, textY + 20);
            }

            currentAngle += sliceAngle;
        });

        // رسم دائرة في المنتصف مع تأثير ثلاثي الأبعاد
        const centerRadius = 45;

        // ظل الدائرة المركزية
        ctx.beginPath();
        ctx.arc(centerX, centerY + depth, centerRadius, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fill();

        // الدائرة المركزية العلوية
        ctx.beginPath();
        ctx.arc(centerX, centerY, centerRadius, 0, 2 * Math.PI);

        const centerGradient = ctx.createRadialGradient(
            centerX - centerRadius * 0.4, centerY - centerRadius * 0.4, 0,
            centerX, centerY, centerRadius
        );
        centerGradient.addColorStop(0, '#ffffff');
        centerGradient.addColorStop(0.3, '#f8f9fa');
        centerGradient.addColorStop(0.7, '#e9ecef');
        centerGradient.addColorStop(1, '#dee2e6');

        ctx.fillStyle = centerGradient;
        ctx.fill();
        ctx.strokeStyle = '#adb5bd';
        ctx.lineWidth = 3;
        ctx.stroke();

        // نص الإجمالي مع ظل
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.font = 'bold 28px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(total, centerX + 1, centerY - 7);

        ctx.fillStyle = '#333';
        ctx.fillText(total, centerX, centerY - 8);

        ctx.font = 'bold 14px Arial';
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillText('إجمالي', centerX + 1, centerY + 16);
        ctx.fillStyle = '#666';
        ctx.fillText('إجمالي', centerX, centerY + 15);

        console.log('✅ تم رسم الرسم البياني ثلاثي الأبعاد بنجاح');
    }

    // ملء الجدول الشهري
    function updateMonthlyData() {
        const year = document.getElementById('yearSelect').value;
        const tableRows = document.getElementById('monthlyTableRows');
        const data = monthlyData[year] || [];

        let html = '';
        let totalCompleted = 0;

        data.forEach(monthData => {
            const total = monthData.completed + monthData.upcoming + monthData.cancelled;
            const percentage = total > 0 ? Math.round((monthData.completed / total) * 100) : 0;
            totalCompleted += monthData.completed;

            // تحديد حالة الشهر (مكتمل إذا انتهى)
            const currentMonth = new Date().getMonth();
            const monthIndex = data.indexOf(monthData);
            const isCompleted = monthIndex <= currentMonth;

            if (isCompleted) { // فقط الأشهر المنتهية
                html += `
                    <div class="table-row ${monthData.completed > 10 ? 'high-performance' : monthData.completed < 5 ? 'low-performance' : ''}">
                        <div class="col-month">
                            <i class="fas fa-calendar-check me-2"></i>
                            ${monthData.month}
                        </div>
                        <div class="col-count">
                            <span class="count-badge">${monthData.completed}</span>
                        </div>
                        <div class="col-percentage">
                            <div class="percentage-bar">
                                <div class="percentage-fill" style="width: ${percentage}%"></div>
                                <span class="percentage-text">${percentage}%</span>
                            </div>
                        </div>
                        <div class="col-status">
                            <span class="status-badge completed">مكتمل</span>
                        </div>
                    </div>
                `;
            }
        });

        tableRows.innerHTML = html;
        console.log('✅ تم تحديث الجدول الشهري');
    }

    // دوال الطباعة والتصدير الجديدة - فقط عند النقر على الأزرار
    function printChart() {
        console.log('🖨️ طباعة الرسم البياني ثلاثي الأبعاد...');

        const canvas = document.getElementById('meetings3DChart');
        if (!canvas) {
            alert('❌ لم يتم العثور على الرسم البياني');
            return;
        }

        const chartImage = canvas.toDataURL('image/png');
        const printWindow = window.open('', '_blank');

        const printHTML = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة التقرير الشهري</title>
                <style>
                    @page { size: A4; margin: 2cm; }
                    body { font-family: Arial, sans-serif; direction: rtl; text-align: center; padding: 20px; margin: 0; background: white; }
                    .header { background: linear-gradient(135deg, #1b4332, #2d5a3d); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; }
                    .chart-container { margin: 30px 0; padding: 20px; border: 2px solid #dee2e6; border-radius: 15px; background: #f8f9fa; }
                    .chart-image { max-width: 100%; height: auto; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
                    .footer { margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>📊 التقرير الشهري المفصل</h2>
                    <p>الرسم البياني ثلاثي الأبعاد</p>
                </div>
                <div class="chart-container">
                    <h3>📈 الإحصائيات الشهرية</h3>
                    <img src="${chartImage}" alt="الرسم البياني" class="chart-image">
                </div>
                <div class="footer">
                    <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>نظام إدارة الاجتماعات - التقرير الشهري المفصل</p>
                </div>
            </body>
            </html>
        `;

        printWindow.document.write(printHTML);
        printWindow.document.close();

        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    }

    function exportChartToPDF() {
        console.log('📄 تصدير PDF للتقرير الشهري...');

        const canvas = document.getElementById('meetings3DChart');
        if (!canvas) {
            alert('❌ لم يتم العثور على الرسم البياني');
            return;
        }

        const chartImage = canvas.toDataURL('image/png');
        const pdfWindow = window.open('', '_blank');

        const pdfHTML = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>التقرير الشهري المفصل - PDF</title>
                <style>
                    @page { size: A4; margin: 1.5cm; }
                    body { font-family: Arial, sans-serif; direction: rtl; margin: 0; padding: 20px; background: #fff; }
                    .pdf-header { background: linear-gradient(135deg, #1b4332, #2d5a3d); color: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; text-align: center; }
                    .chart-section { background: #f8f9fa; padding: 20px; border-radius: 12px; margin: 20px 0; text-align: center; }
                    .chart-image { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin: 15px 0; }
                    .pdf-footer { margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="pdf-header">
                    <h2>📊 التقرير الشهري المفصل - PDF</h2>
                    <p>الإحصائيات ثلاثية الأبعاد والتحليل الشهري</p>
                </div>
                <div class="chart-section">
                    <h3>📈 الرسم البياني ثلاثي الأبعاد</h3>
                    <img src="${chartImage}" alt="الرسم البياني" class="chart-image">
                </div>
                <div class="pdf-footer">
                    <p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                    <p>نظام إدارة الاجتماعات - التقرير الشهري المفصل</p>
                </div>
            </body>
            </html>
        `;

        pdfWindow.document.write(pdfHTML);
        pdfWindow.document.close();

        setTimeout(() => {
            pdfWindow.print();
        }, 1000);
    }

    // تشغيل التطبيق عند تحميل الصفحة - بدون طباعة تلقائية
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 بدء تحميل التقرير الشهري المفصل');

        // تأخير قصير للتأكد من تحميل العناصر
        setTimeout(() => {
            draw3DChart();
            updateMonthlyData();
            console.log('✅ تم تحميل التقرير بنجاح - بدون طباعة تلقائية');
        }, 500);
    });
    </script>
</div>
{% endblock %}
