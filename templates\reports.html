{% extends "base.html" %}

{% block title %}التقارير والإحصائيات - نظام اجتماعات المدير{% endblock %}

{% block extra_css %}

<style>
    .reports-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }
    
    .section-header {
        background: var(--jaf-gradient);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
    }
    
    .section-body {
        padding: 2rem;
    }
    
    .export-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        min-height: 350px;
    }

    .export-card:hover {
        border-color: var(--jaf-primary);
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .export-icon {
        width: 80px;
        height: 80px;
        background: var(--jaf-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
    }
    
    .export-title {
        font-weight: 600;
        color: var(--jaf-primary);
        margin-bottom: 1rem;
        font-size: 1.2rem;
    }
    
    .export-description {
        color: #6c757d;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
    }
    
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 2rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    /* البطاقات الاحترافية */
    .professional-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .professional-stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .professional-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .card-background-pattern {
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, rgba(27, 67, 50, 0.05) 0%, transparent 70%);
        border-radius: 50%;
    }

    .card-header-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .stat-icon-container {
        position: relative;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        position: relative;
        z-index: 2;
    }

    .stat-icon.primary-icon {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
    }

    .stat-icon.success-icon {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .stat-icon.info-icon {
        background: linear-gradient(135deg, #007bff 0%, #17a2b8 100%);
    }

    .stat-icon.warning-icon {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    }

    .stat-icon.secondary-icon {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    }

    .stat-icon.champion-icon {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    }

    .icon-glow {
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: inherit;
        border-radius: 15px;
        opacity: 0.3;
        z-index: 1;
        animation: pulse 2s infinite;
    }

    .stat-badge {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        border: 1px solid #dee2e6;
    }

    .stat-badge.upcoming-badge {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border-color: #c3e6cb;
    }

    .stat-badge.completed-badge {
        background: linear-gradient(135deg, #cce5ff 0%, #b3d7ff 100%);
        color: #004085;
        border-color: #b3d7ff;
    }

    .stat-badge.cancelled-badge {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-color: #f5c6cb;
    }

    .stat-badge.average-badge {
        background: linear-gradient(135deg, #e2e3f1 0%, #d1ecf1 100%);
        color: #0c5460;
        border-color: #d1ecf1;
    }

    .stat-badge.champion-badge {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
        border-color: #ffeaa7;
    }

    .card-content-section {
        flex: 1;
        margin: 1rem 0;
    }

    .stat-number-container {
        position: relative;
        margin-bottom: 0.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1B4332;
        line-height: 1;
        font-variant-numeric: lining-nums;
        font-family: 'Segoe UI', 'Cairo', sans-serif;
        direction: ltr;
        display: inline-block;
    }

    .stat-unit {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
        margin-right: 0.5rem;
    }

    .champion-crown {
        font-size: 1.5rem;
        margin-right: 0.5rem;
        animation: bounce 2s infinite;
    }

    .stat-label {
        font-size: 1rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .stat-sublabel {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .progress-indicator {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .progress-bar {
        height: 100%;
        border-radius: 2px;
        transition: width 0.6s ease;
    }

    .progress-bar.total-progress {
        background: linear-gradient(90deg, #1B4332, #2D5A3D);
    }

    .progress-bar.upcoming-progress {
        background: linear-gradient(90deg, #28a745, #20c997);
    }

    .progress-bar.completed-progress {
        background: linear-gradient(90deg, #007bff, #17a2b8);
    }

    .progress-bar.cancelled-progress {
        background: linear-gradient(90deg, #dc3545, #fd7e14);
    }

    .progress-bar.average-progress {
        background: linear-gradient(90deg, #6f42c1, #e83e8c);
    }

    .progress-bar.champion-progress {
        background: linear-gradient(90deg, #ffc107, #fd7e14);
    }

    .stat-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .trend-text {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .trend-text.champion-trend {
        color: #856404;
        font-weight: 600;
    }

    .card-action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .action-btn.print-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .action-btn.pdf-btn {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
    }

    .action-btn:hover {
        transform: scale(1.1);
    }

    /* تحسين عرض البطاقات للشاشات المختلفة */
    @media (min-width: 1400px) {
        .professional-stats-grid {
            grid-template-columns: repeat(6, 1fr);
        }
    }

    @media (max-width: 1399px) and (min-width: 1200px) {
        .professional-stats-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 1199px) and (min-width: 992px) {
        .professional-stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 991px) {
        .professional-stats-grid {
            grid-template-columns: 1fr;
        }

        .professional-stat-card {
            min-height: 150px;
            padding: 1.5rem;
        }

        .stat-number {
            font-size: 2rem;
        }
    }

    /* تأثيرات الحركة */
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }
    
    .stat-card {
        background: linear-gradient(135deg, rgba(27, 67, 50, 0.1) 0%, rgba(45, 106, 79, 0.1) 100%);
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--jaf-gradient);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--jaf-primary);
        margin-bottom: 0.5rem;
    }

    /* نمط خاص للنصوص الطويلة في البطاقات الإحصائية */
    .stat-number.long-text {
        font-size: 1.4rem;
        line-height: 1.3;
        text-align: center;
    }
    
    .stat-label {
        color: var(--jaf-dark);
        font-weight: 600;
        font-size: 1rem;
    }
    
    .stat-change {
        font-size: 1.1rem !important; /* حجم أكبر */
        font-weight: 700 !important; /* غامق جداً */
        margin-top: 0.5rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .stat-increase {
        color: #B8860B !important; /* ذهبي غامق */
    }

    .stat-decrease {
        color: #B8860B !important; /* ذهبي غامق */
    }
    
    .filter-section {
        background: rgba(27, 67, 50, 0.05);
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .chart-tabs {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        border: 2px solid #dee2e6;
        justify-content: center;
        align-items: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    }

    .chart-tab {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px solid #dee2e6;
        padding: 1rem 1.75rem;
        color: #495057;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        font-size: 0.95rem;
        min-width: 160px;
        min-height: 55px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        line-height: 1.3;
    }

    .chart-tab::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s;
    }

    .chart-tab:hover::before {
        left: 100%;
    }

    .chart-tab.active {
        color: white;
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        border-color: #1B4332;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(27, 67, 50, 0.3);
    }

    .chart-tab:hover {
        color: #1B4332;
        border-color: #1B4332;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(27, 67, 50, 0.2);
    }

    .chart-tab.active:hover {
        color: white;
        transform: translateY(-2px);
    }

    .chart-tab i {
        margin-left: 0.75rem;
        font-size: 1.1rem;
        flex-shrink: 0;
        display: inline-block;
        width: 20px;
        text-align: center;
    }

    .chart-tab span {
        flex: 1;
        text-align: center;
        margin-right: 0.5rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        vertical-align: middle;
    }

    /* ضمان عدم تداخل النص مع الأيقونة */
    .chart-tab {
        gap: 0.5rem;
    }

    .chart-tab:not(:has(span)) {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* تحسين الاستجابة للشاشات الصغيرة */
    @media (max-width: 768px) {
        .chart-tabs {
            flex-direction: column;
            gap: 0.75rem;
            padding: 1rem;
        }

        .chart-tab {
            min-width: 100%;
            width: 100%;
            padding: 1rem 1.5rem;
            font-size: 0.9rem;
            min-height: 50px;
            justify-content: flex-start;
            text-align: right;
        }

        .chart-tab i {
            margin-left: 1rem;
            margin-right: 0;
            font-size: 1.2rem;
            width: 25px;
        }

        .chart-tab span {
            margin-right: 0;
            text-align: right;
        }
    }

    @media (max-width: 576px) {
        .chart-tabs {
            gap: 0.5rem;
            padding: 0.75rem;
        }

        .chart-tab {
            padding: 0.875rem 1.25rem;
            font-size: 0.85rem;
            min-height: 45px;
        }

        .chart-tab i {
            font-size: 1.1rem;
            margin-left: 0.75rem;
            width: 22px;
        }
    }

    /* تحسينات إضافية للرسوم البيانية */
    .chart-container {
        transition: all 0.3s ease;
    }

    .chart-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
    }

    /* تأثيرات الأزرار */
    .btn-sm {
        transition: all 0.3s ease;
    }

    .btn-sm:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .btn-sm:active {
        transform: translateY(0);
    }

    /* تحسين مظهر Canvas */
    #mainChart {
        border-radius: 10px;
        background: rgba(248, 249, 250, 0.5);
    }

    /* تأثيرات التحميل */
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    #chartLoadingIndicator {
        animation: pulse 2s infinite;
    }

    /* تحسين العناوين */
    .section-header {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px 12px 0 0;
        margin-bottom: 0;
        font-weight: 600;
        text-align: center;
        box-shadow: 0 4px 15px rgba(27, 67, 50, 0.2);
    }

    .section-body {
        background: white;
        border-radius: 0 0 12px 12px;
        padding: 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        border: 2px solid #e9ecef;
        border-top: none;
    }
    
    .export-card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .export-card-actions {
        margin-top: auto;
        padding-top: 1rem;
    }

    .print-options {
        display: flex;
        gap: 0.75rem;
        justify-content: center;
        margin-top: 1rem;
        flex-wrap: wrap;
    }

    .btn-export {
        background: var(--jaf-gradient);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
        width: 100%;
        max-width: 200px;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(27, 67, 50, 0.4);
        color: white;
    }

    .btn-print {
        background: var(--jaf-secondary);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        min-width: 100px;
        flex: 1;
        max-width: 120px;
    }

    .btn-print:hover {
        background: var(--jaf-primary);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(27, 67, 50, 0.3);
    }

    /* إصلاح الأزرار في منطقة الرسوم البيانية */
    .chart-controls {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 1.5rem;
        padding: 1rem;
        background: rgba(248, 249, 250, 0.5);
        border-radius: 10px;
    }

    .chart-controls .btn-print {
        margin: 0.25rem;
        min-width: 150px;
    }

    /* تحسينات للأجهزة المحمولة */
    @media (max-width: 768px) {
        .export-card {
            margin-bottom: 1.5rem;
            min-height: 300px;
        }

        .print-options {
            flex-direction: column;
            gap: 0.5rem;
        }

        .btn-print {
            max-width: none;
            width: 100%;
        }

        .chart-controls {
            flex-direction: column;
            gap: 0.75rem;
        }

        .chart-controls .btn-print {
            min-width: auto;
            width: 100%;
            max-width: 250px;
            margin: 0 auto;
        }

        .export-card-content {
            margin-bottom: 1rem;
        }

        .btn-export {
            max-width: none;
            width: 100%;
        }
    }

    @media (max-width: 576px) {
        .export-card {
            min-height: 280px;
            padding: 1.5rem;
        }

        .export-title {
            font-size: 1.1rem;
        }

        .export-description {
            font-size: 0.9rem;
        }
    }

    /* تصميم زر عصري واحترافي مبسط */
    .filter-section {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        padding: 2rem;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
        border: 1px solid rgba(27, 67, 50, 0.1);
        position: relative;
        overflow: hidden;
    }

    .filter-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #1B4332, #2D5A3D, #40916C);
        border-radius: 20px 20px 0 0;
    }

    .filter-section .form-floating {
        position: relative;
        margin-bottom: 0;
    }

    .modern-select-wrapper {
        position: relative;
        display: inline-block;
        width: 100%;
        max-width: 300px;
    }

    .modern-select-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #1B4332;
        margin-bottom: 0.5rem;
        text-align: right;
        letter-spacing: 0.025em;
    }

    .modern-select {
        width: 100%;
        height: 56px;
        padding: 0 3.5rem 0 1.5rem;
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        font-size: 1rem;
        font-weight: 500;
        color: #334155;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        appearance: none;
        direction: rtl;
        text-align: right;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }

    .modern-select:hover {
        border-color: #1B4332;
        box-shadow: 0 4px 12px rgba(27, 67, 50, 0.15);
        transform: translateY(-1px);
    }

    .modern-select:focus {
        outline: none;
        border-color: #1B4332;
        box-shadow: 0 0 0 4px rgba(27, 67, 50, 0.1), 0 4px 12px rgba(27, 67, 50, 0.15);
        transform: translateY(-1px);
    }

    .modern-select-icon {
        position: absolute;
        left: 1.25rem;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 20px;
        pointer-events: none;
        transition: all 0.3s ease;
        z-index: 2;
    }

    .modern-select-icon svg {
        width: 100%;
        height: 100%;
        fill: #64748b;
        transition: all 0.3s ease;
    }

    .modern-select:hover + .modern-select-icon svg,
    .modern-select:focus + .modern-select-icon svg {
        fill: #1B4332;
        transform: rotate(180deg);
    }

    .modern-select option {
        padding: 1rem;
        font-size: 1rem;
        color: #334155;
        background: white;
        border: none;
    }

    .modern-select option:hover,
    .modern-select option:checked {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        color: white;
    }

    /* تحسينات للأجهزة المحمولة - التصميم العصري */
    @media (max-width: 768px) {
        .filter-section {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .modern-select-wrapper {
            max-width: 100%;
        }

        .modern-select {
            height: 52px;
            padding: 0 3rem 0 1.25rem;
            font-size: 0.95rem;
            border-radius: 14px;
        }

        .modern-select-label {
            font-size: 0.8rem;
        }

        .modern-select-icon {
            left: 1rem;
            width: 18px;
            height: 18px;
        }
    }

    @media (max-width: 576px) {
        .filter-section {
            padding: 1.25rem;
            border-radius: 14px;
        }

        .modern-select {
            height: 48px;
            padding: 0 2.75rem 0 1rem;
            font-size: 0.9rem;
            border-radius: 12px;
        }

        .modern-select-label {
            font-size: 0.75rem;
            margin-bottom: 0.4rem;
        }

        .modern-select-icon {
            left: 0.875rem;
            width: 16px;
            height: 16px;
        }
    }

    /* تأثيرات إضافية للتصميم العصري */
    @keyframes selectPulse {
        0% { box-shadow: 0 0 0 0 rgba(27, 67, 50, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(27, 67, 50, 0); }
        100% { box-shadow: 0 0 0 0 rgba(27, 67, 50, 0); }
    }

    .modern-select:focus {
        animation: selectPulse 1.5s infinite;
    }

    .filter-section:hover::before {
        background: linear-gradient(90deg, #2D5A3D, #40916C, #52B788);
    }

    /* ضمان الأرقام الإنجليزية في التقارير */
    .stats-number, .metric-value, .count-display, .total-display,
    .percentage-display, .chart-value, .report-number {
        font-variant-numeric: lining-nums !important;
        font-family: 'Segoe UI', 'Cairo', sans-serif !important;
        direction: ltr !important;
        display: inline-block !important;
    }

    /* تحسين حقول التاريخ في التقارير مع Date Picker */
    input[type="date"] {
        direction: ltr !important;
        text-align: left !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        unicode-bidi: bidi-override !important;
        padding: 12px 16px !important;
        border: 2px solid #e0e0e0 !important;
        border-radius: 8px !important;
        background-color: #ffffff !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
        min-height: 50px !important;
    }

    /* تحسين مظهر Date Picker عند التركيز */
    input[type="date"]:focus {
        border-color: #1B4332 !important;
        box-shadow: 0 0 0 3px rgba(27, 67, 50, 0.1) !important;
        outline: none !important;
    }

    /* تحسين مظهر Date Picker عند التمرير */
    input[type="date"]:hover {
        border-color: #2D5A3D !important;
        background-color: #f8f9fa !important;
    }

    /* تحسين أيقونة التقويم في التقارير */
    input[type="date"]::-webkit-calendar-picker-indicator {
        opacity: 1 !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%231B4332' d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e") !important;
        background-size: 20px 20px !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        width: 30px !important;
        height: 30px !important;
        cursor: pointer !important;
        margin-left: 8px !important;
        border-radius: 4px !important;
        transition: all 0.2s ease !important;
    }

    /* تأثير عند التمرير على أيقونة التقويم */
    input[type="date"]::-webkit-calendar-picker-indicator:hover {
        background-color: rgba(27, 67, 50, 0.1) !important;
        transform: translateY(-50%) scale(1.1) !important;
    }

    /* إصلاح خاص لصفحة التقارير */
    .form-floating input[type="date"] {
        position: relative !important;
        padding-right: 40px !important;
    }

    .form-floating input[type="date"] + label {
        position: absolute !important;
        top: 0 !important;
        right: 16px !important;
        transform: translateY(0) scale(0.85) !important;
        transform-origin: right top !important;
        background: white !important;
        padding: 0 8px !important;
        z-index: 15 !important;
        color: #1B4332 !important;
    }

    /* إصلاح تسميات التاريخ */
    .form-floating label {
        direction: rtl !important;
        text-align: right !important;
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        white-space: nowrap !important;
        overflow: visible !important;
        word-break: keep-all !important;
        word-wrap: normal !important;
        hyphens: none !important;
    }


    .modern-date-picker-reports {
        display: flex;
        align-items: center;
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 50px;
        margin-top: 8px;
    }

    .modern-date-picker-reports:hover {
        border-color: #ffc107;
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.15);
    }

    .modern-date-picker-reports:focus-within {
        border-color: #ffc107;
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
    }

    .date-icon-box-reports {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .date-icon-box-reports::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .date-icon-box-reports:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
    }

    .date-icon-box-reports:hover::before {
        opacity: 1;
    }

    .date-icon-box-reports:active {
        transform: scale(0.95);
    }

    .date-input-container-reports {
        flex: 1;
        position: relative;
        height: 100%;
        display: flex;
        align-items: center;
    }

    .modern-date-input-reports {
        width: 100%;
        height: 100%;
        border: none;
        outline: none;
        background: transparent;
        padding: 0 1rem;
        font-size: 1rem;
        color: #495057;
        direction: ltr;
    }

    .modern-date-input-reports::-webkit-calendar-picker-indicator {
        display: none;
    }

    .date-placeholder-reports {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 1rem;
        font-weight: 500;
        pointer-events: none;
        transition: all 0.3s ease;
        direction: rtl;
        background: linear-gradient(90deg, #6c757d 0%, #495057 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .modern-date-input-reports:focus + .date-placeholder-reports,
    .modern-date-input-reports:valid + .date-placeholder-reports {
        opacity: 0;
        transform: translateY(-50%) scale(0.8);
    }

    .modern-date-input-reports:valid {
        color: #1B4332;
        font-weight: 600;
    }

    /* تأثيرات إضافية للتفاعل في التقارير */
    .modern-date-picker-reports.focused {
        border-color: #ffc107;
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
    }

    .modern-date-picker-reports.focused .date-icon-box-reports {
        background: linear-gradient(135deg, #ffb300 0%, #ff8f00 100%);
        transform: scale(1.02);
    }

    .modern-date-picker-reports.has-value .date-icon-box-reports {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }



    .modern-date-picker-reports.has-value .date-icon-box-reports i {
        animation: checkPulse 0.6s ease-out;
    }

    @keyframes checkPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    /* تحسينات للأجهزة المحمولة في التقارير */
    @media (max-width: 768px) {
        .modern-date-picker-reports {
            height: 45px;
        }

        .date-icon-box-reports {
            width: 45px;
            height: 45px;
            font-size: 1.1rem;
        }

        .date-placeholder-reports {
            font-size: 0.9rem;
        }
    }

    /* Quick Statistics Cards - مثل الصور */
    .quick-stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 140px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .quick-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .quick-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
    }

    .stat-gray {
        --card-color: #6c757d;
        --card-color-light: #8a9199;
    }

    .stat-green {
        --card-color: #28a745;
        --card-color-light: #34ce57;
    }

    .stat-blue {
        --card-color: #007bff;
        --card-color-light: #1a88ff;
    }

    .stat-red {
        --card-color: #dc3545;
        --card-color-light: #e55a6a;
    }

    .quick-stat-card .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--card-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        flex-shrink: 0;
    }

    .quick-stat-card .stat-content {
        flex: 1;
        margin: 0 1rem;
        text-align: center;
    }

    .quick-stat-card .stat-content h3 {
        font-size: 2rem;
        font-weight: 700;
        color: var(--card-color);
        margin: 0;
        line-height: 1;
    }

    .quick-stat-card .stat-content p {
        font-size: 0.9rem;
        font-weight: 600;
        color: #333;
        margin: 0.25rem 0;
    }

    .quick-stat-card .stat-content small {
        font-size: 0.75rem;
        color: #6c757d;
        margin: 0;
    }

    .quick-stat-card .stat-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background: var(--card-color);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    /* Progress Tables - مثل الصور */
    .progress-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .progress-table-header {
        background: var(--jaf-gradient);
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .progress-table-body {
        padding: 1.5rem;
    }

    .progress-row {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .progress-row:last-child {
        border-bottom: none;
    }

    .progress-label {
        flex: 0 0 120px;
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
    }

    .progress-bar-container {
        flex: 1;
        margin: 0 1rem;
        height: 8px;
        background: #f0f0f0;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }

    .progress-bar-fill {
        height: 100%;
        border-radius: 4px;
        transition: width 0.8s ease;
        position: relative;
    }

    .progress-bar-fill.red { background: linear-gradient(90deg, #dc3545, #e55a6a); }
    .progress-bar-fill.orange { background: linear-gradient(90deg, #fd7e14, #ff8c42); }
    .progress-bar-fill.blue { background: linear-gradient(90deg, #007bff, #1a88ff); }
    .progress-bar-fill.green { background: linear-gradient(90deg, #28a745, #34ce57); }

    .progress-percentage {
        flex: 0 0 60px;
        text-align: left;
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
    }

    .progress-count {
        flex: 0 0 40px;
        text-align: center;
        background: #f8f9fa;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
        color: #495057;
    }

    /* Chart Styles - ثلاثي الأبعاد */
    .chart-container {
        padding: 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow:
            inset 0 2px 4px rgba(0, 0, 0, 0.1),
            0 4px 15px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .chart-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
        pointer-events: none;
    }

    #meetingsChart {
        filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
        transition: transform 0.3s ease;
    }

    #meetingsChart:hover {
        transform: scale(1.02);
    }

    .chart-legend {
        padding: 0.5rem 0;
    }

    .legend-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f0f0f0;
        transition: background 0.2s ease;
    }

    .legend-item:hover {
        background: rgba(0, 123, 255, 0.05);
        border-radius: 8px;
        margin: 0 -0.5rem;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .legend-item:last-child {
        border-bottom: none;
    }

    .legend-item.total {
        border-top: 2px solid #e9ecef;
        margin-top: 0.5rem;
        padding-top: 1rem;
        font-weight: 600;
    }

    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-left: 0.75rem;
        flex-shrink: 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .legend-label {
        flex: 1;
        font-size: 0.9rem;
        color: #333;
        margin: 0 0.5rem;
    }

    .legend-value {
        background: #f8f9fa;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        min-width: 30px;
        text-align: center;
    }

    .legend-item.total .legend-value {
        background: var(--jaf-primary);
        color: white;
    }

    /* Responsive Chart */
    @media (max-width: 768px) {
        .chart-container {
            height: 350px !important;
            padding: 1.5rem;
        }

        #meetingsChart {
            width: 320px !important;
            height: 320px !important;
        }

        .legend-item {
            padding: 0.5rem 0;
        }

        .legend-color {
            width: 14px;
            height: 14px;
        }
    }

    @media (max-width: 576px) {
        .chart-container {
            height: 300px !important;
            padding: 1rem;
        }

        #meetingsChart {
            width: 280px !important;
            height: 280px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- مؤشر التحميل -->
<div id="loadingIndicator" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; background: rgba(255,255,255,0.9); padding: 20px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.2);">
    <div style="text-align: center;">
        <i class="fas fa-spinner fa-spin fa-2x text-primary mb-2"></i>
        <div>جاري التحميل...</div>
    </div>
</div>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-primary mb-1">
                <i class="fas fa-chart-bar me-2"></i>
                التقارير والإحصائيات
            </h2>
            <p class="text-muted mb-0">تقارير شاملة وإحصائيات تفصيلية للاجتماعات</p>
        </div>
        <div>
            <button type="button" class="btn btn-outline-secondary me-2" onclick="refreshReports()">
                <i class="fas fa-sync-alt me-1"></i>
                تحديث البيانات
            </button>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#pdfExportModal">
                <i class="fas fa-file-pdf me-1"></i>
                تصدير PDF
            </button>
        </div>
    </div>



    <!-- البطاقات الاحترافية في الأعلى -->
    <div class="professional-stats-grid mb-4">
        <!-- إجمالي الاجتماعات -->
        <div class="professional-stat-card total-meetings-card">
            <div class="card-background-pattern"></div>
            <div class="card-header-section">
                <div class="stat-icon-container">
                    <div class="stat-icon primary-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="icon-glow"></div>
                </div>
                <div class="stat-badge">
                    <span>إجمالي</span>
                </div>
            </div>
            <div class="card-content-section">
                <div class="stat-number-container">
                    <div class="stat-number" id="totalMeetings">{{ total_meetings or 0 }}</div>
                    <div class="stat-unit">اجتماع</div>
                </div>
                <div class="stat-label">إجمالي الاجتماعات</div>
                <div class="progress-indicator">
                    <div class="progress-bar total-progress" style="width: 100%"></div>
                </div>
                <div class="stat-meta">
                    <span class="trend-text">
                        <i class="fas fa-calendar"></i>
                        جميع الفترات
                    </span>
                    <div class="card-action-buttons">
                        <button class="action-btn print-btn" onclick="printStatCard('total-meetings')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn pdf-btn" onclick="exportStatToPDF('total-meetings')" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- الاجتماعات القادمة -->
        <div class="professional-stat-card upcoming-meetings-card">
            <div class="card-background-pattern"></div>
            <div class="card-header-section">
                <div class="stat-icon-container">
                    <div class="stat-icon success-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="icon-glow"></div>
                </div>
                <div class="stat-badge upcoming-badge">
                    <span>قادمة</span>
                </div>
            </div>
            <div class="card-content-section">
                <div class="stat-number-container">
                    <div class="stat-number" id="upcomingMeetings">{{ upcoming_meetings or 0 }}</div>
                    <div class="stat-unit">اجتماع</div>
                </div>
                <div class="stat-label">الاجتماعات القادمة</div>
                <div class="progress-indicator">
                    <div class="progress-bar upcoming-progress" style="width: 75%"></div>
                </div>
                <div class="stat-meta">
                    <span class="trend-text">
                        <i class="fas fa-arrow-up"></i>
                        مجدولة
                    </span>
                    <div class="card-action-buttons">
                        <button class="action-btn print-btn" onclick="printStatCard('upcoming-meetings')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn pdf-btn" onclick="exportStatToPDF('upcoming-meetings')" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- الاجتماعات المكتملة -->
        <div class="professional-stat-card completed-meetings-card">
            <div class="card-background-pattern"></div>
            <div class="card-header-section">
                <div class="stat-icon-container">
                    <div class="stat-icon info-icon">
                        <i class="fas fa-check-double"></i>
                    </div>
                    <div class="icon-glow"></div>
                </div>
                <div class="stat-badge completed-badge">
                    <span>مكتملة</span>
                </div>
            </div>
            <div class="card-content-section">
                <div class="stat-number-container">
                    <div class="stat-number" id="completedMeetings">{{ completed_meetings or 0 }}</div>
                    <div class="stat-unit">اجتماع</div>
                </div>
                <div class="stat-label">الاجتماعات المكتملة</div>
                <div class="progress-indicator">
                    <div class="progress-bar completed-progress" style="width: 85%"></div>
                </div>
                <div class="stat-meta">
                    <span class="trend-text">
                        <i class="fas fa-check"></i>
                        منجزة
                    </span>
                    <div class="card-action-buttons">
                        <button class="action-btn print-btn" onclick="printStatCard('completed-meetings')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn pdf-btn" onclick="exportStatToPDF('completed-meetings')" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- الاجتماعات الملغية -->
        <div class="professional-stat-card cancelled-meetings-card">
            <div class="card-background-pattern"></div>
            <div class="card-header-section">
                <div class="stat-icon-container">
                    <div class="stat-icon warning-icon">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="icon-glow"></div>
                </div>
                <div class="stat-badge cancelled-badge">
                    <span>ملغية</span>
                </div>
            </div>
            <div class="card-content-section">
                <div class="stat-number-container">
                    <div class="stat-number" id="cancelledMeetings">{{ cancelled_meetings or 0 }}</div>
                    <div class="stat-unit">اجتماع</div>
                </div>
                <div class="stat-label">الاجتماعات الملغية</div>
                <div class="progress-indicator">
                    <div class="progress-bar cancelled-progress" style="width: 25%"></div>
                </div>
                <div class="stat-meta">
                    <span class="trend-text">
                        <i class="fas fa-times"></i>
                        ملغية
                    </span>
                    <div class="card-action-buttons">
                        <button class="action-btn print-btn" onclick="printStatCard('cancelled-meetings')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn pdf-btn" onclick="exportStatToPDF('cancelled-meetings')" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- المتوسط الشهري -->
        <div class="professional-stat-card average-meetings-card">
            <div class="card-background-pattern"></div>
            <div class="card-header-section">
                <div class="stat-icon-container">
                    <div class="stat-icon secondary-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="icon-glow"></div>
                </div>
                <div class="stat-badge average-badge">
                    <span>متوسط</span>
                </div>
            </div>
            <div class="card-content-section">
                <div class="stat-number-container">
                    <div class="stat-number" id="monthlyAverage">{{ "%.1f"|format(avg_per_month or 0) }}</div>
                    <div class="stat-unit">شهرياً</div>
                </div>
                <div class="stat-label">المتوسط الشهري</div>
                <div class="progress-indicator">
                    <div class="progress-bar average-progress" style="width: 60%"></div>
                </div>
                <div class="stat-meta">
                    <span class="trend-text">
                        <i class="fas fa-chart-bar"></i>
                        معدل
                    </span>
                    <div class="card-action-buttons">
                        <button class="action-btn print-btn" onclick="printStatCard('monthly-average')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn pdf-btn" onclick="exportStatToPDF('monthly-average')" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- أكثر الشهور نشاطاً -->
        <div class="professional-stat-card champion-month-card">
            <div class="card-background-pattern champion-pattern"></div>
            <div class="card-header-section">
                <div class="stat-icon-container">
                    <div class="stat-icon champion-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="icon-glow champion-glow"></div>
                </div>
                <div class="stat-badge champion-badge">
                    <span>الأكثر نشاطاً</span>
                </div>
            </div>
            <div class="card-content-section">
                <div class="stat-number-container">
                    <div class="stat-number champion-number" id="mostActiveMonth" data-month-name="{{ most_active_month or 'يناير' }}" data-month-count="{{ most_active_count or 0 }}">
                        {{ most_active_month or 'يناير' }}
                    </div>
                    <div class="champion-crown">👑</div>
                </div>
                <div class="stat-label">أكثر الشهور نشاطاً</div>
                <div class="stat-sublabel">{{ most_active_count or 0 }} اجتماع مسجل</div>
                <div class="progress-indicator">
                    <div class="progress-bar champion-progress" style="width: 100%"></div>
                </div>
                <div class="stat-meta">
                    <span class="trend-text champion-trend">
                        <i class="fas fa-crown"></i>
                        بطل الشهر
                    </span>
                    <div class="card-action-buttons">
                        <button class="action-btn print-btn" onclick="printStatCard('most-active-month')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn pdf-btn" onclick="exportStatToPDF('most-active-month')" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني الشهري الجديد -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="progress-table">
                <div class="progress-table-header">
                    <span><i class="fas fa-chart-line me-2"></i>الإحصائيات الشهرية للعام الحالي</span>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="printMonthlyChart()" title="طباعة الرسم البياني الشهري">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="exportMonthlyChartToPDF()" title="تصدير الرسم البياني الشهري كـ PDF">
                            <i class="fas fa-file-pdf me-1"></i>
                            PDF
                        </button>
                    </div>
                </div>
                <div class="progress-table-body">
                    <div class="chart-container" style="position: relative; height: 400px; display: flex; align-items: center; justify-content: center;">
                        <canvas id="monthlyChart" width="800" height="400"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Tables - مثل الصور -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="progress-table">
                <div class="progress-table-header">
                    <span><i class="fas fa-chart-bar me-2"></i>تقدم الاجتماعات</span>
                    <span>النسبة المئوية</span>
                </div>
                <div class="progress-table-body">
                    <div class="progress-row">
                        <div class="progress-label">مكتمل</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill green" style="width: 25%"></div>
                        </div>
                        <div class="progress-percentage">25%</div>
                        <div class="progress-count">1</div>
                    </div>
                    <div class="progress-row">
                        <div class="progress-label">معلق</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill blue" style="width: 60%"></div>
                        </div>
                        <div class="progress-percentage">60%</div>
                        <div class="progress-count">3</div>
                    </div>
                    <div class="progress-row">
                        <div class="progress-label">ملغي</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill red" style="width: 20%"></div>
                        </div>
                        <div class="progress-percentage">20%</div>
                        <div class="progress-count">1</div>
                    </div>
                    <div class="progress-row">
                        <div class="progress-label">الإجمالي</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill green" style="width: 100%"></div>
                        </div>
                        <div class="progress-percentage">100%</div>
                        <div class="progress-count">5</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-3">
            <div class="progress-table">
                <div class="progress-table-header">
                    <span><i class="fas fa-users me-2"></i>المشاركة حسب الجهة</span>
                    <span>عدد الاجتماعات</span>
                </div>
                <div class="progress-table-body">
                    <div class="progress-row">
                        <div class="progress-label">وزارة الدفاع</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill blue" style="width: 40%"></div>
                        </div>
                        <div class="progress-percentage">40%</div>
                        <div class="progress-count">2</div>
                    </div>
                    <div class="progress-row">
                        <div class="progress-label">الحرس الوطني</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill green" style="width: 20%"></div>
                        </div>
                        <div class="progress-percentage">20%</div>
                        <div class="progress-count">1</div>
                    </div>
                    <div class="progress-row">
                        <div class="progress-label">أمن الدولة</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill orange" style="width: 40%"></div>
                        </div>
                        <div class="progress-percentage">40%</div>
                        <div class="progress-count">2</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التصميم الجديد - مثل الصورة الثانية -->
    <div class="row mb-4">
        <!-- الجدول الجديد على اليسار -->
        <div class="col-lg-6 mb-3">
            <div class="progress-table">
                <div class="progress-table-header">
                    <span><i class="fas fa-chart-bar me-2"></i>تقدم الاجتماعات</span>
                    <span>النسبة المئوية</span>
                </div>
                <div class="progress-table-body">
                    <div class="progress-row">
                        <div class="progress-label">مكتمل</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill" style="width: {{ (completed_meetings / (total_meetings or 1) * 100) if total_meetings else 0 }}%; background: #28a745;"></div>
                        </div>
                        <div class="progress-percentage">{{ (completed_meetings / (total_meetings or 1) * 100)|round|int if total_meetings else 0 }}%</div>
                        <div class="progress-count">{{ completed_meetings or 0 }}</div>
                    </div>
                    <div class="progress-row">
                        <div class="progress-label">قادم</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill" style="width: {{ (upcoming_meetings / (total_meetings or 1) * 100) if total_meetings else 0 }}%; background: #007bff;"></div>
                        </div>
                        <div class="progress-percentage">{{ (upcoming_meetings / (total_meetings or 1) * 100)|round|int if total_meetings else 0 }}%</div>
                        <div class="progress-count">{{ upcoming_meetings or 0 }}</div>
                    </div>
                    <div class="progress-row">
                        <div class="progress-label">ملغي</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill" style="width: {{ (cancelled_meetings / (total_meetings or 1) * 100) if total_meetings else 0 }}%; background: #dc3545;"></div>
                        </div>
                        <div class="progress-percentage">{{ (cancelled_meetings / (total_meetings or 1) * 100)|round|int if total_meetings else 0 }}%</div>
                        <div class="progress-count">{{ cancelled_meetings or 0 }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسم البياني الدائري على اليمين -->
        <div class="col-lg-6 mb-3">
            <div class="progress-table">
                <div class="progress-table-header">
                    <span><i class="fas fa-chart-pie me-2"></i>توزيع الاجتماعات حسب الحالة</span>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="printChart()" title="طباعة الرسم البياني">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="exportChartToPDF()" title="تصدير الرسم البياني كـ PDF">
                            <i class="fas fa-file-pdf me-1"></i>
                            PDF
                        </button>
                    </div>
                </div>
                <div class="progress-table-body">
                    <div class="chart-container" style="position: relative; height: 300px; display: flex; align-items: center; justify-content: center;">
                        <canvas id="meetingsChart" width="300" height="300"></canvas>
                    </div>

                    <!-- رسم الرسم البياني مباشرة -->
                    <script>
                    console.log('🔥🔥🔥 رسم الرسم البياني الجديد');

                    // دالة لتفتيح اللون
                    function lightenColor(color, amount) {
                        const usePound = color[0] === '#';
                        const col = usePound ? color.slice(1) : color;
                        const num = parseInt(col, 16);
                        let r = (num >> 16) + amount * 255;
                        let g = (num >> 8 & 0x00FF) + amount * 255;
                        let b = (num & 0x0000FF) + amount * 255;
                        r = r > 255 ? 255 : r < 0 ? 0 : r;
                        g = g > 255 ? 255 : g < 0 ? 0 : g;
                        b = b > 255 ? 255 : b < 0 ? 0 : b;
                        return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
                    }

                    // بيانات حقيقية من قاعدة البيانات
                    const realData = {
                        completed: {{ completed_meetings or 0 }},
                        upcoming: {{ upcoming_meetings or 0 }},
                        cancelled: {{ cancelled_meetings or 0 }}
                    };

                    console.log('📊 البيانات الحقيقية:', realData);

                    // رسم الرسم البياني
                    const canvas = document.getElementById('meetingsChart');
                    if (canvas) {
                        const ctx = canvas.getContext('2d');
                        const centerX = 150;
                        const centerY = 150;
                        const radius = 80;

                        // مسح الكانفاس
                        ctx.clearRect(0, 0, 300, 300);

                        // حساب الإجمالي
                        const total = realData.completed + realData.upcoming + realData.cancelled;
                        console.log('📊 الإجمالي:', total);

                        if (total > 0) {
                            // بيانات الشرائح
                            const slices = [
                                { value: realData.completed, color: '#28a745', label: 'مكتمل' },
                                { value: realData.upcoming, color: '#007bff', label: 'قادم' },
                                { value: realData.cancelled, color: '#dc3545', label: 'ملغي' }
                            ].filter(slice => slice.value > 0);

                            let currentAngle = -Math.PI / 2;

                            // رسم الشرائح
                            slices.forEach(slice => {
                                const sliceAngle = (slice.value / total) * 2 * Math.PI;
                                const percent = Math.round((slice.value / total) * 100);

                                // رسم الشريحة
                                ctx.beginPath();
                                ctx.moveTo(centerX, centerY);
                                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                                ctx.closePath();
                                ctx.fillStyle = slice.color;
                                ctx.fill();

                                // رسم الحدود
                                ctx.strokeStyle = '#fff';
                                ctx.lineWidth = 3;
                                ctx.stroke();

                                // رسم النسبة المئوية
                                if (sliceAngle > 0.2) {
                                    const textAngle = currentAngle + sliceAngle / 2;
                                    const textX = centerX + Math.cos(textAngle) * (radius * 0.7);
                                    const textY = centerY + Math.sin(textAngle) * (radius * 0.7);

                                    ctx.fillStyle = '#fff';
                                    ctx.font = 'bold 14px Arial';
                                    ctx.textAlign = 'center';
                                    ctx.textBaseline = 'middle';
                                    ctx.fillText(percent + '%', textX, textY);
                                }

                                currentAngle += sliceAngle;
                            });

                            // رسم دائرة في المنتصف
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, 30, 0, 2 * Math.PI);
                            ctx.fillStyle = '#fff';
                            ctx.fill();
                            ctx.strokeStyle = '#dee2e6';
                            ctx.lineWidth = 2;
                            ctx.stroke();

                            // نص الإجمالي
                            ctx.fillStyle = '#333';
                            ctx.font = 'bold 16px Arial';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            ctx.fillText(total, centerX, centerY - 5);

                            ctx.font = '12px Arial';
                            ctx.fillStyle = '#666';
                            ctx.fillText('إجمالي', centerX, centerY + 10);

                            console.log('✅ تم رسم الرسم البياني بنجاح');
                        } else {
                            // رسم دائرة فارغة
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                            ctx.fillStyle = '#f8f9fa';
                            ctx.fill();
                            ctx.strokeStyle = '#dee2e6';
                            ctx.lineWidth = 3;
                            ctx.stroke();

                            ctx.fillStyle = '#6c757d';
                            ctx.font = 'bold 16px Arial';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            ctx.fillText('لا توجد بيانات', centerX, centerY);

                            console.log('✅ تم رسم الرسم البياني الفارغ');
                        }
                    }

                    // دالة طباعة الرسم البياني
                    function printChart() {
                        console.log('🖨️ طباعة الرسم البياني...');

                        const canvas = document.getElementById('meetingsChart');
                        if (!canvas) {
                            alert('❌ لم يتم العثور على الرسم البياني');
                            return;
                        }

                        // تحويل الكانفاس إلى صورة
                        const chartImage = canvas.toDataURL('image/png');

                        // إنشاء نافذة طباعة
                        const printWindow = window.open('', '_blank');
                        const printHTML = `
                            <!DOCTYPE html>
                            <html dir="rtl" lang="ar">
                            <head>
                                <meta charset="UTF-8">
                                <title>طباعة الرسم البياني - توزيع الاجتماعات</title>
                                <style>
                                    @page { size: A4; margin: 2cm; }
                                    body {
                                        font-family: Arial, sans-serif;
                                        direction: rtl;
                                        text-align: center;
                                        padding: 20px;
                                        margin: 0;
                                    }
                                    .header {
                                        background: linear-gradient(135deg, #1b4332, #2d5a3d);
                                        color: white;
                                        padding: 30px;
                                        border-radius: 15px;
                                        margin-bottom: 30px;
                                    }
                                    .chart-container {
                                        margin: 30px 0;
                                        padding: 20px;
                                        border: 2px solid #dee2e6;
                                        border-radius: 15px;
                                        background: #f8f9fa;
                                    }
                                    .chart-image {
                                        max-width: 100%;
                                        height: auto;
                                        border-radius: 10px;
                                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                                    }
                                    .stats-summary {
                                        display: flex;
                                        justify-content: space-around;
                                        margin: 20px 0;
                                        flex-wrap: wrap;
                                    }
                                    .stat-item {
                                        background: white;
                                        padding: 15px;
                                        border-radius: 10px;
                                        margin: 5px;
                                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                        min-width: 120px;
                                    }
                                    .stat-completed { border-left: 4px solid #28a745; }
                                    .stat-upcoming { border-left: 4px solid #007bff; }
                                    .stat-cancelled { border-left: 4px solid #dc3545; }
                                    .footer {
                                        margin-top: 40px;
                                        padding: 20px;
                                        background: #f8f9fa;
                                        border-radius: 10px;
                                        font-size: 12px;
                                        color: #666;
                                    }
                                </style>
                            </head>
                            <body>
                                <div class="header">
                                    <h2>📊 تقرير توزيع الاجتماعات</h2>
                                    <p>الرسم البياني الدائري</p>
                                </div>

                                <div class="chart-container">
                                    <h3>📈 توزيع الاجتماعات حسب الحالة</h3>
                                    <img src="${chartImage}" alt="الرسم البياني" class="chart-image">
                                </div>

                                <div class="stats-summary">
                                    <div class="stat-item stat-completed">
                                        <h4>✅ مكتملة</h4>
                                        <p><strong>${realData.completed}</strong> اجتماع</p>
                                        <small>${Math.round((realData.completed / (realData.completed + realData.upcoming + realData.cancelled)) * 100)}%</small>
                                    </div>
                                    <div class="stat-item stat-upcoming">
                                        <h4>📅 قادمة</h4>
                                        <p><strong>${realData.upcoming}</strong> اجتماع</p>
                                        <small>${Math.round((realData.upcoming / (realData.completed + realData.upcoming + realData.cancelled)) * 100)}%</small>
                                    </div>
                                    <div class="stat-item stat-cancelled">
                                        <h4>❌ ملغية</h4>
                                        <p><strong>${realData.cancelled}</strong> اجتماع</p>
                                        <small>${Math.round((realData.cancelled / (realData.completed + realData.upcoming + realData.cancelled)) * 100)}%</small>
                                    </div>
                                </div>

                                <div class="footer">
                                    <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-SA')}</p>
                                    <p>نظام إدارة الاجتماعات</p>
                                </div>
                            </body>
                            </html>
                        `;

                        printWindow.document.write(printHTML);
                        printWindow.document.close();

                        setTimeout(() => {
                            printWindow.print();
                            printWindow.close();
                        }, 500);
                    }

                    // دالة تصدير PDF
                    function exportChartToPDF() {
                        console.log('📄 تصدير PDF...');

                        const canvas = document.getElementById('meetingsChart');
                        if (!canvas) {
                            alert('❌ لم يتم العثور على الرسم البياني');
                            return;
                        }

                        // تحويل الكانفاس إلى صورة
                        const chartImage = canvas.toDataURL('image/png');

                        // إنشاء نافذة جديدة للـ PDF
                        const pdfWindow = window.open('', '_blank');
                        const pdfHTML = `
                            <!DOCTYPE html>
                            <html dir="rtl" lang="ar">
                            <head>
                                <meta charset="UTF-8">
                                <title>تقرير توزيع الاجتماعات - PDF</title>
                                <style>
                                    @page { size: A4; margin: 1.5cm; }
                                    body {
                                        font-family: Arial, sans-serif;
                                        direction: rtl;
                                        margin: 0;
                                        padding: 20px;
                                        background: #fff;
                                    }
                                    .pdf-header {
                                        background: linear-gradient(135deg, #1b4332, #2d5a3d);
                                        color: white;
                                        padding: 25px;
                                        border-radius: 12px;
                                        margin-bottom: 25px;
                                        text-align: center;
                                    }
                                    .chart-section {
                                        background: #f8f9fa;
                                        padding: 20px;
                                        border-radius: 12px;
                                        margin: 20px 0;
                                        text-align: center;
                                    }
                                    .chart-image {
                                        max-width: 100%;
                                        height: auto;
                                        border-radius: 8px;
                                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                                        margin: 15px 0;
                                    }
                                    .stats-grid {
                                        display: grid;
                                        grid-template-columns: repeat(3, 1fr);
                                        gap: 15px;
                                        margin: 20px 0;
                                    }
                                    .stat-card {
                                        background: white;
                                        padding: 15px;
                                        border-radius: 8px;
                                        text-align: center;
                                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                    }
                                    .stat-card.completed { border-top: 4px solid #28a745; }
                                    .stat-card.upcoming { border-top: 4px solid #007bff; }
                                    .stat-card.cancelled { border-top: 4px solid #dc3545; }
                                    .stat-number { font-size: 24px; font-weight: bold; margin: 10px 0; }
                                    .stat-percentage { font-size: 14px; color: #666; }
                                    .pdf-footer {
                                        margin-top: 30px;
                                        padding: 15px;
                                        background: #f8f9fa;
                                        border-radius: 8px;
                                        text-align: center;
                                        font-size: 12px;
                                        color: #666;
                                    }
                                </style>
                            </head>
                            <body>
                                <div class="pdf-header">
                                    <h2>📊 تقرير توزيع الاجتماعات - PDF</h2>
                                    <p>الرسم البياني الدائري التفاعلي</p>
                                </div>

                                <div class="chart-section">
                                    <h3>📈 الرسم البياني - توزيع الاجتماعات حسب الحالة</h3>
                                    <img src="${chartImage}" alt="الرسم البياني" class="chart-image">

                                    <div class="stats-grid">
                                        <div class="stat-card completed">
                                            <h4>✅ الاجتماعات المكتملة</h4>
                                            <div class="stat-number">${realData.completed}</div>
                                            <div class="stat-percentage">${Math.round((realData.completed / (realData.completed + realData.upcoming + realData.cancelled)) * 100)}%</div>
                                        </div>
                                        <div class="stat-card upcoming">
                                            <h4>📅 الاجتماعات القادمة</h4>
                                            <div class="stat-number">${realData.upcoming}</div>
                                            <div class="stat-percentage">${Math.round((realData.upcoming / (realData.completed + realData.upcoming + realData.cancelled)) * 100)}%</div>
                                        </div>
                                        <div class="stat-card cancelled">
                                            <h4>❌ الاجتماعات الملغية</h4>
                                            <div class="stat-number">${realData.cancelled}</div>
                                            <div class="stat-percentage">${Math.round((realData.cancelled / (realData.completed + realData.upcoming + realData.cancelled)) * 100)}%</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="pdf-footer">
                                    <p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                                    <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                                    <p>نظام إدارة الاجتماعات - تقرير مُولد تلقائياً</p>
                                </div>
                            </body>
                            </html>
                        `;

                        pdfWindow.document.write(pdfHTML);
                        pdfWindow.document.close();

                        setTimeout(() => {
                            pdfWindow.print();
                        }, 1000);
                    }
                    </script>









    <!-- Statistics Overview -->
    <div class="reports-section fade-in-up">
        <div class="section-header">
            <i class="fas fa-chart-line me-2"></i>
            نظرة عامة على الإحصائيات
        </div>

        <div class="section-body">
            <!-- تم نقل البطاقات إلى الأعلى -->

        </div>
    </div>


</div>
{% endblock %}

{% block extra_js %}
<!-- نظام الإشعارات الموحد -->
<script src="{{ url_for('static', filename='js/unified-notifications.js') }}"></script>
<script src="{{ url_for('static', filename='js/reports.js') }}"></script>

<!-- دوال الإشعارات البديلة -->
<script>
console.log('🟢🟢🟢 تم تحميل ملف JavaScript للتقارير!');

// دوال الإشعارات البديلة في حالة عدم تحميل النظام الموحد
if (typeof showSuccessNotification === 'undefined') {
    window.showSuccessNotification = function(message) {
        console.log('✅ نجاح:', message);
        alert('✅ ' + message);
    };
}

if (typeof showErrorNotification === 'undefined') {
    window.showErrorNotification = function(message) {
        console.log('❌ خطأ:', message);
        alert('❌ ' + message);
    };
}

if (typeof showInfoNotification === 'undefined') {
    window.showInfoNotification = function(message) {
        console.log('ℹ️ معلومات:', message);
        alert('ℹ️ ' + message);
    };
}

if (typeof showWarningNotification === 'undefined') {
    window.showWarningNotification = function(message) {
        console.log('⚠️ تحذير:', message);
        alert('⚠️ ' + message);
    };
}

// دوال إضافية للتوافق
if (typeof showSuccessMessage === 'undefined') {
    window.showSuccessMessage = window.showSuccessNotification;
}

if (typeof showErrorMessage === 'undefined') {
    window.showErrorMessage = window.showErrorNotification;
}

if (typeof showLoadingMessage === 'undefined') {
    window.showLoadingMessage = function(message) {
        console.log('🔄 تحميل:', message);
    };
}

if (typeof hideLoadingMessage === 'undefined') {
    window.hideLoadingMessage = function() {
        console.log('✅ انتهاء التحميل');
    };
}

console.log('✅ تم تحميل دوال الإشعارات البديلة');
</script>

<!-- تحسين Date Picker الحديث للتقارير -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة منتقي التاريخ الحديث في التقارير
    initModernDatePickersReports();

    // إعداد التواريخ الافتراضية للتقارير
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // تعيين التواريخ الافتراضية
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');

    if (startDate && !startDate.value) {
        startDate.value = firstDayOfMonth.toISOString().split('T')[0];
        updateReportDateDisplay(startDate, startDate.nextElementSibling);
        startDate.dispatchEvent(new Event('change'));
    }
    if (endDate && !endDate.value) {
        endDate.value = today.toISOString().split('T')[0];
        updateReportDateDisplay(endDate, endDate.nextElementSibling);
        endDate.dispatchEvent(new Event('change'));
    }
});

// تهيئة منتقي التاريخ الحديث للتقارير
function initModernDatePickersReports() {
    const datePickers = document.querySelectorAll('.modern-date-picker-reports');

    datePickers.forEach(picker => {
        const input = picker.querySelector('.modern-date-input-reports');
        const iconBox = picker.querySelector('.date-icon-box-reports');
        const placeholder = picker.querySelector('.date-placeholder-reports');

        // النقر على الأيقونة أو المربع لفتح منتقي التاريخ
        iconBox.addEventListener('click', () => {
            input.focus();
            if (input.showPicker) {
                input.showPicker();
            }
        });

        // النقر على المنطقة الكاملة لفتح منتقي التاريخ
        picker.addEventListener('click', (e) => {
            if (e.target !== input) {
                input.focus();
                if (input.showPicker) {
                    input.showPicker();
                }
            }
        });

        // تحديث المظهر عند تغيير التاريخ
        input.addEventListener('change', () => {
            updateReportDateDisplay(input, placeholder);

            // تحديث عرض التاريخ بالتنسيق المطلوب
            if (input.value) {
                const formattedDate = formatDateYMD(input.value);
                // إنشاء عنصر لعرض التاريخ المنسق
                let displaySpan = input.parentElement.querySelector('.date-display-span');
                if (!displaySpan) {
                    displaySpan = document.createElement('span');
                    displaySpan.className = 'date-display-span';
                    displaySpan.style.cssText = `
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                        color: #1B4332;
                        font-size: 1.1rem;
                        font-weight: 600;
                        pointer-events: none;
                        z-index: 15;
                        font-family: 'Cairo', 'Tajawal', sans-serif;
                    `;
                    input.parentElement.appendChild(displaySpan);
                }
                displaySpan.textContent = formattedDate;
                displaySpan.style.display = 'block';
                input.style.opacity = '0';
            } else {
                const displaySpan = input.parentElement.querySelector('.date-display-span');
                if (displaySpan) {
                    displaySpan.style.display = 'none';
                }
                input.style.opacity = '1';
            }

            // إظهار رسالة تأكيد
            showReportDateSelectedMessage(input);

            // تحديث الإحصائيات تلقائياً عند تغيير التاريخ - معطل لتجنب التضارب
            // if (typeof updateStatistics === 'function') {
            //     setTimeout(updateStatistics, 500);
            // }
        });

        // تحديث المظهر عند التركيز
        input.addEventListener('focus', () => {
            picker.classList.add('focused');
        });

        input.addEventListener('blur', () => {
            picker.classList.remove('focused');
        });

        // تحديث المظهر عند تحميل الصفحة
        updateReportDateDisplay(input, placeholder);

        // تحديث عرض التاريخ الأولي إذا كان موجوداً
        if (input.value) {
            input.dispatchEvent(new Event('change'));
        }
    });
}

// وظيفة تحديث عرض التاريخ في التقارير
function updateReportDateDisplay(input, placeholder) {
    const picker = input.closest('.modern-date-picker-reports');

    if (input.value) {
        // إخفاء النص البديل عند وجود تاريخ
        placeholder.style.opacity = '0';

        // تحسين مظهر النص المدخل
        input.style.color = '#1B4332';
        input.style.fontWeight = '600';

        // إضافة كلاس للمربع عند وجود قيمة
        picker.classList.add('has-value');
    } else {
        // إظهار النص البديل عند عدم وجود تاريخ
        placeholder.style.opacity = '1';

        // إعادة تعيين مظهر النص
        input.style.color = '#495057';
        input.style.fontWeight = '400';

        // إزالة كلاس القيمة
        picker.classList.remove('has-value');
    }
}

// دالة تنسيق التاريخ بصيغة السنة/الشهر/اليوم
function formatDateYMD(dateValue) {
    const date = new Date(dateValue);
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // الشهر يبدأ من 0
    const day = date.getDate();
    return `${year}/${month}/${day}`;
}

// إظهار رسالة تأكيد اختيار التاريخ في التقارير
function showReportDateSelectedMessage(input) {
    const formattedDate = formatDateYMD(input.value);
    const message = `📅 تم اختيار التاريخ: ${formattedDate}`;
    showSuccessNotification(message);
}
</script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// تحقق فوري من Chart.js
console.log('🔍 فحص Chart.js فوري...');
if (typeof Chart !== 'undefined') {
    console.log('✅ Chart.js محمل بنجاح، الإصدار:', Chart.version);
} else {
    console.log('❌ Chart.js غير محمل');
}

// تحقق من العناصر
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 فحص العناصر...');

    const canvas = document.getElementById('mainChart');
    const tabs = document.querySelectorAll('.chart-tab');

    console.log('Canvas موجود:', !!canvas);
    console.log('عدد الأزرار:', tabs.length);

    if (canvas) {
        console.log('✅ عنصر mainChart موجود');
    } else {
        console.log('❌ عنصر mainChart غير موجود');
    }
});

// ===== نظام الإشعارات الذهبي للتقارير =====

// نظام الإشعارات الذهبي للتقارير
function showGoldenNotification(message, type = 'success', duration = 4000) {
    // إزالة الإشعار السابق إن وجد
    const existingNotification = document.getElementById('goldenNotification');
    if (existingNotification) {
        existingNotification.remove();
    }

    const notificationDiv = document.createElement('div');
    notificationDiv.id = 'goldenNotification';
    notificationDiv.className = 'golden-notification';

    // تحديد الألوان والأيقونة حسب النوع
    let bgGradient, iconClass, iconColor;
    switch(type) {
        case 'success':
            bgGradient = 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)';
            iconClass = 'fas fa-check-circle';
            iconColor = '#155724';
            break;
        case 'error':
            bgGradient = 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)';
            iconClass = 'fas fa-exclamation-circle';
            iconColor = '#856404';
            break;
        case 'warning':
            bgGradient = 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)';
            iconClass = 'fas fa-exclamation-triangle';
            iconColor = '#856404';
            break;
        case 'info':
            bgGradient = 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)';
            iconClass = 'fas fa-info-circle';
            iconColor = '#856404';
            break;
        default:
            bgGradient = 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)';
            iconClass = 'fas fa-bell';
            iconColor = '#856404';
    }

    notificationDiv.style.cssText = `
        position: fixed !important;
        top: 20px !important;
        left: 20px !important;
        background: ${bgGradient} !important;
        color: #212529 !important;
        padding: 1rem 1.5rem !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3) !important;
        z-index: 99999 !important;
        min-width: 320px !important;
        max-width: 500px !important;
        transform: translateX(-400px) !important;
        transition: all 0.4s ease !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        direction: rtl !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    `;

    notificationDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.75rem;">
            <i class="${iconClass}" style="font-size: 1.2rem; color: ${iconColor}; flex-shrink: 0;"></i>
            <span style="font-weight: 600; flex: 1; color: #212529 !important; font-size: 1rem; line-height: 1.4;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: #212529 !important; font-size: 1.4rem; cursor: pointer; padding: 0; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                ×
            </button>
        </div>
    `;

    document.body.appendChild(notificationDiv);

    // تأثير الظهور
    setTimeout(() => {
        notificationDiv.style.transform = 'translateX(0)';
    }, 100);

    // إخفاء تلقائي
    if (duration > 0) {
        setTimeout(() => {
            if (notificationDiv && notificationDiv.parentElement) {
                notificationDiv.style.transform = 'translateX(-400px)';
                setTimeout(() => {
                    if (notificationDiv && notificationDiv.parentElement) {
                        notificationDiv.remove();
                    }
                }, 400);
            }
        }, duration);
    }
}

// دوال مساعدة للإشعارات
function showSuccessNotification(message) {
    showGoldenNotification(message, 'success');
}

function showErrorNotification(message) {
    showGoldenNotification(message, 'error');
}

function showWarningNotification(message) {
    showGoldenNotification(message, 'warning');
}

function showInfoNotification(message) {
    showGoldenNotification(message, 'info');
}

// دالة اختبار الإشعارات
function testNotifications() {
    console.log('🧪 اختبار الإشعارات...');
    showSuccessNotification('✅ اختبار إشعار النجاح - النص يجب أن يظهر بوضوح');
}



// دالة عرض اسم الشهر مع رقم الشهر
function displayMonthWithNumber() {
    const monthElement = document.getElementById('mostActiveMonth');
    if (!monthElement) return;

    const monthName = monthElement.getAttribute('data-month-name');
    const monthCount = monthElement.getAttribute('data-month-count') || '0';
    if (!monthName) return;

    // قاموس تحويل أسماء الشهور إلى أرقام
    const monthMap = {
        'كانون الثاني': '1',
        'شباط': '2',
        'آذار': '3',
        'نيسان': '4',
        'أيار': '5',
        'حزيران': '6',
        'تموز': '7',
        'آب': '8',
        'أيلول': '9',
        'تشرين الأول': '10',
        'تشرين الثاني': '11',
        'كانون الأول': '12',
        // أسماء عربية احتياطية
        'يناير': '1',
        'فبراير': '2',
        'مارس': '3',
        'أبريل': '4',
        'مايو': '5',
        'يونيو': '6',
        'يوليو': '7',
        'أغسطس': '8',
        'سبتمبر': '9',
        'أكتوبر': '10',
        'نوفمبر': '11',
        'ديسمبر': '12',
        // أسماء إنجليزية احتياطية
        'January': '1',
        'February': '2',
        'March': '3',
        'April': '4',
        'May': '5',
        'June': '6',
        'July': '7',
        'August': '8',
        'September': '9',
        'October': '10',
        'November': '11',
        'December': '12'
    };

    // التعامل مع الحالات المختلفة
    let displayText;

    if (monthName === 'متساوي') {
        displayText = 'جميع الشهور متساوية';
        // إضافة كلاس للنص الطويل
        monthElement.classList.add('long-text');
    } else if (monthName === 'لا يوجد') {
        displayText = 'لا توجد اجتماعات';
        // إضافة كلاس للنص الطويل
        monthElement.classList.add('long-text');
    } else {
        // عرض رقم الشهر في التقويم (يناير=1, فبراير=2, إلخ)
        const monthNumber = monthMap[monthName] || '1';
        displayText = `${monthName} (${monthNumber})`;

        // إضافة كلاس للأسماء الطويلة
        if (monthName === 'تشرين الثاني' || monthName === 'تشرين الأول' || monthName === 'كانون الثاني' || monthName === 'كانون الأول') {
            monthElement.classList.add('long-text');
        } else {
            // إزالة كلاس النص الطويل إذا كان موجوداً
            monthElement.classList.remove('long-text');
        }
    }

    monthElement.textContent = displayText;
    console.log(`🗓️ تم عرض الشهر: "${displayText}"`);
}

// تشغيل اختبار الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل صفحة التقارير');

    // عرض اسم الشهر مع رقم الشهر
    displayMonthWithNumber();

    // تهيئة محدد الفترة الزمنية - معطل لتجنب التضارب مع البيانات الحقيقية
    // const periodFilter = document.getElementById('periodFilter');
    // if (periodFilter) {
    //     // تطبيق الفترة الافتراضية
    //     handlePeriodChange(periodFilter.value);
    // }

    // يمكن إلغاء التعليق لاختبار الإشعارات
    // setTimeout(() => testNotifications(), 2000);

    // اختبار الأزرار عند تحميل الصفحة
    console.log('🔧 تم تحميل صفحة التقارير');
    console.log('🔧 اختبار وجود الدوال:');
    console.log('🔧 printStatCard:', typeof printStatCard);
    console.log('🔧 exportStatToPDF:', typeof exportStatToPDF);
    console.log('🔧 getCardData:', typeof getCardData);

    // إضافة دوال اختبار مؤقتة إذا لم تكن موجودة
    if (typeof printStatCard === 'undefined') {
        console.log('⚠️ دالة printStatCard غير موجودة - إنشاء دالة مؤقتة');
        window.printStatCard = function(cardType) {
            console.log('🖨️ طباعة البطاقة:', cardType);

            // إنشاء محتوى بسيط للطباعة
            const printContent = `
                <div style="text-align: center; padding: 40px; font-family: Arial;">
                    <h1>نظام إدارة الاجتماعات</h1>
                    <h2>تقرير إحصائي - ${cardType}</h2>
                    <div style="border: 2px solid #007bff; padding: 20px; margin: 20px; border-radius: 10px;">
                        <h3>البيانات الإحصائية</h3>
                        <p>تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}</p>
                    </div>
                </div>
            `;

            const originalContent = document.body.innerHTML;
            document.body.innerHTML = printContent;
            window.print();
            document.body.innerHTML = originalContent;
            location.reload();
        };
    }

    if (typeof exportStatToPDF === 'undefined') {
        console.log('⚠️ دالة exportStatToPDF غير موجودة - إنشاء دالة مؤقتة');
        window.exportStatToPDF = function(cardType) {
            console.log('📄 تصدير PDF للبطاقة:', cardType);

            // إنشاء محتوى بسيط لتصدير PDF
            const pdfContent = `
                <div style="text-align: center; padding: 40px; font-family: Arial;">
                    <h1>نظام إدارة الاجتماعات</h1>
                    <h2>تقرير PDF - ${cardType}</h2>
                    <div style="border: 2px solid #dc3545; padding: 20px; margin: 20px; border-radius: 10px;">
                        <h3>البيانات الإحصائية</h3>
                        <p>تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}</p>
                        <p><strong>تعليمات:</strong> اختر "حفظ كـ PDF" من خيارات الطباعة</p>
                    </div>
                </div>
            `;

            const originalContent = document.body.innerHTML;
            document.body.innerHTML = pdfContent;
            setTimeout(() => {
                window.print();
                setTimeout(() => {
                    document.body.innerHTML = originalContent;
                    location.reload();
                }, 1000);
            }, 500);
        };
    }
});

</script>
<script src="{{ url_for('static', filename='js/reports-fixed.js') }}"></script>
<script src="{{ url_for('static', filename='js/simple-charts.js') }}"></script>
<script src="{{ url_for('static', filename='js/print-reports.js') }}"></script>
<script src="{{ url_for('static', filename='js/print-fix.js') }}"></script>
<!-- قالب الطباعة الموحد -->
<script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>

<script>
// حل مباشر لمشكلة الطباعة والـ PDF
console.log('🚀 تحميل الحل المباشر للطباعة والـ PDF');

// دالة لإنشاء جدول الاجتماعات حسب النوع
function createMeetingsTable(cardType) {
    // بيانات الاجتماعات الثابتة للعرض التوضيحي
    const meetings = [
        {
            id: 1,
            title: 'اجتماع مجلس الإدارة',
            date: '2025-01-15',
            time: '10:00',
            location: 'قاعة الاجتماعات الرئيسية',
            status: 'scheduled',
            attendees_count: 8,
            description: 'مناقشة الخطة الاستراتيجية للعام الجديد'
        },
        {
            id: 2,
            title: 'اجتماع فريق التطوير',
            date: '2025-01-20',
            time: '14:00',
            location: 'مكتب التطوير',
            status: 'completed',
            attendees_count: 5,
            description: 'مراجعة التقدم في المشاريع الحالية'
        },
        {
            id: 3,
            title: 'اجتماع الموارد البشرية',
            date: '2025-01-25',
            time: '09:30',
            location: 'قاعة الموارد البشرية',
            status: 'cancelled',
            attendees_count: 3,
            description: 'مناقشة سياسات الموظفين الجديدة'
        },
        {
            id: 4,
            title: 'اجتماع المراجعة الشهرية',
            date: '2025-02-01',
            time: '11:00',
            location: 'قاعة المؤتمرات',
            status: 'scheduled',
            attendees_count: 12,
            description: 'مراجعة الأداء والنتائج الشهرية'
        }
    ];

    let filteredMeetings = [];
    const now = new Date();
    const today = new Date();
    today.setHours(0, 0, 0, 0); // بداية اليوم الحالي

    console.log('🔍 فلترة الاجتماعات لنوع البطاقة:', cardType);
    console.log('📅 التاريخ الحالي:', today.toISOString().split('T')[0]);

    switch(cardType) {
        case 'total-meetings':
            filteredMeetings = meetings;
            console.log('📊 إجمالي الاجتماعات:', filteredMeetings.length);
            break;

        case 'upcoming-meetings':
            filteredMeetings = meetings.filter(m => {
                const meetingDate = new Date(m.date);
                meetingDate.setHours(0, 0, 0, 0);
                // اجتماع قادم = في المستقبل أو اليوم + ليس ملغي أو مكتمل
                const isUpcoming = meetingDate >= today && (m.status === 'scheduled' || m.status === 'active' || !m.status);
                console.log(`📅 ${m.title}: ${m.date} - ${isUpcoming ? 'قادم ✅' : 'ليس قادم ❌'} (الحالة: ${m.status || 'غير محدد'})`);
                return isUpcoming;
            });
            // ترتيب من الأقرب للأبعد
            filteredMeetings.sort((a, b) => new Date(a.date) - new Date(b.date));
            console.log('🔜 الاجتماعات القادمة النهائية:', filteredMeetings.length);
            break;

        case 'completed-meetings':
            filteredMeetings = meetings.filter(m => {
                const meetingDate = new Date(m.date);
                meetingDate.setHours(0, 0, 0, 0);
                // اجتماع مكتمل = في الماضي أو حالته مكتمل
                const isCompleted = meetingDate < today || m.status === 'completed';
                console.log(`✅ ${m.title}: ${m.date} - ${isCompleted ? 'مكتمل ✅' : 'ليس مكتمل ❌'} (الحالة: ${m.status || 'غير محدد'})`);
                return isCompleted;
            });
            // ترتيب من الأحدث للأقدم
            filteredMeetings.sort((a, b) => new Date(b.date) - new Date(a.date));
            console.log('✅ الاجتماعات المكتملة النهائية:', filteredMeetings.length);
            break;

        case 'cancelled-meetings':
            filteredMeetings = meetings.filter(m => {
                const isCancelled = m.status === 'cancelled';
                console.log(`❌ ${m.title}: ${isCancelled ? 'ملغي' : 'ليس ملغي'} (الحالة: ${m.status})`);
                return isCancelled;
            });
            // ترتيب من الأحدث للأقدم
            filteredMeetings.sort((a, b) => new Date(b.date) - new Date(a.date));
            console.log('❌ الاجتماعات الملغية:', filteredMeetings.length);
            break;

        case 'monthly-average':
            // عرض جميع الاجتماعات مرتبة من الأحدث للأقدم
            filteredMeetings = [...meetings];
            filteredMeetings.sort((a, b) => new Date(b.date) - new Date(a.date));
            console.log('📈 اجتماعات المتوسط الشهري:', filteredMeetings.length);
            break;

        case 'most-active-month':
            // عرض اجتماعات الشهر الأكثر نشاطاً (تشرين الثاني)
            filteredMeetings = meetings.filter(m => {
                const meetingDate = new Date(m.date);
                const isNovember = meetingDate.getMonth() === 10; // تشرين الثاني = شهر 10 (0-based)
                console.log(`🏆 ${m.title}: ${m.date} - ${isNovember ? 'في تشرين الثاني' : 'ليس في تشرين الثاني'}`);
                return isNovember;
            });
            // ترتيب من الأقرب للأبعد
            filteredMeetings.sort((a, b) => new Date(a.date) - new Date(b.date));
            console.log('🏆 اجتماعات الشهر الأكثر نشاطاً:', filteredMeetings.length);
            break;
    }

    if (filteredMeetings.length === 0) {
        const noDataMessages = {
            'upcoming-meetings': 'لا توجد اجتماعات قادمة حالياً',
            'completed-meetings': 'لا توجد اجتماعات مكتملة',
            'cancelled-meetings': 'لا توجد اجتماعات ملغية',
            'total-meetings': 'لا توجد اجتماعات في النظام',
            'monthly-average': 'لا توجد اجتماعات لحساب المتوسط',
            'most-active-month': 'لا توجد اجتماعات في الشهر الأكثر نشاطاً'
        };

        return `
            <div class="meetings-table-container">
                <h3 class="table-title">📋 ${tableTitles[cardType] || 'تفاصيل الاجتماعات'} (0 اجتماع)</h3>
                <div class="no-data">
                    <div class="no-data-icon">📅</div>
                    <div class="no-data-message">${noDataMessages[cardType] || 'لا توجد اجتماعات لعرضها'}</div>
                    <div class="no-data-note">سيتم عرض الاجتماعات هنا عند إضافتها للنظام</div>
                </div>
            </div>
        `;
    }

    // تحديد عنوان الجدول حسب نوع البطاقة
    const tableTitles = {
        'total-meetings': '📋 جميع الاجتماعات',
        'upcoming-meetings': '🔜 الاجتماعات القادمة',
        'completed-meetings': '✅ الاجتماعات المكتملة',
        'cancelled-meetings': '❌ الاجتماعات الملغية',
        'monthly-average': '📈 جميع الاجتماعات (للمتوسط الشهري)',
        'most-active-month': '🏆 اجتماعات الشهر الأكثر نشاطاً (تشرين الثاني)'
    };

    const tableTitle = tableTitles[cardType] || '📋 تفاصيل الاجتماعات';

    let tableHTML = `
        <div class="meetings-table-container">
            <h3 class="table-title">${tableTitle} (${filteredMeetings.length} اجتماع)</h3>
            <table class="meetings-table">
                <thead>
                    <tr>
                        <th>العنوان</th>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                        <th>المكان</th>
                        <th>الحالة</th>
                        <th>المشاركون</th>
                    </tr>
                </thead>
                <tbody>
    `;

    filteredMeetings.forEach(meeting => {
        const statusText = {
            'scheduled': 'مجدول',
            'completed': 'مكتمل',
            'cancelled': 'ملغي',
            'postponed': 'مؤجل'
        }[meeting.status] || meeting.status;

        const statusClass = {
            'scheduled': 'status-scheduled',
            'completed': 'status-completed',
            'cancelled': 'status-cancelled',
            'postponed': 'status-postponed'
        }[meeting.status] || 'status-default';

        tableHTML += `
            <tr>
                <td class="meeting-title">${meeting.title}</td>
                <td class="meeting-date">${meeting.date}</td>
                <td class="meeting-time">${meeting.time}</td>
                <td class="meeting-location">${meeting.location}</td>
                <td class="meeting-status ${statusClass}">${statusText}</td>
                <td class="meeting-attendees">${meeting.attendees_count}</td>
            </tr>
        `;
    });

    tableHTML += `
                </tbody>
            </table>
            <div class="table-summary">
                <p><strong>إجمالي الاجتماعات المعروضة:</strong> ${filteredMeetings.length}</p>
            </div>
        </div>
    `;

    return tableHTML;
}

// دالة لطباعة بطاقة محددة - محدثة مع الفلترة الذكية
function directPrintCard(cardType) {
    console.log('🖨️ طباعة مباشرة للبطاقة مع الفلترة الذكية:', cardType);
    console.log('🔄 إصدار الطباعة: v2.0 - فلترة ذكية');

    let title = '', value = '', description = '', color = '#007bff';

    switch(cardType) {
        case 'total-meetings':
            title = 'إجمالي الاجتماعات';
            value = document.getElementById('totalMeetings')?.textContent?.trim() || '{{ total_meetings or 0 }}';
            description = 'العدد الكلي للاجتماعات المسجلة في النظام';
            color = '#007bff';
            console.log('📊 بيانات إجمالي الاجتماعات:', value);
            break;
        case 'upcoming-meetings':
            title = 'الاجتماعات القادمة';
            value = document.getElementById('upcomingMeetings')?.textContent?.trim() || '{{ upcoming_meetings or 0 }}';
            description = 'الاجتماعات المجدولة في المستقبل';
            color = '#28a745';
            console.log('📊 بيانات الاجتماعات القادمة:', value);
            break;
        case 'completed-meetings':
            title = 'الاجتماعات المكتملة';
            value = document.getElementById('completedMeetings')?.textContent?.trim() || '{{ completed_meetings or 0 }}';
            description = 'الاجتماعات التي تم عقدها بنجاح';
            color = '#17a2b8';
            console.log('📊 بيانات الاجتماعات المكتملة:', value);
            break;
        case 'cancelled-meetings':
            title = 'الاجتماعات الملغية';
            value = document.getElementById('cancelledMeetings')?.textContent?.trim() || '{{ cancelled_meetings or 0 }}';
            description = 'الاجتماعات التي تم إلغاؤها';
            color = '#dc3545';
            console.log('📊 بيانات الاجتماعات الملغية:', value);
            break;
        case 'monthly-average':
            title = 'المتوسط الشهري';
            value = document.getElementById('monthlyAverage')?.textContent?.trim() || '{{ "%.1f"|format(avg_per_month or 0) }}';
            description = 'معدل الاجتماعات شهرياً';
            color = '#6f42c1';
            console.log('📊 بيانات المتوسط الشهري:', value);
            break;
        case 'most-active-month':
            title = 'أكثر الشهور نشاطاً';
            value = document.getElementById('mostActiveMonth')?.textContent?.trim() || '{{ most_active_month or "غير محدد" }}';
            description = 'الشهر الذي سجل أعلى عدد من الاجتماعات';
            color = '#fd7e14';
            console.log('📊 بيانات أكثر الشهور نشاطاً:', value);
            break;
    }

    console.log('📊 البيانات:', { title, value, description, color });

    // إنشاء جدول الاجتماعات للطباعة
    console.log('🖨️ إنشاء جدول الطباعة لنوع البطاقة:', cardType);
    const meetingsTable = createMeetingsTable(cardType);
    console.log('🖨️ تم إنشاء جدول الطباعة بنجاح');

    const printHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>طباعة - ${title}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    direction: rtl;
                    padding: 40px;
                    line-height: 1.6;
                }
                .header {
                    text-align: center;
                    margin-bottom: 40px;
                    border-bottom: 3px solid ${color};
                    padding-bottom: 20px;
                }
                .main-title {
                    font-size: 28px;
                    font-weight: bold;
                    color: ${color};
                    margin-bottom: 10px;
                }
                .subtitle {
                    font-size: 16px;
                    color: #666;
                }
                .card {
                    background: #f8f9fa;
                    border: 2px solid ${color};
                    border-radius: 12px;
                    padding: 40px;
                    margin: 30px 0;
                    text-align: center;
                }
                .value {
                    font-size: 48px;
                    font-weight: bold;
                    color: ${color};
                    margin-bottom: 20px;
                }
                .card-title {
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 15px;
                }
                .card-description {
                    font-size: 16px;
                    color: #666;
                    line-height: 1.6;
                }

                /* تنسيق الجدول */
                .meetings-table-container {
                    margin: 40px 0;
                    background: white;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .table-title {
                    color: ${color};
                    font-size: 20px;
                    margin-bottom: 20px;
                    text-align: center;
                    border-bottom: 2px solid ${color};
                    padding-bottom: 10px;
                }
                .meetings-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    font-size: 14px;
                }
                .meetings-table th {
                    background: ${color};
                    color: white;
                    padding: 12px 8px;
                    text-align: center;
                    font-weight: bold;
                    border: 1px solid #ddd;
                }
                .meetings-table td {
                    padding: 10px 8px;
                    border: 1px solid #ddd;
                    text-align: center;
                    vertical-align: middle;
                }
                .meetings-table tr:nth-child(even) {
                    background-color: #f8f9fa;
                }
                .meetings-table tr:hover {
                    background-color: #e9ecef;
                }
                .meeting-title {
                    font-weight: bold;
                    color: #333;
                    text-align: right !important;
                }
                .meeting-date {
                    color: #666;
                    font-family: monospace;
                }
                .meeting-time {
                    color: #666;
                    font-family: monospace;
                }
                .meeting-location {
                    color: #555;
                    text-align: right !important;
                }
                .status-scheduled {
                    background: #28a745;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                .status-completed {
                    background: #17a2b8;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                .status-cancelled {
                    background: #dc3545;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                .status-postponed {
                    background: #ffc107;
                    color: #333;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                .meeting-attendees {
                    font-weight: bold;
                    color: ${color};
                }
                .table-summary {
                    margin-top: 20px;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 6px;
                    text-align: center;
                    color: #666;
                }
                .no-data {
                    text-align: center;
                    padding: 40px;
                    color: #666;
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                    border-radius: 12px;
                    border: 2px dashed #dee2e6;
                    margin: 20px 0;
                }
                .no-data-icon {
                    font-size: 48px;
                    margin-bottom: 15px;
                    opacity: 0.7;
                }
                .no-data-message {
                    font-size: 18px;
                    font-weight: bold;
                    color: #495057;
                    margin-bottom: 10px;
                }
                .no-data-note {
                    font-size: 14px;
                    color: #6c757d;
                    font-style: italic;
                }

                .footer {
                    margin-top: 40px;
                    text-align: center;
                    font-size: 14px;
                    color: #999;
                    border-top: 1px solid #dee2e6;
                    padding-top: 20px;
                }

                @media print {
                    body { padding: 20px; }
                    .meetings-table { font-size: 12px; }
                    .meetings-table th, .meetings-table td { padding: 6px 4px; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="main-title">🏛️ نظام إدارة الاجتماعات</div>
                <div class="subtitle">📊 تقرير إحصائي مفصل - ${title}</div>
            </div>
            <div class="card">
                <div class="value">${value}</div>
                <div class="card-title">${title}</div>
                <div class="card-description">${description}</div>
            </div>
            ${meetingsTable}
            <div class="footer">
                <p><strong>📅 التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                <p><strong>🕐 الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                <p><strong>🏛️ المؤسسة:</strong> القوات المسلحة الأردنية</p>
            </div>
        </body>
        </html>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(printHTML);
    printWindow.document.close();
    setTimeout(() => { printWindow.print(); printWindow.close(); }, 500);
}

// دالة لتصدير PDF - محدثة مع الفلترة الذكية
function directExportToPDF(cardType) {
    console.log('📄 تصدير PDF مباشر للبطاقة مع الفلترة الذكية:', cardType);
    console.log('🔄 إصدار PDF: v2.0 - فلترة ذكية');

    let title = '', value = '', description = '', color = '#007bff';

    switch(cardType) {
        case 'total-meetings':
            title = 'إجمالي الاجتماعات';
            value = document.getElementById('totalMeetings')?.textContent?.trim() || '{{ total_meetings or 0 }}';
            description = 'العدد الكلي للاجتماعات المسجلة في النظام';
            color = '#007bff';
            console.log('📄 PDF - بيانات إجمالي الاجتماعات:', value);
            break;
        case 'upcoming-meetings':
            title = 'الاجتماعات القادمة';
            value = document.getElementById('upcomingMeetings')?.textContent?.trim() || '{{ upcoming_meetings or 0 }}';
            description = 'الاجتماعات المجدولة في المستقبل';
            color = '#28a745';
            break;
        case 'completed-meetings':
            title = 'الاجتماعات المكتملة';
            value = document.getElementById('completedMeetings')?.textContent?.trim() || '{{ completed_meetings or 0 }}';
            description = 'الاجتماعات التي تم عقدها بنجاح';
            color = '#17a2b8';
            break;
        case 'cancelled-meetings':
            title = 'الاجتماعات الملغية';
            value = document.getElementById('cancelledMeetings')?.textContent?.trim() || '{{ cancelled_meetings or 0 }}';
            description = 'الاجتماعات التي تم إلغاؤها';
            color = '#dc3545';
            break;
        case 'monthly-average':
            title = 'المتوسط الشهري';
            value = document.getElementById('monthlyAverage')?.textContent?.trim() || '{{ "%.1f"|format(avg_per_month or 0) }}';
            description = 'معدل الاجتماعات شهرياً';
            color = '#6f42c1';
            break;
        case 'most-active-month':
            title = 'أكثر الشهور نشاطاً';
            value = document.getElementById('mostActiveMonth')?.textContent?.trim() || '{{ most_active_month or "غير محدد" }}';
            description = 'الشهر الذي سجل أعلى عدد من الاجتماعات';
            color = '#fd7e14';
            break;
    }

    console.log('📊 البيانات للـ PDF:', { title, value, description, color });

    // إنشاء جدول الاجتماعات للـ PDF
    console.log('📄 إنشاء جدول PDF لنوع البطاقة:', cardType);
    const meetingsTable = createMeetingsTable(cardType);
    console.log('📄 تم إنشاء جدول PDF بنجاح');

    const pdfHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تصدير PDF - ${title}</title>
            <style>
                @page { size: A4; margin: 2cm; }
                body { font-family: Arial, sans-serif; direction: rtl; padding: 0; margin: 0; background: white; color: #333; }
                .pdf-container { max-width: 800px; margin: 0 auto; padding: 20px; }
                .header { text-align: center; margin-bottom: 50px; border-bottom: 4px solid ${color}; padding-bottom: 30px; background: #f8f9fa; border-radius: 12px; padding: 40px; }
                .main-title { font-size: 32px; font-weight: bold; color: ${color}; margin-bottom: 15px; }
                .subtitle { font-size: 18px; color: #666; font-weight: 500; }
                .card { background: #ffffff; border: 3px solid ${color}; border-radius: 16px; padding: 60px; margin: 40px 0; text-align: center; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
                .value { font-size: 72px; font-weight: bold; color: ${color}; margin-bottom: 30px; }
                .card-title { font-size: 28px; font-weight: bold; color: #333; margin-bottom: 20px; border-bottom: 2px solid #eee; padding-bottom: 15px; }
                .card-description { font-size: 18px; color: #666; line-height: 1.8; font-style: italic; }
                .stats-summary { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 30px 0; text-align: right; }
                .stats-summary h3 { color: ${color}; margin-bottom: 15px; font-size: 20px; }
                .footer { margin-top: 60px; text-align: center; font-size: 14px; color: #999; border-top: 2px solid #dee2e6; padding-top: 30px; background: #f8f9fa; border-radius: 8px; padding: 30px; }
                .footer-info { margin: 10px 0; font-weight: 500; }
                .footer-org { font-weight: bold; color: ${color}; font-size: 16px; margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class="pdf-container">
                <div class="header">
                    <div class="main-title">🏛️ نظام إدارة الاجتماعات</div>
                    <div class="subtitle">📄 تقرير PDF مفصل - ${title}</div>
                </div>
                <div class="card">
                    <div class="value">${value}</div>
                    <div class="card-title">${title}</div>
                    <div class="card-description">${description}</div>
                </div>
                <div class="stats-summary">
                    <h3>📊 ملخص الإحصائية</h3>
                    <p><strong>النوع:</strong> ${title}</p>
                    <p><strong>القيمة:</strong> ${value}</p>
                    <p><strong>الوصف:</strong> ${description}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p><strong>وقت الإنشاء:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                </div>

                ${meetingsTable}

                <div class="footer">
                    <div class="footer-info"><strong>📅 تاريخ التصدير:</strong> ${new Date().toLocaleDateString('ar-SA', { year: 'numeric', month: 'long', day: 'numeric' })}</div>
                    <div class="footer-info"><strong>🕐 وقت التصدير:</strong> ${new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</div>
                    <div class="footer-info"><strong>👤 المستخدم:</strong> مدير النظام</div>
                    <div class="footer-org">القوات المسلحة الأردنية - نظام إدارة الاجتماعات</div>
                </div>
            </div>
        </body>
        </html>
    `;

    const pdfWindow = window.open('', '_blank');
    pdfWindow.document.write(pdfHTML);
    pdfWindow.document.close();
    setTimeout(() => { pdfWindow.print(); }, 1000);
}

// إعادة تعريف الدوال الأصلية بقوة - استخدام القالب الموحد
window.printStatCard = function(cardType) {
    console.log('🖨️ طباعة البطاقة باستخدام القالب الموحد:', cardType);

    // استخدام وظيفة القالب الموحد إذا كانت متوفرة
    if (typeof window.printStatCard !== 'undefined' && window.printStatCard.name !== 'printStatCard') {
        // تجنب الاستدعاء المتكرر
        directPrintCard(cardType);
    } else {
        directPrintCard(cardType);
    }
};
window.exportStatCardToPDF = directExportToPDF;

// حل نهائي: إعادة ربط جميع الأزرار بقوة
setTimeout(function() {
    console.log('🔧 إعادة ربط جميع الأزرار بقوة...');

    // البحث عن جميع الأزرار في الصفحة
    const allButtons = document.querySelectorAll('button, a, .btn');

    allButtons.forEach(function(btn, index) {
        const onclick = btn.getAttribute('onclick') || '';
        const text = btn.textContent || '';
        const title = btn.getAttribute('title') || '';

        // إذا كان زر طباعة
        if (onclick.includes('printStatCard') || text.includes('طباعة') || title.includes('طباعة')) {
            console.log('🖨️ ربط زر طباعة:', index, onclick);

            // استخراج نوع البطاقة
            let cardType = 'total-meetings';
            if (onclick.includes('total-meetings')) cardType = 'total-meetings';
            else if (onclick.includes('upcoming-meetings')) cardType = 'upcoming-meetings';
            else if (onclick.includes('completed-meetings')) cardType = 'completed-meetings';
            else if (onclick.includes('cancelled-meetings')) cardType = 'cancelled-meetings';
            else if (onclick.includes('monthly-average')) cardType = 'monthly-average';
            else if (onclick.includes('most-active-month')) cardType = 'most-active-month';
            else {
                // تحديد النوع من موقع الزر
                const card = btn.closest('.col-md-4, .stat-card, .card');
                if (card) {
                    const cardIndex = Array.from(card.parentElement.children).indexOf(card);
                    const types = ['total-meetings', 'upcoming-meetings', 'completed-meetings', 'cancelled-meetings', 'monthly-average', 'most-active-month'];
                    cardType = types[cardIndex] || 'total-meetings';
                }
            }

            // إزالة الحدث القديم وإضافة الجديد
            btn.removeAttribute('onclick');
            btn.onclick = function(e) {
                e.preventDefault();
                console.log('🎯 طباعة البطاقة النهائية:', cardType);
                directPrintCard(cardType);
                return false;
            };
        }

        // إذا كان زر PDF
        else if (onclick.includes('exportStatCardToPDF') || text.includes('PDF') || title.includes('PDF')) {
            console.log('📄 ربط زر PDF:', index, onclick);

            // استخراج نوع البطاقة
            let cardType = 'total-meetings';
            if (onclick.includes('total-meetings')) cardType = 'total-meetings';
            else if (onclick.includes('upcoming-meetings')) cardType = 'upcoming-meetings';
            else if (onclick.includes('completed-meetings')) cardType = 'completed-meetings';
            else if (onclick.includes('cancelled-meetings')) cardType = 'cancelled-meetings';
            else if (onclick.includes('monthly-average')) cardType = 'monthly-average';
            else if (onclick.includes('most-active-month')) cardType = 'most-active-month';
            else {
                // تحديد النوع من موقع الزر
                const card = btn.closest('.col-md-4, .stat-card, .card');
                if (card) {
                    const cardIndex = Array.from(card.parentElement.children).indexOf(card);
                    const types = ['total-meetings', 'upcoming-meetings', 'completed-meetings', 'cancelled-meetings', 'monthly-average', 'most-active-month'];
                    cardType = types[cardIndex] || 'total-meetings';
                }
            }

            // إزالة الحدث القديم وإضافة الجديد
            btn.removeAttribute('onclick');
            btn.onclick = function(e) {
                e.preventDefault();
                console.log('🎯 تصدير PDF النهائي:', cardType);
                directExportToPDF(cardType);
                return false;
            };
        }
    });

    console.log('✅ تم ربط جميع الأزرار بنجاح');
}, 2000);

console.log('✅ تم تحميل الحل المباشر بنجاح');



// تحديث البيانات الحقيقية
function updateRealData() {
    // جلب البيانات من المتغيرات المرسلة من Python
    const totalMeetings = {{ total_meetings or 0 }};
    const completedMeetings = {{ completed_meetings or 0 }};
    const upcomingMeetings = {{ upcoming_meetings or 0 }};
    const cancelledMeetings = {{ cancelled_meetings or 0 }};
    const postponedMeetings = {{ postponed_meetings or 0 }};

    console.log('📊 البيانات الحقيقية:', {
        total: totalMeetings,
        completed: completedMeetings,
        upcoming: upcomingMeetings,
        cancelled: cancelledMeetings,
        postponed: postponedMeetings
    });

    // تحديث جدول التقدم
    updateProgressTable(totalMeetings, completedMeetings, upcomingMeetings, cancelledMeetings);

    // تحديث الرسم البياني
    // تعيين المتغيرات العامة أولاً
    window.completedMeetings = completedMeetings;
    window.upcomingMeetings = upcomingMeetings;
    window.cancelledMeetings = cancelledMeetings;

    updateChart();

    // تحديث جدول المشاركة حسب الجهة
    updateInvitingPartiesTable();
}

// تحديث جدول التقدم
function updateProgressTable(total, completed, upcoming, cancelled) {
    if (total === 0) {
        // إذا لم توجد اجتماعات، اعرض قيم افتراضية
        return;
    }

    const completedPercentage = Math.round((completed / total) * 100);
    const upcomingPercentage = Math.round((upcoming / total) * 100);
    const cancelledPercentage = Math.round((cancelled / total) * 100);

    // تحديث الصفوف
    const progressRows = document.querySelectorAll('.progress-table .progress-row');
    if (progressRows.length >= 4) {
        // صف المكتمل
        updateProgressRow(progressRows[0], 'مكتمل', completedPercentage, completed, 'green');

        // صف القادم (بدلاً من معلق)
        updateProgressRow(progressRows[1], 'قادم', upcomingPercentage, upcoming, 'blue');

        // صف الملغي
        updateProgressRow(progressRows[2], 'ملغي', cancelledPercentage, cancelled, 'red');

        // صف الإجمالي
        updateProgressRow(progressRows[3], 'الإجمالي', 100, total, 'green');
    }
}

// تحديث صف واحد في جدول التقدم
function updateProgressRow(row, label, percentage, count, colorClass) {
    const labelElement = row.querySelector('.progress-label');
    const progressBar = row.querySelector('.progress-bar-fill');
    const percentageElement = row.querySelector('.progress-percentage');
    const countElement = row.querySelector('.progress-count');

    if (labelElement) labelElement.textContent = label;
    if (progressBar) {
        progressBar.style.width = percentage + '%';
        progressBar.className = `progress-bar-fill ${colorClass}`;
    }
    if (percentageElement) percentageElement.textContent = percentage + '%';
    if (countElement) countElement.textContent = count;
}

// تحديث الرسم البياني
function updatePieChart(completed, upcoming, cancelled) {
    const total = completed + upcoming + cancelled;
    if (total === 0) {
        // إذا لم توجد اجتماعات، اعرض رسم بياني فارغ
        drawEmptyChart();
        return;
    }

    // تحديث البيانات
    const data = [
        { label: 'مكتمل', value: completed, color: '#28a745' },
        { label: 'قادم', value: upcoming, color: '#007bff' },
        { label: 'ملغي', value: cancelled, color: '#dc3545' }
    ].filter(item => item.value > 0); // إزالة العناصر التي قيمتها 0

    drawPieChartWithData(data, total);
    updateChartLegend(data, total);
}

// رسم الرسم البياني البسيط جداً
function drawPieChartWithData(data, total) {
    console.log('🎨 بدء رسم الرسم البياني:', data, total);

    const canvas = document.getElementById('meetingsChart');
    if (!canvas) {
        console.error('❌ لم يتم العثور على الكانفاس');
        return;
    }

    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 100;

    // مسح الكانفاس
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    console.log('🎨 مركز الرسم:', centerX, centerY, 'نصف القطر:', radius);

    // رسم دائرة بسيطة أولاً للاختبار
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.fillStyle = '#007bff';
    ctx.fill();
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 3;
    ctx.stroke();

    // نص في المنتصف
    ctx.fillStyle = '#fff';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(total, centerX, centerY);

    console.log('✅ تم رسم دائرة بسيطة للاختبار');
}

// رسم الجانب الجانبي للشريحة
function drawSliceSide(ctx, centerX, centerY, radius, depth, startAngle, sliceAngle, color) {
    const endAngle = startAngle + sliceAngle;

    // نقاط البداية والنهاية
    const startX = centerX + Math.cos(startAngle) * radius;
    const startY = centerY + Math.sin(startAngle) * radius;
    const endX = centerX + Math.cos(endAngle) * radius;
    const endY = centerY + Math.sin(endAngle) * radius;

    // رسم الجانب الأيسر
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(startX, startY);
    ctx.lineTo(startX, startY + depth);
    ctx.lineTo(centerX, centerY + depth);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // رسم الجانب الأيمن
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(endX, endY);
    ctx.lineTo(endX, endY + depth);
    ctx.lineTo(centerX, centerY + depth);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // رسم القوس الخارجي
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
    ctx.lineTo(endX, endY + depth);
    ctx.arc(centerX, centerY + depth, radius, endAngle, startAngle, true);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();
}

// دالة لتفتيح اللون
function lightenColor(color, amount) {
    const num = parseInt(color.replace("#", ""), 16);
    const amt = Math.round(2.55 * amount * 100);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

// دالة لتغميق اللون
function darkenColor(color, amount) {
    const num = parseInt(color.replace("#", ""), 16);
    const amt = Math.round(2.55 * amount * 100);
    const R = (num >> 16) - amt;
    const G = (num >> 8 & 0x00FF) - amt;
    const B = (num & 0x0000FF) - amt;
    return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
        (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
        (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
}

// تحديث قائمة الألوان
function updateChartLegend(data, total) {
    const legendContainer = document.querySelector('.chart-legend');
    if (!legendContainer) return;

    // مسح المحتوى الحالي
    legendContainer.innerHTML = '';

    // إضافة عناصر البيانات
    data.forEach(item => {
        const legendItem = document.createElement('div');
        legendItem.className = 'legend-item';
        legendItem.innerHTML = `
            <div class="legend-color" style="background: ${item.color};"></div>
            <span class="legend-label">${item.label}</span>
            <span class="legend-value">${item.value}</span>
        `;
        legendContainer.appendChild(legendItem);
    });

    // إضافة صف الإجمالي
    const totalItem = document.createElement('div');
    totalItem.className = 'legend-item total';
    totalItem.innerHTML = `
        <div class="legend-color" style="background: #6c757d;"></div>
        <span class="legend-label">الإجمالي</span>
        <span class="legend-value">${total}</span>
    `;
    legendContainer.appendChild(totalItem);
}

// رسم رسم بياني فارغ بسيط جداً
function drawEmptyChart() {
    console.log('🎨 رسم رسم بياني فارغ');

    const canvas = document.getElementById('meetingsChart');
    if (!canvas) {
        console.error('❌ لم يتم العثور على الكانفاس');
        return;
    }

    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 100;

    // مسح الكانفاس
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // رسم دائرة رمادية بسيطة
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.fillStyle = '#f8f9fa';
    ctx.fill();
    ctx.strokeStyle = '#dee2e6';
    ctx.lineWidth = 3;
    ctx.stroke();

    // نص "لا توجد بيانات"
    ctx.fillStyle = '#6c757d';
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('لا توجد بيانات', centerX, centerY);

    console.log('✅ تم رسم الرسم البياني الفارغ');
}

// تحديث الرسم البياني
function updateChart() {
    console.log('🔄 تحديث الرسم البياني...');
    console.log('📊 البيانات الحالية:', {
        completed: window.completedMeetings,
        upcoming: window.upcomingMeetings,
        cancelled: window.cancelledMeetings
    });

    const data = [
        { label: 'مكتملة', value: window.completedMeetings || 0, color: '#28a745' },
        { label: 'قادمة', value: window.upcomingMeetings || 0, color: '#007bff' },
        { label: 'ملغاة', value: window.cancelledMeetings || 0, color: '#dc3545' }
    ];

    const total = (window.completedMeetings || 0) + (window.upcomingMeetings || 0) + (window.cancelledMeetings || 0);

    console.log('📊 بيانات الرسم البياني:', data);
    console.log('📊 الإجمالي:', total);

    // التحقق من وجود الكانفاس
    const canvas = document.getElementById('meetingsChart');
    if (!canvas) {
        console.error('❌ لم يتم العثور على عنصر الكانفاس meetingsChart');
        return;
    }

    console.log('✅ تم العثور على الكانفاس:', canvas.width, 'x', canvas.height);

    if (total > 0) {
        console.log('🎨 رسم رسم بياني بالبيانات');
        drawPieChartWithData(data, total);
        updateChartLegend(data, total);
    } else {
        console.log('🎨 رسم رسم بياني فارغ');
        drawEmptyChart();
    }
}

// تحديث جدول المشاركة حسب الجهة
async function updateInvitingPartiesTable() {
    console.log('📊 تحديث جدول المشاركة حسب الجهة...');

    try {
        const response = await fetch('/api/reports/inviting-parties');
        const data = await response.json();

        if (data.success && data.data) {
            const partiesData = data.data.slice(0, 3); // أخذ أول 3 جهات
            const tableBody = document.querySelector('.progress-table:last-child .progress-table-body');

            if (tableBody) {
                // مسح المحتوى الحالي
                tableBody.innerHTML = '';

                // إضافة البيانات الحقيقية
                partiesData.forEach((party, index) => {
                    const colorClass = ['blue', 'green', 'orange'][index] || 'blue';

                    const row = document.createElement('div');
                    row.className = 'progress-row';
                    row.innerHTML = `
                        <div class="progress-label">${party.name}</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill ${colorClass}" style="width: ${party.percentage}%"></div>
                        </div>
                        <div class="progress-percentage">${party.percentage}%</div>
                        <div class="progress-count">${party.count}</div>
                    `;

                    tableBody.appendChild(row);
                });

                console.log('✅ تم تحديث جدول جهات الدعوة بنجاح');
            }
        } else {
            console.log('⚠️ لم يتم العثور على بيانات جهات الدعوة');
        }
    } catch (error) {
        console.error('❌ خطأ في جلب بيانات جهات الدعوة:', error);
    }
}

// دالة تحديث البيانات (للزر)
function refreshReports() {
    console.log('🔄 تحديث بيانات التقارير...');

    // إظهار مؤشر التحميل
    showLoadingIndicator('جاري تحديث البيانات...');

    // إعادة تحميل الصفحة للحصول على أحدث البيانات
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// إضافة تفاعل مع الرسم البياني
function addChartInteractivity() {
    const canvas = document.getElementById('meetingsChart');
    if (!canvas) return;

    let isAnimating = false;

    canvas.addEventListener('mouseenter', function() {
        if (!isAnimating) {
            isAnimating = true;
            canvas.style.transform = 'scale(1.05) rotateX(5deg)';
            canvas.style.transition = 'transform 0.3s ease';

            setTimeout(() => {
                isAnimating = false;
            }, 300);
        }
    });

    canvas.addEventListener('mouseleave', function() {
        canvas.style.transform = 'scale(1) rotateX(0deg)';
    });

    // إضافة تأثير النقر
    canvas.addEventListener('click', function(e) {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // تأثير الموجة عند النقر
        createRippleEffect(canvas, x, y);
    });
}

// تأثير الموجة
function createRippleEffect(canvas, x, y) {
    const ripple = document.createElement('div');
    ripple.style.position = 'absolute';
    ripple.style.left = (x - 10) + 'px';
    ripple.style.top = (y - 10) + 'px';
    ripple.style.width = '20px';
    ripple.style.height = '20px';
    ripple.style.borderRadius = '50%';
    ripple.style.background = 'rgba(0, 123, 255, 0.3)';
    ripple.style.transform = 'scale(0)';
    ripple.style.animation = 'ripple 0.6s ease-out';
    ripple.style.pointerEvents = 'none';
    ripple.style.zIndex = '1000';

    canvas.parentElement.style.position = 'relative';
    canvas.parentElement.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    #meetingsChart {
        cursor: pointer;
        transform-style: preserve-3d;
    }
`;
document.head.appendChild(style);

// تشغيل التحديثات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀🚀🚀 بدء تحميل صفحة التقارير...');

    // انتظار قليل للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        console.log('🔍 البحث عن الكانفاس...');
        const canvas = document.getElementById('meetingsChart');
        console.log('🔍 نتيجة البحث عن الكانفاس:', canvas);

        if (canvas) {
            console.log('✅✅✅ تم العثور على الكانفاس!');
            console.log('📏 أبعاد الكانفاس:', canvas.width, 'x', canvas.height);
            console.log('📍 موقع الكانفاس:', canvas.offsetLeft, canvas.offsetTop);

            // رسم اختبار فوري
            const ctx = canvas.getContext('2d');

            // مسح الكانفاس أولاً
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // رسم خلفية ملونة
            ctx.fillStyle = '#e3f2fd';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // رسم مربع أحمر
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(50, 50, 100, 100);

            // رسم دائرة زرقاء
            ctx.beginPath();
            ctx.arc(300, 200, 80, 0, 2 * Math.PI);
            ctx.fillStyle = '#007bff';
            ctx.fill();

            // رسم نص
            ctx.fillStyle = '#000000';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('اختبار الرسم', 200, 350);

            console.log('🎨🎨🎨 تم رسم عناصر الاختبار!');

            // تحديث البيانات بعد ثانيتين
            setTimeout(() => {
                console.log('⏰ بدء تحديث البيانات الحقيقية...');
                updateRealData();
            }, 2000);

        } else {
            console.error('❌❌❌ لم يتم العثور على الكانفاس!');
            console.log('🔍 جميع العناصر الموجودة:');
            const allElements = document.querySelectorAll('*');
            for (let i = 0; i < Math.min(10, allElements.length); i++) {
                console.log(`- ${allElements[i].tagName} id="${allElements[i].id}" class="${allElements[i].className}"`);
            }
        }
    }, 500);
});

// دالة تصدير PDF للتقرير الكامل
function generatePDFReport() {
    console.log('📄 بدء تصدير PDF للتقرير الكامل...');

    // إخفاء المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('pdfExportModal'));
    if (modal) modal.hide();

    // إظهار مؤشر التحميل
    showLoadingIndicator('جاري تصدير PDF...');

    // تحضير بيانات التقرير
    setTimeout(() => {
        hideLoadingIndicator();

        // الحصول على الرسم البياني
        const canvas = document.getElementById('meetingsChart');
        const chartImage = canvas ? canvas.toDataURL('image/png') : '';

        // بيانات التقرير
        const reportData = {
            completed: {{ completed_meetings or 5 }},
            upcoming: {{ upcoming_meetings or 3 }},
            cancelled: {{ cancelled_meetings or 2 }},
            total: {{ total_meetings or 10 }}
        };

        // إنشاء نافذة PDF للتقرير الكامل
        const pdfWindow = window.open('', '_blank');
        const fullReportHTML = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير الاجتماعات الشامل - PDF</title>
                <style>
                    @page { size: A4; margin: 1.5cm; }
                    body {
                        font-family: Arial, sans-serif;
                        direction: rtl;
                        padding: 0;
                        margin: 0;
                        background: white;
                        color: #333;
                        line-height: 1.6;
                    }
                    .pdf-container {
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                    }
                    .header {
                        background: linear-gradient(135deg, #1b4332, #2d5a3d);
                        color: white;
                        padding: 40px;
                        border-radius: 15px;
                        margin-bottom: 30px;
                        text-align: center;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                    }
                    .section {
                        background: white;
                        padding: 30px;
                        border-radius: 15px;
                        margin: 20px 0;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        border: 1px solid #dee2e6;
                    }
                    .stats-overview {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        gap: 20px;
                        margin: 20px 0;
                    }
                    .stat-box {
                        background: #f8f9fa;
                        padding: 25px;
                        border-radius: 12px;
                        text-align: center;
                        border: 2px solid #dee2e6;
                    }
                    .stat-box.total { border-color: #6f42c1; background: rgba(111, 66, 193, 0.1); }
                    .stat-box.completed { border-color: #28a745; background: rgba(40, 167, 69, 0.1); }
                    .stat-box.upcoming { border-color: #007bff; background: rgba(0, 123, 255, 0.1); }
                    .stat-box.cancelled { border-color: #dc3545; background: rgba(220, 53, 69, 0.1); }
                    .stat-number {
                        font-size: 2.5rem;
                        font-weight: bold;
                        margin: 10px 0;
                    }
                    .chart-section {
                        text-align: center;
                        margin: 30px 0;
                    }
                    .chart-image {
                        max-width: 100%;
                        height: auto;
                        border-radius: 10px;
                        margin: 20px 0;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                    }
                    .summary-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        background: white;
                        border-radius: 10px;
                        overflow: hidden;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    }
                    .summary-table th,
                    .summary-table td {
                        padding: 15px;
                        text-align: center;
                        border-bottom: 1px solid #dee2e6;
                    }
                    .summary-table th {
                        background: #f8f9fa;
                        font-weight: bold;
                        color: #333;
                    }
                    .footer {
                        margin-top: 40px;
                        padding: 30px;
                        background: #f8f9fa;
                        border-radius: 15px;
                        border: 1px solid #dee2e6;
                        text-align: center;
                    }
                    .footer-info {
                        margin: 8px 0;
                        font-size: 14px;
                        color: #555;
                    }
                    .footer-org {
                        margin-top: 20px;
                        font-weight: bold;
                        color: #1b4332;
                        font-size: 16px;
                    }
                    h1, h2, h3 { color: #1b4332; }
                    .page-break { page-break-before: always; }
                </style>
            </head>
            <body>
                <div class="pdf-container">
                    <!-- صفحة الغلاف -->
                    <div class="header">
                        <h1>🏛️ القوات المسلحة الأردنية</h1>
                        <h2>📊 التقرير الشامل للاجتماعات</h2>
                        <p>تقرير مفصل عن إحصائيات وتوزيع الاجتماعات</p>
                        <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 20px 0;">
                        <p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
                    </div>

                    <!-- ملخص الإحصائيات -->
                    <div class="section">
                        <h3>📈 ملخص الإحصائيات العامة</h3>
                        <div class="stats-overview">
                            <div class="stat-box total">
                                <h4>📊 إجمالي الاجتماعات</h4>
                                <div class="stat-number">${reportData.total}</div>
                                <p>العدد الكلي للاجتماعات</p>
                            </div>
                            <div class="stat-box completed">
                                <h4>✅ الاجتماعات المكتملة</h4>
                                <div class="stat-number">${reportData.completed}</div>
                                <p>${Math.round((reportData.completed / reportData.total) * 100)}% من الإجمالي</p>
                            </div>
                            <div class="stat-box upcoming">
                                <h4>📅 الاجتماعات القادمة</h4>
                                <div class="stat-number">${reportData.upcoming}</div>
                                <p>${Math.round((reportData.upcoming / reportData.total) * 100)}% من الإجمالي</p>
                            </div>
                            <div class="stat-box cancelled">
                                <h4>❌ الاجتماعات الملغاة</h4>
                                <div class="stat-number">${reportData.cancelled}</div>
                                <p>${Math.round((reportData.cancelled / reportData.total) * 100)}% من الإجمالي</p>
                            </div>
                        </div>
                    </div>

                    <!-- الرسم البياني -->
                    ${chartImage ? `
                    <div class="section">
                        <h3>📊 الرسم البياني - توزيع الاجتماعات</h3>
                        <div class="chart-section">
                            <img src="${chartImage}" alt="الرسم البياني" class="chart-image">
                            <p><em>الرسم البياني الدائري يوضح توزيع الاجتماعات حسب الحالة</em></p>
                        </div>
                    </div>
                    ` : ''}

                    <!-- جدول التفاصيل -->
                    <div class="section">
                        <h3>📋 جدول التفاصيل</h3>
                        <table class="summary-table">
                            <thead>
                                <tr>
                                    <th>نوع الاجتماع</th>
                                    <th>العدد</th>
                                    <th>النسبة المئوية</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>الاجتماعات المكتملة</td>
                                    <td>${reportData.completed}</td>
                                    <td>${Math.round((reportData.completed / reportData.total) * 100)}%</td>
                                    <td style="color: #28a745; font-weight: bold;">✅ مكتمل</td>
                                </tr>
                                <tr>
                                    <td>الاجتماعات القادمة</td>
                                    <td>${reportData.upcoming}</td>
                                    <td>${Math.round((reportData.upcoming / reportData.total) * 100)}%</td>
                                    <td style="color: #007bff; font-weight: bold;">📅 مجدول</td>
                                </tr>
                                <tr>
                                    <td>الاجتماعات الملغاة</td>
                                    <td>${reportData.cancelled}</td>
                                    <td>${Math.round((reportData.cancelled / reportData.total) * 100)}%</td>
                                    <td style="color: #dc3545; font-weight: bold;">❌ ملغي</td>
                                </tr>
                                <tr style="background: #f8f9fa; font-weight: bold;">
                                    <td>الإجمالي</td>
                                    <td>${reportData.total}</td>
                                    <td>100%</td>
                                    <td style="color: #6f42c1;">📊 شامل</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- معلومات التقرير -->
                    <div class="footer">
                        <div class="footer-info"><strong>📅 تاريخ إنشاء التقرير:</strong> ${new Date().toLocaleDateString('ar-SA', { year: 'numeric', month: 'long', day: 'numeric' })}</div>
                        <div class="footer-info"><strong>🕐 وقت إنشاء التقرير:</strong> ${new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</div>
                        <div class="footer-info"><strong>👤 المستخدم:</strong> مدير النظام</div>
                        <div class="footer-info"><strong>🖥️ النظام:</strong> نظام إدارة الاجتماعات</div>
                        <div class="footer-org">القوات المسلحة الأردنية</div>
                        <div style="margin-top: 15px; font-size: 12px; color: #888;">
                            هذا التقرير تم إنشاؤه تلقائياً بواسطة نظام إدارة الاجتماعات
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `;

        pdfWindow.document.write(fullReportHTML);
        pdfWindow.document.close();

        // عرض تعليمات PDF وطباعة
        setTimeout(() => {
            // إظهار رسالة نجاح
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'تم تحضير التقرير!',
                    text: 'سيتم فتح نافذة الطباعة لحفظ التقرير كـ PDF',
                    icon: 'success',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#28a745'
                });
            } else {
                alert('✅ تم تحضير التقرير!\n\nسيتم فتح نافذة الطباعة لحفظ التقرير كـ PDF');
            }

            // فتح نافذة الطباعة
            pdfWindow.print();
        }, 1000);

        console.log('✅ تم تحضير التقرير الشامل لتصدير PDF');
    }, 2000);
}

// دالة إظهار مؤشر التحميل
function showLoadingIndicator(message = 'جاري التحميل...') {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
        indicator.querySelector('div div').textContent = message;
        indicator.style.display = 'block';
    }
}

// دالة إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}
</script>

<!-- PDF Export Modal - مثل الصورة الثالثة -->
<div class="modal fade" id="pdfExportModal" tabindex="-1" aria-labelledby="pdfExportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
            <div class="modal-header" style="background: var(--jaf-gradient); color: white; border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" id="pdfExportModalLabel">
                    <i class="fas fa-file-pdf me-2"></i>
                    تصدير PDF
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <div class="text-center mb-3">
                    <div style="font-size: 3rem; color: #dc3545; margin-bottom: 1rem;">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <h6 style="color: #333; font-weight: 600;">تصدير التقرير بصيغة PDF</h6>
                    <p style="color: #6c757d; font-size: 0.9rem; margin-bottom: 1.5rem;">
                        سيتم تصدير التقرير الحالي مع جميع الإحصائيات والرسوم البيانية
                    </p>
                </div>

                <div class="export-options mb-3">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" value="" id="includeCharts" checked>
                        <label class="form-check-label" for="includeCharts">
                            <i class="fas fa-chart-bar me-2 text-primary"></i>
                            تضمين الرسوم البيانية
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" value="" id="includeStats" checked>
                        <label class="form-check-label" for="includeStats">
                            <i class="fas fa-calculator me-2 text-success"></i>
                            تضمين الإحصائيات التفصيلية
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="includeTables" checked>
                        <label class="form-check-label" for="includeTables">
                            <i class="fas fa-table me-2 text-info"></i>
                            تضمين الجداول
                        </label>
                    </div>
                </div>

                <div class="alert alert-info" style="background: rgba(13, 202, 240, 0.1); border: 1px solid rgba(13, 202, 240, 0.2); border-radius: 10px;">
                    <i class="fas fa-info-circle me-2"></i>
                    <small>سيتم تحميل الملف تلقائياً بعد الإنتهاء من التصدير</small>
                </div>
            </div>
            <div class="modal-footer" style="border: none; padding: 1rem 2rem 2rem;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="border-radius: 25px; padding: 0.5rem 1.5rem;">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-danger" onclick="generatePDFReport()" style="border-radius: 25px; padding: 0.5rem 1.5rem; background: linear-gradient(135deg, #dc3545, #c82333); border: none;">
                    <i class="fas fa-download me-1"></i>
                    تحميل PDF
                </button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript للرسم البياني الشهري -->
<script>
// رسم الرسم البياني الشهري
function drawMonthlyChart() {
    console.log('📊 بدء رسم الرسم البياني الشهري...');

    const canvas = document.getElementById('monthlyChart');
    if (!canvas) {
        console.error('❌ لم يتم العثور على عنصر الرسم البياني الشهري');
        return;
    }

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // مسح الكانفاس
    ctx.clearRect(0, 0, width, height);

    // الحصول على التاريخ الحالي
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // 0-11
    const currentYear = currentDate.getFullYear();

    // أسماء الأشهر
    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    // بيانات وهمية للأشهر المنتهية فقط (يمكن استبدالها ببيانات حقيقية من API)
    const monthlyData = [];
    for (let i = 0; i <= currentMonth; i++) {
        monthlyData.push({
            month: monthNames[i],
            meetings: Math.floor(Math.random() * 15) + 5, // بيانات عشوائية للاختبار
            completed: Math.floor(Math.random() * 10) + 2,
            cancelled: Math.floor(Math.random() * 3) + 1
        });
    }

    console.log('📊 بيانات الأشهر:', monthlyData);

    // إعدادات الرسم
    const padding = 60;
    const chartWidth = width - (padding * 2);
    const chartHeight = height - (padding * 2);
    const barWidth = chartWidth / monthlyData.length;
    const maxMeetings = Math.max(...monthlyData.map(d => d.meetings));

    // رسم الخلفية
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);

    // رسم الشبكة
    ctx.strokeStyle = '#e9ecef';
    ctx.lineWidth = 1;

    // خطوط أفقية
    for (let i = 0; i <= 5; i++) {
        const y = padding + (chartHeight / 5) * i;
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();

        // تسميات المحور Y
        ctx.fillStyle = '#6c757d';
        ctx.font = '12px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(Math.round(maxMeetings - (maxMeetings / 5) * i), padding - 10, y + 4);
    }

    // رسم الأعمدة
    monthlyData.forEach((data, index) => {
        const x = padding + (barWidth * index) + (barWidth * 0.1);
        const barHeight = (data.meetings / maxMeetings) * chartHeight;
        const y = height - padding - barHeight;
        const actualBarWidth = barWidth * 0.8;

        // رسم العمود الرئيسي
        const gradient = ctx.createLinearGradient(0, y, 0, y + barHeight);
        gradient.addColorStop(0, '#1B4332');
        gradient.addColorStop(1, '#2D5A3D');

        ctx.fillStyle = gradient;
        ctx.fillRect(x, y, actualBarWidth, barHeight);

        // رسم حدود العمود
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, actualBarWidth, barHeight);

        // رسم القيمة فوق العمود
        ctx.fillStyle = '#1B4332';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(data.meetings, x + actualBarWidth / 2, y - 10);

        // رسم اسم الشهر
        ctx.fillStyle = '#495057';
        ctx.font = '12px Arial';
        ctx.save();
        ctx.translate(x + actualBarWidth / 2, height - padding + 20);
        ctx.rotate(-Math.PI / 6); // دوران 30 درجة
        ctx.textAlign = 'center';
        ctx.fillText(data.month, 0, 0);
        ctx.restore();
    });

    // رسم العنوان
    ctx.fillStyle = '#1B4332';
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`إحصائيات الاجتماعات الشهرية - ${currentYear}`, width / 2, 30);

    // رسم تسمية المحور Y
    ctx.save();
    ctx.translate(20, height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillStyle = '#6c757d';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('عدد الاجتماعات', 0, 0);
    ctx.restore();

    console.log('✅ تم رسم الرسم البياني الشهري بنجاح');
}

// طباعة الرسم البياني الشهري
function printMonthlyChart() {
    console.log('🖨️ طباعة الرسم البياني الشهري...');

    const canvas = document.getElementById('monthlyChart');
    if (!canvas) {
        alert('❌ لم يتم العثور على الرسم البياني الشهري');
        return;
    }

    const chartImage = canvas.toDataURL('image/png');
    const printWindow = window.open('', '_blank');

    const printHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>طباعة الرسم البياني الشهري</title>
            <style>
                @page { size: A4 landscape; margin: 2cm; }
                body {
                    font-family: Arial, sans-serif;
                    direction: rtl;
                    text-align: center;
                    padding: 20px;
                    margin: 0;
                }
                .header {
                    background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                }
                .chart-container {
                    margin: 30px 0;
                    padding: 20px;
                    border: 2px solid #dee2e6;
                    border-radius: 15px;
                    background: #f8f9fa;
                }
                .chart-image {
                    max-width: 100%;
                    height: auto;
                    border-radius: 10px;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🏛️ القوات المسلحة الأردنية</h1>
                <h2>📊 الإحصائيات الشهرية للاجتماعات</h2>
                <p>الرسم البياني الشهري للعام الحالي</p>
            </div>
            <div class="chart-container">
                <h3>📈 توزيع الاجتماعات حسب الأشهر</h3>
                <img src="${chartImage}" alt="الرسم البياني الشهري" class="chart-image">
            </div>
            <script>
                window.onload = function() {
                    window.print();
                    setTimeout(() => window.close(), 1000);
                };
            </script>
        </body>
        </html>
    `;

    printWindow.document.write(printHTML);
    printWindow.document.close();

    console.log('✅ تم إرسال الرسم البياني الشهري للطباعة');
}

// تصدير الرسم البياني الشهري كـ PDF
function exportMonthlyChartToPDF() {
    console.log('📄 تصدير الرسم البياني الشهري كـ PDF...');

    const canvas = document.getElementById('monthlyChart');
    if (!canvas) {
        alert('❌ لم يتم العثور على الرسم البياني الشهري');
        return;
    }

    const chartImage = canvas.toDataURL('image/png');
    const pdfWindow = window.open('', '_blank');

    const pdfHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تصدير PDF - الرسم البياني الشهري</title>
            <style>
                @page { size: A4 landscape; margin: 1.5cm; }
                body {
                    font-family: Arial, sans-serif;
                    direction: rtl;
                    text-align: center;
                    padding: 0;
                    margin: 0;
                    background: white;
                    color: #333;
                }
                .header {
                    background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
                    color: white;
                    padding: 40px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                }
                .chart-section {
                    background: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin: 20px 0;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    border: 1px solid #dee2e6;
                }
                .chart-image {
                    max-width: 100%;
                    height: auto;
                    border-radius: 10px;
                    margin: 20px 0;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🏛️ القوات المسلحة الأردنية</h1>
                <h2>📊 تقرير الإحصائيات الشهرية - PDF</h2>
                <p>الرسم البياني الشهري للاجتماعات</p>
            </div>
            <div class="chart-section">
                <h3>📈 الرسم البياني - الإحصائيات الشهرية</h3>
                <img src="${chartImage}" alt="الرسم البياني الشهري" class="chart-image">
            </div>
            <script>
                setTimeout(() => {
                    alert('📄 لحفظ الملف كـ PDF:\\n\\n1. اضغط Ctrl+P أو Cmd+P\\n2. اختر "حفظ كـ PDF" من قائمة الطابعات\\n3. اضغط "حفظ"\\n\\n✅ ستفتح نافذة الطباعة تلقائياً');
                    window.print();
                }, 1000);
            </script>
        </body>
        </html>
    `;

    pdfWindow.document.write(pdfHTML);
    pdfWindow.document.close();

    console.log('✅ تم تحضير الرسم البياني الشهري لتصدير PDF');
}

// دوال طباعة البطاقات الإحصائية
function printStatCard(cardType) {
    console.log('🖨️ طباعة بطاقة:', cardType);

    let cardData = {};
    let cardTitle = '';
    let cardIcon = '';
    let cardColor = '';

    switch(cardType) {
        case 'total-meetings':
            cardData = {
                number: document.getElementById('totalMeetings')?.textContent || '0',
                label: 'إجمالي الاجتماعات',
                description: 'العدد الكلي للاجتماعات المسجلة في النظام'
            };
            cardTitle = 'إجمالي الاجتماعات';
            cardIcon = 'fas fa-calendar-check';
            cardColor = '#1B4332';
            break;
        case 'upcoming-meetings':
            cardData = {
                number: document.getElementById('upcomingMeetings')?.textContent || '0',
                label: 'الاجتماعات القادمة',
                description: 'الاجتماعات المجدولة في المستقبل'
            };
            cardTitle = 'الاجتماعات القادمة';
            cardIcon = 'fas fa-clock';
            cardColor = '#28a745';
            break;
        case 'completed-meetings':
            cardData = {
                number: document.getElementById('completedMeetings')?.textContent || '0',
                label: 'الاجتماعات المكتملة',
                description: 'الاجتماعات التي تم عقدها بنجاح'
            };
            cardTitle = 'الاجتماعات المكتملة';
            cardIcon = 'fas fa-check-double';
            cardColor = '#007bff';
            break;
        case 'cancelled-meetings':
            cardData = {
                number: document.getElementById('cancelledMeetings')?.textContent || '0',
                label: 'الاجتماعات الملغية',
                description: 'الاجتماعات التي تم إلغاؤها'
            };
            cardTitle = 'الاجتماعات الملغية';
            cardIcon = 'fas fa-ban';
            cardColor = '#dc3545';
            break;
        case 'monthly-average':
            cardData = {
                number: document.getElementById('monthlyAverage')?.textContent || '0',
                label: 'المتوسط الشهري',
                description: 'معدل عدد الاجتماعات شهرياً',
                unit: 'شهرياً'
            };
            cardTitle = 'المتوسط الشهري';
            cardIcon = 'fas fa-chart-line';
            cardColor = '#6f42c1';
            break;
        case 'most-active-month':
            cardData = {
                number: document.getElementById('mostActiveMonth')?.textContent || 'يناير',
                label: 'أكثر الشهور نشاطاً',
                description: document.getElementById('mostActiveMonth')?.getAttribute('data-month-count') + ' اجتماع مسجل' || '0 اجتماع مسجل'
            };
            cardTitle = 'أكثر الشهور نشاطاً';
            cardIcon = 'fas fa-trophy';
            cardColor = '#ffc107';
            break;
    }

    const printWindow = window.open('', '_blank');
    const printHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>طباعة ${cardTitle}</title>
            <style>
                @page { size: A4; margin: 2cm; }
                body {
                    font-family: Arial, sans-serif;
                    direction: rtl;
                    text-align: center;
                    padding: 20px;
                    margin: 0;
                    background: #f8f9fa;
                }
                .header {
                    background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                }
                .card-container {
                    background: white;
                    border-radius: 20px;
                    padding: 40px;
                    margin: 30px auto;
                    max-width: 500px;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e9ecef;
                }
                .card-icon {
                    width: 80px;
                    height: 80px;
                    background: ${cardColor};
                    border-radius: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 2rem;
                    color: white;
                    margin: 0 auto 20px;
                }
                .card-number {
                    font-size: 4rem;
                    font-weight: 700;
                    color: ${cardColor};
                    margin: 20px 0;
                    line-height: 1;
                }
                .card-label {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #495057;
                    margin-bottom: 10px;
                }
                .card-description {
                    font-size: 1rem;
                    color: #6c757d;
                    margin-bottom: 20px;
                }
                .print-date {
                    font-size: 0.9rem;
                    color: #6c757d;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #e9ecef;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🏛️ القوات المسلحة الأردنية</h1>
                <h2>📊 تقرير ${cardTitle}</h2>
                <p>بطاقة إحصائية مفصلة</p>
            </div>
            <div class="card-container">
                <div class="card-icon">
                    <i class="${cardIcon}"></i>
                </div>
                <div class="card-number">${cardData.number}${cardData.unit ? ' ' + cardData.unit : ''}</div>
                <div class="card-label">${cardData.label}</div>
                <div class="card-description">${cardData.description}</div>
                <div class="print-date">
                    تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}
                </div>
            </div>
            <script>
                window.onload = function() {
                    window.print();
                    setTimeout(() => window.close(), 1000);
                };
            </script>
        </body>
        </html>
    `;

    printWindow.document.write(printHTML);
    printWindow.document.close();

    console.log('✅ تم إرسال بطاقة', cardTitle, 'للطباعة');
}

// دالة تصدير البطاقات كـ PDF
function exportStatToPDF(cardType) {
    console.log('📄 تصدير بطاقة كـ PDF:', cardType);

    // استخدام نفس منطق الطباعة مع تعديل العنوان
    printStatCard(cardType);

    setTimeout(() => {
        alert('📄 لحفظ البطاقة كـ PDF:\\n\\n1. اضغط Ctrl+P أو Cmd+P\\n2. اختر "حفظ كـ PDF" من قائمة الطابعات\\n3. اضغط "حفظ"\\n\\n✅ ستفتح نافذة الطباعة تلقائياً');
    }, 500);

    console.log('✅ تم تحضير بطاقة', cardType, 'لتصدير PDF');
}

// رسم الرسم البياني الشهري عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل صفحة التقارير...');

    // رسم الرسم البياني الشهري
    setTimeout(() => {
        console.log('📊 رسم الرسم البياني الشهري...');
        drawMonthlyChart();
    }, 1000);

    // رسم الرسم البياني الدائري
    setTimeout(() => {
        console.log('🎯 رسم الرسم البياني الدائري...');
        const canvas = document.getElementById('meetingsChart');
        if (canvas) {
            console.log('✅ تم العثور على الرسم البياني الدائري');
            // تحديث البيانات أولاً
            updateRealData();
        } else {
            console.log('❌ لم يتم العثور على الرسم البياني الدائري');
        }
    }, 1500);

    // إضافة تفاعل مع الرسم البياني
    setTimeout(() => {
        addChartInteractivity();
    }, 2000);
});
</script>

{% endblock %}
