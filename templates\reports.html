{% extends "base.html" %}

{% block title %}التقارير والإحصائيات{% endblock %}

{% block extra_css %}
<style>
    /* البطاقات الإحصائية الستة */
    .stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 35px rgba(0,0,0,0.15);
    }

    .stat-card.bg-primary { border-left: 5px solid #007bff; }
    .stat-card.bg-success { border-left: 5px solid #28a745; }
    .stat-card.bg-info { border-left: 5px solid #17a2b8; }
    .stat-card.bg-danger { border-left: 5px solid #dc3545; }
    .stat-card.bg-warning { border-left: 5px solid #ffc107; }
    .stat-card.bg-purple { border-left: 5px solid #6f42c1; }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
        margin-left: 15px;
    }

    .stat-card.bg-primary .stat-icon { color: #007bff; }
    .stat-card.bg-success .stat-icon { color: #28a745; }
    .stat-card.bg-info .stat-icon { color: #17a2b8; }
    .stat-card.bg-danger .stat-icon { color: #dc3545; }
    .stat-card.bg-warning .stat-icon { color: #ffc107; }
    .stat-card.bg-purple .stat-icon { color: #6f42c1; }

    .stat-content {
        flex: 1;
        text-align: right;
    }

    .stat-content h3 {
        font-size: 2rem;
        font-weight: bold;
        margin: 0 0 5px 0;
        color: #333;
    }

    .stat-content p {
        font-size: 0.9rem;
        color: #666;
        margin: 0;
        font-weight: 500;
    }

    /* تصميم البطاقات الجديدة */
    .chart-card, .monthly-table-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .chart-card:hover, .monthly-table-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .chart-card::before, .monthly-table-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #28a745, #007bff, #dc3545);
    }

    /* رأس البطاقات */
    .chart-header, .table-header {
        padding: 20px 25px;
        background: linear-gradient(135deg, #1b4332 0%, #2d5a3d 100%);
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chart-header h4, .table-header h4 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
    }

    .chart-controls {
        display: flex;
        gap: 8px;
    }

    .chart-controls .btn {
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        transition: all 0.3s ease;
    }

    .chart-controls .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    /* جسم البطاقات */
    .chart-body, .table-body {
        padding: 25px;
    }

    /* الرسم البياني ثلاثي الأبعاد */
    .chart-container-3d {
        position: relative;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: radial-gradient(circle at center, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.5) 100%);
        border-radius: 15px;
        margin-bottom: 20px;
        overflow: hidden;
    }

    .chart-container-3d::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, rgba(40,167,69,0.1), rgba(0,123,255,0.1), rgba(220,53,69,0.1), rgba(40,167,69,0.1));
        animation: rotate 30s linear infinite;
        pointer-events: none;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    #meetings3DChart {
        position: relative;
        z-index: 2;
        filter: drop-shadow(0 8px 16px rgba(0,0,0,0.15));
    }

    /* الأسطورة الحديثة */
    .chart-legend-modern {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin-top: 20px;
    }

    .legend-item-modern {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px;
        background: rgba(255,255,255,0.8);
        border-radius: 12px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .legend-item-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    }

    .legend-item-modern.completed {
        border-color: #28a745;
    }

    .legend-item-modern.upcoming {
        border-color: #007bff;
    }

    .legend-item-modern.cancelled {
        border-color: #dc3545;
    }

    .legend-dot {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-bottom: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .legend-item-modern.completed .legend-dot {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .legend-item-modern.upcoming .legend-dot {
        background: linear-gradient(135deg, #007bff, #6610f2);
    }

    .legend-item-modern.cancelled .legend-dot {
        background: linear-gradient(135deg, #dc3545, #e83e8c);
    }

    .legend-item-modern span {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 5px;
    }

    .legend-item-modern strong {
        font-size: 1.4rem;
        font-weight: 700;
        color: #333;
    }

    /* الجدول الشهري */
    .year-selector select {
        background: linear-gradient(135deg, #b8860b, #daa520);
        border: 2px solid #8b6914;
        border-radius: 10px;
        color: white;
        padding: 10px 18px;
        font-size: 1rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(184, 134, 11, 0.4);
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        min-width: 80px;
    }

    .year-selector select:hover {
        background: linear-gradient(135deg, #9a7209, #b8860b);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(184, 134, 11, 0.5);
        border-color: #6b4e03;
    }

    .year-selector select:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(218, 165, 32, 0.3);
    }

    /* أزرار الطباعة وتصدير PDF */
    .header-controls {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .year-selector {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
    }

    .btn-print, .btn-pdf {
        padding: 8px 16px;
        border: none;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    .btn-print {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    .btn-print:hover {
        background: linear-gradient(135deg, #218838, #1ea085);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(40,167,69,0.3);
    }

    .btn-pdf {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
    }

    .btn-pdf:hover {
        background: linear-gradient(135deg, #c82333, #a71e2a);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220,53,69,0.3);
    }

    /* إشعارات النجاح والخطأ */
    .success-notification, .error-notification {
        position: fixed;
        top: 20px;
        left: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        font-size: 14px;
        z-index: 9999;
        animation: slideIn 0.3s ease-out;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        min-width: 300px;
        max-width: 500px;
        text-align: right;
        direction: rtl;
    }

    .success-notification {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .error-notification {
        background: linear-gradient(135deg, #dc3545, #e74c3c);
    }

    @keyframes slideIn {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(-100%);
            opacity: 0;
        }
    }

    /* تحسين مظهر صف "لا توجد بيانات" */
    .no-data-row {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        margin: 10px 0;
    }

    .loading-row {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border-radius: 8px;
        animation: pulse 1.5s ease-in-out infinite alternate;
    }

    @keyframes pulse {
        from { opacity: 0.6; }
        to { opacity: 1; }
    }

    /* تأثير انتقالي للرسم البياني */
    #meetings3DChart {
        transition: opacity 0.3s ease-in-out;
    }

    /* تحسين عرض الصفوف الفارغة */
    .empty-month {
        opacity: 0.6;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    }

    .empty-badge {
        background: linear-gradient(135deg, #6c757d, #495057) !important;
        opacity: 0.7;
    }

    .monthly-stats-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table-header-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
        gap: 15px;
        padding: 15px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }

    .table-rows {
        max-height: 300px;
        overflow-y: auto;
    }

    .table-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
        gap: 15px;
        padding: 15px 20px;
        border-bottom: 1px solid #f1f3f4;
        transition: all 0.3s ease;
        align-items: center;
    }

    .table-row:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        transform: translateX(5px);
    }

    .table-row.high-performance {
        background: linear-gradient(135deg, rgba(40,167,69,0.1) 0%, rgba(40,167,69,0.05) 100%);
        border-left: 4px solid #28a745;
    }

    .table-row.low-performance {
        background: linear-gradient(135deg, rgba(220,53,69,0.1) 0%, rgba(220,53,69,0.05) 100%);
        border-left: 4px solid #dc3545;
    }

    .col-month {
        font-weight: 600;
        color: #333;
    }

    .count-badge {
        display: inline-block;
        background: linear-gradient(135deg, #1b4332, #2d5a3d);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .percentage-bar {
        position: relative;
        background: #e9ecef;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
    }

    .percentage-fill {
        height: 100%;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 10px;
        transition: width 0.8s ease;
        position: relative;
    }

    .percentage-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 0.8rem;
        font-weight: 600;
        color: #333;
        z-index: 2;
    }

    .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-badge.completed {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    /* تحسينات الاستجابة */
    @media (max-width: 768px) {
        .chart-legend-modern {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .legend-item-modern {
            flex-direction: row;
            justify-content: space-between;
            padding: 12px;
        }

        .legend-dot {
            margin-bottom: 0;
            margin-left: 10px;
        }

        .table-header-row, .table-row {
            grid-template-columns: 1fr;
            gap: 10px;
            text-align: center;
        }

        .chart-container-3d {
            height: 300px;
        }

        #meetings3DChart {
            width: 300px !important;
            height: 300px !important;
        }
    }

    @media (max-width: 576px) {
        .chart-body, .table-body {
            padding: 15px;
        }

        .chart-header, .table-header {
            padding: 15px 20px;
        }

        .chart-container-3d {
            height: 250px;
        }

        #meetings3DChart {
            width: 250px !important;
            height: 250px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-primary mb-1">
                <i class="fas fa-chart-bar me-2"></i>
                التقارير والإحصائيات
            </h2>
            <p class="text-muted mb-0">تقارير شاملة وإحصائيات تفصيلية للاجتماعات</p>
        </div>
    </div>

    <!-- البطاقات الإحصائية الستة -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card bg-primary">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalMeetings">{{ total_meetings or 0 }}</h3>
                    <p>إجمالي الاجتماعات</p>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card bg-success">
                <div class="stat-icon">
                    <i class="fas fa-check-double"></i>
                </div>
                <div class="stat-content">
                    <h3 id="completedMeetings">{{ completed_meetings or 0 }}</h3>
                    <p>الاجتماعات المكتملة</p>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card bg-info">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3 id="upcomingMeetings">{{ upcoming_meetings or 0 }}</h3>
                    <p>الاجتماعات القادمة</p>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card bg-danger">
                <div class="stat-icon">
                    <i class="fas fa-ban"></i>
                </div>
                <div class="stat-content">
                    <h3 id="cancelledMeetings">{{ cancelled_meetings or 0 }}</h3>
                    <p>الاجتماعات الملغية</p>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card bg-warning">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content">
                    <h3 id="monthlyAverage">{{ monthly_average or 0 }}</h3>
                    <p>المتوسط الشهري</p>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card bg-purple">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-content">
                    <h3 id="mostActiveMonth">{{ most_active_month or 'يناير' }}</h3>
                    <p>أكثر الشهور نشاطاً</p>
                </div>
            </div>
        </div>
    </div>

    <!-- التصميم الجديد المحسن - رسم بياني ثلاثي الأبعاد وجدول شهري -->
    <div class="row mb-4">
        <!-- الرسم البياني الدائري ثلاثي الأبعاد -->
        <div class="col-lg-6 mb-3">
            <div class="chart-card">
                <div class="chart-header">
                    <h4><i class="fas fa-chart-pie me-2"></i>الإحصائيات الشهرية</h4>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="printChart()" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="exportChartToPDF()" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-body">
                    <div class="chart-container-3d">
                        <canvas id="meetings3DChart" width="400" height="400"></canvas>
                    </div>
                    <div class="chart-legend-modern">
                        <div class="legend-item-modern completed">
                            <div class="legend-dot"></div>
                            <span>مكتملة</span>
                            <strong id="completedCount">{{ completed_meetings or 0 }}</strong>
                        </div>
                        <div class="legend-item-modern upcoming">
                            <div class="legend-dot"></div>
                            <span>قادمة</span>
                            <strong id="upcomingCount">{{ upcoming_meetings or 0 }}</strong>
                        </div>
                        <div class="legend-item-modern cancelled">
                            <div class="legend-dot"></div>
                            <span>ملغية</span>
                            <strong id="cancelledCount">{{ cancelled_meetings or 0 }}</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجدول الشهري المفصل -->
        <div class="col-lg-6 mb-3">
            <div class="monthly-table-card">
                <div class="table-header">
                    <h4><i class="fas fa-calendar-alt me-2"></i>التقرير الشهري المفصل</h4>
                    <div class="header-controls">
                        <div class="year-selector">
                            <label for="yearSelect" style="color: white; font-weight: bold; margin-left: 8px; font-size: 0.9rem;">
                                <i class="fas fa-calendar-year me-1"></i>السنة:
                            </label>
                            <select id="yearSelect" class="form-select form-select-sm" onchange="onYearChange()"
                                    title="اختر السنة لعرض البيانات والإحصائيات الخاصة بها">
                                <option value="2024" selected>📅 2024</option>
                                <option value="2025">📅 2025</option>
                                <option value="2026">📅 2026</option>
                                <option value="2027">📅 2027</option>
                                <option value="2028">📅 2028</option>
                                <option value="2029">📅 2029</option>
                                <option value="2030">📅 2030</option>
                            </select>
                        </div>
                        <div class="action-buttons">
                            <button class="btn btn-print" onclick="printReport()" title="طباعة التقرير">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                            <button class="btn btn-pdf" onclick="exportToPDF()" title="تصدير إلى PDF">
                                <i class="fas fa-file-pdf me-1"></i>PDF
                            </button>
                        </div>
                    </div>
                </div>
                <div class="table-body">
                    <div class="monthly-stats-table">
                        <div class="table-header-row">
                            <div class="col-month">الشهر</div>
                            <div class="col-count">مكتملة</div>
                            <div class="col-count">قادمة</div>
                            <div class="col-count">ملغاة</div>
                            <div class="col-percentage">نسبة الإنجاز</div>
                        </div>
                        <div class="table-rows" id="monthlyTableRows">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    console.log('🔥 بدء تحميل التصميم الجديد للتقارير');

    // بيانات الاجتماعات (سيتم تحديثها من API)
    let meetingsData = {
        completed: 0,
        upcoming: 0,
        cancelled: 0
    };

    console.log('📊 تم تهيئة بيانات الاجتماعات:', meetingsData);

    // متغير لحفظ البيانات الشهرية المحملة
    let monthlyDataCache = {};

    // دالة للتحقق من صحة البيانات
    function validateMeetingsData(data) {
        return {
            completed: parseInt(data.completed) || 0,
            upcoming: parseInt(data.upcoming) || 0,
            cancelled: parseInt(data.cancelled) || 0
        };
    }

    // دالة لتفتيح اللون
    function lightenColor(color, amount) {
        const usePound = color[0] === '#';
        const col = usePound ? color.slice(1) : color;
        const num = parseInt(col, 16);
        let r = (num >> 16) + amount * 255;
        let g = (num >> 8 & 0x00FF) + amount * 255;
        let b = (num & 0x0000FF) + amount * 255;
        r = r > 255 ? 255 : r < 0 ? 0 : r;
        g = g > 255 ? 255 : g < 0 ? 0 : g;
        b = b > 255 ? 255 : b < 0 ? 0 : b;
        return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    }

    // رسم الرسم البياني ثلاثي الأبعاد
    function draw3DChart() {
        const canvas = document.getElementById('meetings3DChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const centerX = 200;
        const centerY = 180;
        const radius = 120;
        const depth = 25;

        // مسح الكانفاس
        ctx.clearRect(0, 0, 400, 400);

        // التأكد من صحة البيانات وحساب الإجمالي
        const completed = parseInt(meetingsData.completed) || 0;
        const upcoming = parseInt(meetingsData.upcoming) || 0;
        const cancelled = parseInt(meetingsData.cancelled) || 0;
        const total = completed + upcoming + cancelled;

        console.log(`📊 بيانات الرسم البياني - مكتملة: ${completed}, قادمة: ${upcoming}, ملغاة: ${cancelled}, المجموع: ${total}`);

        if (total === 0) {
            // رسم دائرة فارغة
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fillStyle = '#f8f9fa';
            ctx.fill();
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 3;
            ctx.stroke();

            ctx.fillStyle = '#6c757d';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('لا توجد بيانات', centerX, centerY);

            console.log('📊 تم رسم دائرة فارغة - لا توجد بيانات');
            return;
        }

        // بيانات الشرائح باستخدام القيم المحققة
        const slices = [
            { value: completed, color: '#28a745', darkColor: '#1e7e34', label: 'مكتملة' },
            { value: upcoming, color: '#007bff', darkColor: '#0056b3', label: 'قادمة' },
            { value: cancelled, color: '#dc3545', darkColor: '#a71e2a', label: 'ملغية' }
        ].filter(slice => slice.value > 0);

        let currentAngle = -Math.PI / 2;

        // رسم الظلال (الجانب السفلي)
        slices.forEach(slice => {
            const sliceAngle = (slice.value / total) * 2 * Math.PI;

            // رسم الجانب السفلي
            ctx.beginPath();
            ctx.moveTo(centerX, centerY + depth);
            ctx.arc(centerX, centerY + depth, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fillStyle = slice.darkColor;
            ctx.fill();

            currentAngle += sliceAngle;
        });

        // إعادة تعيين الزاوية للرسم العلوي
        currentAngle = -Math.PI / 2;

        // رسم الشرائح العلوية مع التدرج
        slices.forEach(slice => {
            const sliceAngle = (slice.value / total) * 2 * Math.PI;
            const percent = Math.round((slice.value / total) * 100);

            // رسم الشريحة العلوية
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();

            // تدرج لوني للتأثير ثلاثي الأبعاد
            const gradient = ctx.createRadialGradient(
                centerX - radius * 0.3, centerY - radius * 0.3, 0,
                centerX, centerY, radius
            );
            gradient.addColorStop(0, lightenColor(slice.color, 0.4));
            gradient.addColorStop(0.7, slice.color);
            gradient.addColorStop(1, slice.darkColor);

            ctx.fillStyle = gradient;
            ctx.fill();

            // رسم الحدود
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 4;
            ctx.stroke();

            // رسم النسبة المئوية داخل الشريحة
            if (sliceAngle > 0.3) {
                const textAngle = currentAngle + sliceAngle / 2;
                const textX = centerX + Math.cos(textAngle) * (radius * 0.7);
                const textY = centerY + Math.sin(textAngle) * (radius * 0.7);

                // ظل النص
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(percent + '%', textX + 1, textY + 1);

                // النص الأساسي
                ctx.fillStyle = '#fff';
                ctx.fillText(percent + '%', textX, textY);

                // رسم العدد تحت النسبة
                ctx.font = 'bold 12px Arial';
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.fillText('(' + slice.value + ')', textX + 1, textY + 21);
                ctx.fillStyle = '#fff';
                ctx.fillText('(' + slice.value + ')', textX, textY + 20);
            }

            currentAngle += sliceAngle;
        });

        // رسم دائرة في المنتصف مع تأثير ثلاثي الأبعاد
        const centerRadius = 45;

        // ظل الدائرة المركزية
        ctx.beginPath();
        ctx.arc(centerX, centerY + depth, centerRadius, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fill();

        // الدائرة المركزية العلوية
        ctx.beginPath();
        ctx.arc(centerX, centerY, centerRadius, 0, 2 * Math.PI);

        const centerGradient = ctx.createRadialGradient(
            centerX - centerRadius * 0.4, centerY - centerRadius * 0.4, 0,
            centerX, centerY, centerRadius
        );
        centerGradient.addColorStop(0, '#ffffff');
        centerGradient.addColorStop(0.3, '#f8f9fa');
        centerGradient.addColorStop(0.7, '#e9ecef');
        centerGradient.addColorStop(1, '#dee2e6');

        ctx.fillStyle = centerGradient;
        ctx.fill();
        ctx.strokeStyle = '#adb5bd';
        ctx.lineWidth = 3;
        ctx.stroke();

        // نص الإجمالي مع ظل
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.font = 'bold 28px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(total, centerX + 1, centerY - 7);

        ctx.fillStyle = '#333';
        ctx.fillText(total, centerX, centerY - 8);

        ctx.font = 'bold 14px Arial';
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillText('إجمالي', centerX + 1, centerY + 16);
        ctx.fillStyle = '#666';
        ctx.fillText('إجمالي', centerX, centerY + 15);

        console.log('✅ تم رسم الرسم البياني ثلاثي الأبعاد بنجاح');
    }

    // ملء الجدول الشهري بالبيانات الحقيقية
    async function updateMonthlyData() {
        const year = document.getElementById('yearSelect').value;
        const tableRows = document.getElementById('monthlyTableRows');

        try {
            console.log(`🔄 جلب البيانات الشهرية لسنة ${year}...`);

            // إظهار مؤشر تحميل في الجدول
            tableRows.innerHTML = `
                <div class="table-row loading-row">
                    <div class="col-month" style="grid-column: 1 / -1; text-align: center; padding: 20px;">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري تحميل بيانات سنة ${year}...
                    </div>
                </div>
            `;

            const response = await fetch(`/api/reports/monthly-data/${year}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'فشل في جلب البيانات');
            }

            const data = result.months;
            let html = '';
            let hasData = false;

            console.log(`📊 استلام بيانات سنة ${year}:`, result);

            // تحديث بيانات الرسم البياني والبطاقات بناءً على السنة المحددة
            if (result.year_totals) {
                // التحقق من صحة البيانات باستخدام الدالة المخصصة
                const validatedData = validateMeetingsData(result.year_totals);

                meetingsData.completed = validatedData.completed;
                meetingsData.upcoming = validatedData.upcoming;
                meetingsData.cancelled = validatedData.cancelled;

                console.log(`📊 بيانات السنة ${year}:`, meetingsData);

                // تحديث البطاقات الإحصائية للسنة المحددة
                const totalMeetingsElement = document.getElementById('totalMeetings');
                const completedMeetingsElement = document.getElementById('completedMeetings');
                const upcomingMeetingsElement = document.getElementById('upcomingMeetings');
                const cancelledMeetingsElement = document.getElementById('cancelledMeetings');

                if (totalMeetingsElement) totalMeetingsElement.textContent = validatedData.completed + validatedData.upcoming + validatedData.cancelled;
                if (completedMeetingsElement) completedMeetingsElement.textContent = validatedData.completed;
                if (upcomingMeetingsElement) upcomingMeetingsElement.textContent = validatedData.upcoming;
                if (cancelledMeetingsElement) cancelledMeetingsElement.textContent = validatedData.cancelled;

                // تحديث الأسطورة مع التحقق من وجود العناصر
                const completedElement = document.getElementById('completedCount');
                const upcomingElement = document.getElementById('upcomingCount');
                const cancelledElement = document.getElementById('cancelledCount');

                if (completedElement) completedElement.textContent = validatedData.completed;
                if (upcomingElement) upcomingElement.textContent = validatedData.upcoming;
                if (cancelledElement) cancelledElement.textContent = validatedData.cancelled;

                // حساب المتوسط الشهري والشهر الأكثر نشاطاً للسنة المحددة
                const monthsWithData = data.filter(month => month.total > 0);
                const avgPerMonth = monthsWithData.length > 0 ? Math.round((validatedData.completed + validatedData.upcoming + validatedData.cancelled) / 12) : 0;

                let mostActiveMonth = 'غير محدد';
                let maxMeetings = 0;
                data.forEach(month => {
                    if (month.total > maxMeetings) {
                        maxMeetings = month.total;
                        mostActiveMonth = month.month;
                    }
                });

                const monthlyAverageElement = document.getElementById('monthlyAverage');
                const mostActiveMonthElement = document.getElementById('mostActiveMonth');

                if (monthlyAverageElement) monthlyAverageElement.textContent = avgPerMonth;
                if (mostActiveMonthElement) mostActiveMonthElement.textContent = mostActiveMonth;

                // إعادة رسم الرسم البياني مع تأثير انتقالي
                const canvas = document.getElementById('meetings3DChart');
                if (canvas) {
                    // تأثير تلاشي قبل الرسم الجديد
                    canvas.style.opacity = '0.3';

                    setTimeout(() => {
                        draw3DChart();
                        // إعادة الشفافية الكاملة
                        canvas.style.opacity = '1';
                        console.log('✅ تم تحديث الرسم البياني بنجاح للسنة:', year);
                    }, 200);
                }
            }

            data.forEach(monthData => {
                const total = monthData.completed + monthData.upcoming + monthData.cancelled;
                const percentage = total > 0 ? Math.round((monthData.completed / total) * 100) : 0;

                // تحديد ما إذا كان هناك بيانات في هذا الشهر
                if (total > 0) {
                    hasData = true;
                }

                // عرض جميع الأشهر التي تحتوي على بيانات أو الأشهر المنتهية
                if (total > 0 || (monthData.ended && year <= new Date().getFullYear())) {
                    const rowClass = total === 0 ? 'empty-month' :
                                   monthData.completed > 10 ? 'high-performance' :
                                   monthData.completed < 5 ? 'low-performance' : '';

                    html += `
                        <div class="table-row ${rowClass}">
                            <div class="col-month">
                                <i class="fas fa-calendar-${total > 0 ? 'check' : 'times'} me-2"></i>
                                ${monthData.month}
                            </div>
                            <div class="col-count">
                                <span class="count-badge ${total === 0 ? 'empty-badge' : ''}">${monthData.completed}</span>
                                <small style="display: block; color: #6c757d; font-size: 0.7rem;">مكتملة</small>
                            </div>
                            <div class="col-count">
                                <span class="count-badge ${total === 0 ? 'empty-badge' : ''}" style="background: linear-gradient(135deg, #007bff, #0056b3);">${monthData.upcoming}</span>
                                <small style="display: block; color: #6c757d; font-size: 0.7rem;">قادمة</small>
                            </div>
                            <div class="col-count">
                                <span class="count-badge ${total === 0 ? 'empty-badge' : ''}" style="background: linear-gradient(135deg, #dc3545, #a71e2a);">${monthData.cancelled}</span>
                                <small style="display: block; color: #6c757d; font-size: 0.7rem;">ملغاة</small>
                            </div>
                            <div class="col-percentage">
                                <div class="percentage-bar">
                                    <div class="percentage-fill" style="width: ${percentage}%; background: linear-gradient(90deg, #28a745, #20c997);"></div>
                                    <span class="percentage-text">${percentage}%</span>
                                </div>
                            </div>
                        </div>
                    `;
                }
            });

            if (html === '') {
                html = `
                    <div class="table-row">
                        <div class="col-month" style="grid-column: 1 / -1; text-align: center; color: #666;">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد بيانات لسنة ${year}
                        </div>
                    </div>
                `;
            }

            // إذا لم تكن هناك بيانات، عرض رسالة مناسبة
            if (!hasData) {
                html = `
                    <div class="table-row no-data-row">
                        <div class="col-month" style="grid-column: 1 / -1; text-align: center; padding: 30px;">
                            <i class="fas fa-calendar-times me-2" style="color: #6c757d; font-size: 2rem;"></i>
                            <div style="margin-top: 10px; color: #6c757d; font-size: 1.1rem;">
                                لا توجد اجتماعات مسجلة لسنة ${year}
                            </div>
                            <div style="margin-top: 5px; color: #adb5bd; font-size: 0.9rem;">
                                يمكنك إضافة اجتماعات جديدة من قسم الاجتماعات
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // إضافة معلومة عن عدد الأشهر التي تحتوي على بيانات
                const monthsWithDataCount = data.filter(month => month.total > 0).length;
                const totalYearMeetings = result.year_totals ? (result.year_totals.completed + result.year_totals.upcoming + result.year_totals.cancelled) : 0;

                html += `
                    <div class="table-row summary-row" style="background: linear-gradient(135deg, #e8f5e8, #d4edda); border-top: 2px solid #28a745; margin-top: 10px;">
                        <div class="col-month" style="grid-column: 1 / -1; text-align: center; padding: 15px; color: #155724;">
                            <i class="fas fa-chart-line me-2"></i>
                            <strong>ملخص سنة ${year}: ${totalYearMeetings} اجتماع في ${monthsWithDataCount} شهر</strong>
                            <br>
                            <small style="color: #6c757d; margin-top: 5px; display: block;">
                                💡 نصيحة: يمكنك اختيار سنة أخرى من القائمة المنسدلة أعلاه
                            </small>
                        </div>
                    </div>
                `;
            }

            tableRows.innerHTML = html;

            const totalMeetings = result.year_totals ? (result.year_totals.completed + result.year_totals.upcoming + result.year_totals.cancelled) : 0;
            console.log(`✅ تم تحديث الجدول الشهري لسنة ${year} - البيانات: ${hasData ? `موجودة (${totalMeetings} اجتماع)` : 'غير موجودة'}`);

        } catch (error) {
            console.error('❌ خطأ في جلب البيانات الشهرية:', error);

            // إعادة تعيين بيانات الرسم البياني إلى الصفر
            meetingsData = { completed: 0, upcoming: 0, cancelled: 0 };

            // تحديث الأسطورة
            const completedElement = document.getElementById('completedCount');
            const upcomingElement = document.getElementById('upcomingCount');
            const cancelledElement = document.getElementById('cancelledCount');

            if (completedElement) completedElement.textContent = '0';
            if (upcomingElement) upcomingElement.textContent = '0';
            if (cancelledElement) cancelledElement.textContent = '0';

            // إعادة رسم الرسم البياني
            draw3DChart();

            tableRows.innerHTML = `
                <div class="table-row">
                    <div class="col-month" style="grid-column: 1 / -1; text-align: center; color: #dc3545;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في تحميل البيانات: ${error.message}
                    </div>
                </div>
            `;
        }
    }

    // دالة لتحديث البيانات عند تغيير السنة
    async function onYearChange() {
        const year = document.getElementById('yearSelect').value;
        console.log(`🔄 تغيير السنة إلى ${year} - تحديث البيانات...`);

        // إظهار مؤشر التحميل
        showLoadingIndicator();

        try {
            // تحديث الجدول الشهري والرسم البياني
            await updateMonthlyData();

            // إخفاء مؤشر التحميل
            hideLoadingIndicator();

            console.log(`✅ تم تحديث البيانات لسنة ${year} بنجاح`);

            // حساب إجمالي الاجتماعات للسنة
            const totalYearMeetings = meetingsData.completed + meetingsData.upcoming + meetingsData.cancelled;

            // إظهار رسالة نجاح مؤقتة مع التفاصيل
            if (totalYearMeetings > 0) {
                showSuccessMessage(`تم تحديث بيانات سنة ${year} بنجاح! (${totalYearMeetings} اجتماع)`);
            } else {
                showSuccessMessage(`تم تحديث بيانات سنة ${year} - لا توجد اجتماعات مسجلة`);
            }

        } catch (error) {
            console.error(`❌ خطأ في تحديث بيانات السنة ${year}:`, error);
            hideLoadingIndicator();
            showErrorMessage(`فشل في تحميل بيانات سنة ${year}`);
        }
    }

    // دالة لإظهار مؤشر التحميل
    function showLoadingIndicator() {
        const yearSelect = document.getElementById('yearSelect');
        yearSelect.style.opacity = '0.6';
        yearSelect.disabled = true;

        // إضافة مؤشر تحميل بصري
        const label = yearSelect.previousElementSibling;
        if (label) {
            label.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحميل...';
        }
    }

    // دالة لإخفاء مؤشر التحميل
    function hideLoadingIndicator() {
        const yearSelect = document.getElementById('yearSelect');
        yearSelect.style.opacity = '1';
        yearSelect.disabled = false;

        // إعادة النص الأصلي
        const label = yearSelect.previousElementSibling;
        if (label) {
            label.innerHTML = '<i class="fas fa-calendar-year me-1"></i>السنة:';
        }
    }

    // دالة لإظهار رسالة نجاح
    function showSuccessMessage(message) {
        console.log('✅ إظهار رسالة نجاح:', message);

        const notification = document.createElement('div');
        notification.className = 'success-notification';
        notification.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <strong>${message}</strong>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    // دالة لإظهار رسالة خطأ
    function showErrorMessage(message) {
        console.log('❌ إظهار رسالة خطأ:', message);

        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>${message}</strong>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 4000);
    }

    // دوال الطباعة والتصدير الجديدة - فقط عند النقر على الأزرار
    function printChart() {
        console.log('🖨️ طباعة تقرير اجتماع الخطوة الموحد من صفحة التقارير...');

        // استخدام نفس منطق طباعة الاجتماع الموحد من meetings_simple
        // سنطبع تقرير لأول اجتماع متاح أو اجتماع تجريبي

        // جلب أول اجتماع من قاعدة البيانات
        fetch('/api/meeting/1')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('✅ تم جلب بيانات الاجتماع بنجاح:', data.meeting.subject);
                printUnifiedMeetingReport(data.meeting);
            } else {
                throw new Error(data.message || 'خطأ في جلب البيانات');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في جلب بيانات الاجتماع:', error);
            console.log('🔄 استخدام بيانات تجريبية...');

            // استخدام بيانات تجريبية في حالة الخطأ
            const sampleMeeting = {
                subject: 'اجتماع تقرير النظام المحسن',
                meeting_type: 'اجتماع رسمي',
                meeting_date: '2025-08-15',
                meeting_time: '10:00',
                location: 'قاعة الاجتماعات الرئيسية',
                inviting_party: 'مديرية الدائرة المالية',
                dress_code: 'رسمي',
                book_number: '12345',
                book_date: '2025-07-27',
                notes: 'اجتماع مهم لمناقشة تطوير النظام',
                is_cancelled: false,
                is_postponed: false
            };

            printUnifiedMeetingReport(sampleMeeting);
        });
    }

    // دالة إنشاء تقرير اجتماع الخطوة المحسن (نسخة من meetings_simple)
    function printUnifiedMeetingReport(meeting) {
        console.log('🖨️ إنشاء تقرير اجتماع الخطوة المحسن');

        // تحويل التاريخ إلى صيغة YYYY/MM/DD
        const formatDate = (dateStr) => {
            if (!dateStr) return 'غير محدد';
            const date = new Date(dateStr);
            return date.toISOString().split('T')[0].replace(/-/g, '/');
        };

        // تحويل الوقت إلى صيغة مقروءة
        const formatTime = (timeStr) => {
            if (!timeStr) return 'غير محدد';
            return timeStr;
        };

        // تحويل الوقت مع تحديد الفترة (صباحاً/مساءً)
        const formatTimeWithPeriod = (timeStr) => {
            if (!timeStr) return 'غير محدد';

            // تحويل الوقت إلى ساعة ودقيقة
            const [hours, minutes] = timeStr.split(':').map(Number);

            // تحديد الفترة
            let period = '';
            if (hours >= 0 && hours < 12) {
                period = 'صباحاً';
            } else {
                period = 'مساءً';
            }

            return `${timeStr} ${period}`;
        };

        // تحديد الحالة الحقيقية للاجتماع
        const getMeetingStatus = (meeting) => {
            if (meeting.is_cancelled) return 'ملغي';
            if (meeting.is_postponed) return 'مؤجل';

            // فحص إذا كان الاجتماع منتهي بناءً على التاريخ
            const today = new Date();
            const meetingDate = new Date(meeting.meeting_date);

            if (meetingDate < today) {
                return 'منتهي';
            }

            return 'نشط';
        };

        // إنشاء HTML للتقرير المحسن
        const reportHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>اجتماع الخطوة</title>
                <style>
                    body {
                        font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
                        direction: rtl;
                        margin: 0;
                        padding: 15px;
                        background: #f8f9fa;
                        line-height: 1.4;
                        font-size: 14px;
                    }
                    .report-container {
                        max-width: 210mm;
                        min-height: 297mm;
                        margin: 0 auto;
                        background: white;
                        border-radius: 10px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                        overflow: hidden;
                        page-break-inside: avoid;
                    }
                    .header {
                        background: linear-gradient(135deg, #1B4332 0%, #2d5a3d 100%);
                        color: white;
                        padding: 20px;
                        text-align: center;
                    }
                    .header h1 {
                        margin: 0;
                        font-size: 24px;
                        font-weight: bold;
                    }
                    .header .subtitle {
                        margin: 8px 0 0 0;
                        font-size: 14px;
                        opacity: 0.9;
                    }
                    .content {
                        padding: 25px;
                    }
                    .details-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                        margin-bottom: 20px;
                    }
                    .detail-section {
                        background: #f8f9fa;
                        border-radius: 8px;
                        padding: 15px;
                        border-right: 3px solid #1B4332;
                    }
                    .detail-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 10px;
                        padding: 8px;
                        background: white;
                        border-radius: 6px;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                    }
                    .detail-item:last-child {
                        margin-bottom: 0;
                    }
                    .detail-icon {
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-left: 12px;
                        font-size: 14px;
                        color: white;
                        flex-shrink: 0;
                    }
                    .detail-content {
                        flex: 1;
                    }
                    .detail-label {
                        font-weight: bold;
                        color: #1B4332;
                        font-size: 12px;
                        margin-bottom: 2px;
                    }
                    .detail-value {
                        color: #333;
                        font-size: 14px;
                    }
                    .icon-blue { background: linear-gradient(135deg, #007bff, #0056b3); }
                    .icon-green { background: linear-gradient(135deg, #28a745, #1e7e34); }
                    .icon-orange { background: linear-gradient(135deg, #fd7e14, #e55a00); }
                    .icon-purple { background: linear-gradient(135deg, #6f42c1, #5a32a3); }
                    .icon-red { background: linear-gradient(135deg, #dc3545, #c82333); }
                    .icon-teal { background: linear-gradient(135deg, #20c997, #17a2b8); }
                    .footer {
                        background: #f8f9fa;
                        padding: 15px 25px;
                        text-align: center;
                        border-top: 1px solid #dee2e6;
                        color: #666;
                        font-size: 12px;
                    }
                    @media print {
                        body {
                            background: white;
                            padding: 0;
                            margin: 0;
                            font-size: 12px;
                        }
                        .report-container {
                            box-shadow: none;
                            max-width: 100%;
                            margin: 0;
                            border-radius: 0;
                            page-break-inside: avoid;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="report-container">
                    <div class="header">
                        <h1>اجتماع الخطوة</h1>
                        <div class="subtitle">تفاصيل الاجتماع</div>
                    </div>

                    <div class="content">
                        <div class="details-grid">
                            <div class="detail-section">
                                <h4 style="color: #1B4332; margin-bottom: 12px; text-align: center; border-bottom: 2px solid #1B4332; padding-bottom: 6px; font-size: 14px;">تفاصيل الاجتماع</h4>

                                <div class="detail-item">
                                    <div class="detail-icon icon-blue">📋</div>
                                    <div class="detail-content">
                                        <div class="detail-label">الموضوع</div>
                                        <div class="detail-value">${meeting.subject || 'غير محدد'}</div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-icon icon-green">🏢</div>
                                    <div class="detail-content">
                                        <div class="detail-label">نوع الفعالية</div>
                                        <div class="detail-value">${meeting.meeting_type || 'اجتماع'}</div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-icon icon-orange">📍</div>
                                    <div class="detail-content">
                                        <div class="detail-label">المكان</div>
                                        <div class="detail-value">${meeting.location || 'قاعة الاجتماعات'}</div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-icon icon-red">📅</div>
                                    <div class="detail-content">
                                        <div class="detail-label">التاريخ</div>
                                        <div class="detail-value">${formatDate(meeting.meeting_date)}</div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-icon icon-teal">🕐</div>
                                    <div class="detail-content">
                                        <div class="detail-label">الوقت</div>
                                        <div class="detail-value">${formatTimeWithPeriod(meeting.meeting_time)}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h4 style="color: #1B4332; margin-bottom: 12px; text-align: center; border-bottom: 2px solid #1B4332; padding-bottom: 6px; font-size: 14px;">جهة الدعوة</h4>

                                <div class="detail-item">
                                    <div class="detail-icon icon-purple">🏛️</div>
                                    <div class="detail-content">
                                        <div class="detail-label">الجهة</div>
                                        <div class="detail-value">${meeting.inviting_party || 'مديرية الدائرة المالية'}</div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-icon icon-green">👔</div>
                                    <div class="detail-content">
                                        <div class="detail-label">نوع اللباس</div>
                                        <div class="detail-value">${meeting.dress_code || 'رسمي'}</div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-icon icon-orange">⏰</div>
                                    <div class="detail-content">
                                        <div class="detail-label">الحضور قبل الموعد</div>
                                        <div class="detail-value">${formatTimeWithPeriod(meeting.meeting_time)}</div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-icon icon-blue">📋</div>
                                    <div class="detail-content">
                                        <div class="detail-label">الحالة</div>
                                        <div class="detail-value">${getMeetingStatus(meeting)}</div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-icon icon-red">📍</div>
                                    <div class="detail-content">
                                        <div class="detail-label">المكان</div>
                                        <div class="detail-value">${meeting.location || 'قاعة الاجتماعات'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${meeting.book_number || meeting.notes ? `
                        <div class="detail-section" style="grid-column: 1 / -1; margin-top: 15px;">
                            <h4 style="color: #1B4332; margin-bottom: 12px; text-align: center; border-bottom: 2px solid #1B4332; padding-bottom: 6px; font-size: 14px;">معلومات إضافية</h4>

                            ${meeting.book_number ? `
                            <div class="detail-item">
                                <div class="detail-icon icon-purple">📄</div>
                                <div class="detail-content">
                                    <div class="detail-label">رقم الكتاب</div>
                                    <div class="detail-value">${meeting.book_number}</div>
                                </div>
                            </div>
                            ` : ''}

                            ${meeting.book_date ? `
                            <div class="detail-item">
                                <div class="detail-icon icon-green">📅</div>
                                <div class="detail-content">
                                    <div class="detail-label">تاريخ الكتاب</div>
                                    <div class="detail-value">${formatDate(meeting.book_date)}</div>
                                </div>
                            </div>
                            ` : ''}

                            ${meeting.notes ? `
                            <div class="detail-item">
                                <div class="detail-icon icon-orange">📝</div>
                                <div class="detail-content">
                                    <div class="detail-label">ملاحظات</div>
                                    <div class="detail-value">${meeting.notes}</div>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                        ` : ''}
                    </div>

                    <div class="footer">
                        تاريخ الطباعة: ${new Date().toISOString().split('T')[0].replace(/-/g, '/')}
                    </div>
                </div>
            </body>
            </html>
        `;

        // فتح نافذة طباعة جديدة
        const printWindow = window.open('', '_blank', 'width=900,height=700');
        if (!printWindow) {
            console.error('❌ تعذر فتح نافذة الطباعة');
            alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
            return;
        }

        console.log('✅ تم فتح نافذة الطباعة بنجاح');
        printWindow.document.write(reportHTML);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
                console.log('✅ تم تشغيل طباعة تقرير اجتماع الخطوة من صفحة التقارير');
            }, 500);
        };

        console.log('✅ تم إنشاء التقرير المحسن بنجاح من صفحة التقارير');
    }

    function exportChartToPDF() {
        console.log('📄 تصدير PDF للتقرير الشهري...');

        const canvas = document.getElementById('meetings3DChart');
        if (!canvas) {
            alert('❌ لم يتم العثور على الرسم البياني');
            return;
        }

        const chartImage = canvas.toDataURL('image/png');
        const pdfWindow = window.open('', '_blank');

        const pdfHTML = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>التقرير الشهري المفصل - PDF</title>
                <style>
                    @page { size: A4; margin: 1.5cm; }
                    body { font-family: Arial, sans-serif; direction: rtl; margin: 0; padding: 20px; background: #fff; }
                    .pdf-header { background: linear-gradient(135deg, #1b4332, #2d5a3d); color: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; text-align: center; }
                    .chart-section { background: #f8f9fa; padding: 20px; border-radius: 12px; margin: 20px 0; text-align: center; }
                    .chart-image { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin: 15px 0; }
                    .pdf-footer { margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="pdf-header">
                    <h2>📊 التقرير الشهري المفصل - PDF</h2>
                    <p>الإحصائيات ثلاثية الأبعاد والتحليل الشهري</p>
                </div>
                <div class="chart-section">
                    <h3>📈 الرسم البياني ثلاثي الأبعاد</h3>
                    <img src="${chartImage}" alt="الرسم البياني" class="chart-image">
                </div>
                <div class="pdf-footer">
                    <p><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                    <p>نظام إدارة الاجتماعات - التقرير الشهري المفصل</p>
                </div>
            </body>
            </html>
        `;

        pdfWindow.document.write(pdfHTML);
        pdfWindow.document.close();

        setTimeout(() => {
            pdfWindow.print();
        }, 1000);
    }

    // دالة لتحديث البيانات العامة (البطاقات الإحصائية العامة فقط)
    async function updateRealTimeData() {
        try {
            console.log('🔄 جلب البيانات العامة للبطاقات الإحصائية...');

            const response = await fetch('/api/reports/stats');
            const result = await response.json();

            if (result.success) {
                // حفظ البيانات العامة للمرجع
                window.generalStats = {
                    total_meetings: result.total_meetings || 0,
                    completed_meetings: result.completed_meetings || 0,
                    upcoming_meetings: result.upcoming_meetings || 0,
                    cancelled_meetings: result.cancelled_meetings || 0,
                    avg_per_month: result.avg_per_month || 0,
                    most_active_month: result.most_active_month || 'غير محدد'
                };

                console.log('✅ تم حفظ البيانات العامة بنجاح');
            } else {
                console.error('❌ فشل في جلب البيانات العامة:', result.error);
            }

        } catch (error) {
            console.error('❌ خطأ في جلب البيانات العامة:', error);
        }
    }

    // دالة طباعة التقرير
    function printReport() {
        const year = document.getElementById('yearSelect').value;
        console.log(`🖨️ طباعة تقرير سنة ${year}...`);

        // إنشاء نافذة طباعة مخصصة
        const printWindow = window.open('', '_blank');
        const reportContent = generatePrintableReport(year);

        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير الاجتماعات - سنة ${year}</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; direction: rtl; }

                    /* تقسيم الصفحات */
                    .page {
                        min-height: 100vh;
                        padding: 20px;
                        page-break-after: always;
                        display: flex;
                        flex-direction: column;
                    }
                    .page:last-child { page-break-after: avoid; }

                    /* رأس الصفحة */
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 3px solid #2c3e50;
                        padding-bottom: 20px;
                    }
                    .header h1 { color: #2c3e50; margin: 0; font-size: 2.2rem; }
                    .header h2 { color: #7f8c8d; margin: 5px 0; font-size: 1.4rem; }
                    .header p { color: #6c757d; margin: 10px 0; }

                    /* البطاقات الإحصائية */
                    .stats-grid {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                        margin: 30px 0;
                        flex-grow: 1;
                    }
                    .stat-card {
                        background: #f8f9fa;
                        padding: 25px;
                        border-radius: 12px;
                        text-align: center;
                        border: 2px solid #dee2e6;
                        position: relative;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                    }
                    .stat-card.primary { border-color: #007bff; background: linear-gradient(135deg, #e3f2fd, #f8f9fa); }
                    .stat-card.success { border-color: #28a745; background: linear-gradient(135deg, #e8f5e9, #f8f9fa); }
                    .stat-card.info { border-color: #17a2b8; background: linear-gradient(135deg, #e0f7fa, #f8f9fa); }
                    .stat-card.danger { border-color: #dc3545; background: linear-gradient(135deg, #ffebee, #f8f9fa); }
                    .stat-card.warning { border-color: #ffc107; background: linear-gradient(135deg, #fff8e1, #f8f9fa); }
                    .stat-card.secondary { border-color: #6c757d; background: linear-gradient(135deg, #f5f5f5, #f8f9fa); }

                    .stat-number { font-size: 3rem; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
                    .stat-label { color: #6c757d; font-size: 1rem; font-weight: 600; }
                    .stat-icon { font-size: 2rem; position: absolute; top: 15px; right: 15px; opacity: 0.3; }

                    /* الجدول */
                    .monthly-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 1.1rem;
                    }
                    .monthly-table th, .monthly-table td {
                        border: 2px solid #dee2e6;
                        padding: 15px;
                        text-align: center;
                    }
                    .monthly-table th {
                        background: linear-gradient(135deg, #2c3e50, #34495e);
                        color: white;
                        font-weight: bold;
                        font-size: 1.2rem;
                    }
                    .monthly-table tr:nth-child(even) { background: #f8f9fa; }

                    /* صندوق الملخص */
                    .summary-box {
                        background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
                        border: 2px solid #2196f3;
                        border-radius: 12px;
                        padding: 20px;
                        margin: 30px 0;
                    }
                    .summary-box h4 { color: #1976d2; margin-bottom: 15px; }
                    .summary-stats { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
                    .summary-item { text-align: center; }
                    .summary-label { display: block; color: #6c757d; font-size: 0.9rem; }
                    .summary-value { display: block; color: #2c3e50; font-size: 1.5rem; font-weight: bold; }

                    /* الرسم البياني */
                    .chart-container {
                        display: flex;
                        justify-content: center;
                        margin: 30px 0;
                    }
                    .chart-placeholder {
                        background: #f8f9fa;
                        border: 2px solid #dee2e6;
                        border-radius: 12px;
                        padding: 30px;
                        text-align: center;
                        min-width: 400px;
                    }
                    .chart-title { font-size: 1.5rem; font-weight: bold; color: #2c3e50; margin-bottom: 20px; }
                    .chart-data { display: flex; flex-direction: column; gap: 15px; }
                    .chart-segment { display: flex; align-items: center; gap: 10px; font-size: 1.2rem; }
                    .segment-color { width: 20px; height: 20px; border-radius: 50%; }

                    /* التحليل */
                    .analysis-section {
                        background: linear-gradient(135deg, #fff3e0, #f8f9fa);
                        border: 2px solid #ff9800;
                        border-radius: 12px;
                        padding: 20px;
                        margin: 30px 0;
                    }
                    .analysis-section h4 { color: #f57c00; margin-bottom: 15px; }
                    .analysis-content { display: flex; flex-direction: column; gap: 10px; }
                    .analysis-item { padding: 10px; background: white; border-radius: 8px; }

                    /* تذييل الصفحة */
                    .page-footer {
                        margin-top: auto;
                        text-align: center;
                        color: #6c757d;
                        font-size: 0.9rem;
                        padding-top: 20px;
                        border-top: 1px solid #dee2e6;
                    }
                    .footer {
                        text-align: center;
                        color: #6c757d;
                        font-size: 0.8rem;
                        margin-top: 20px;
                    }

                    @media print {
                        body { margin: 0; }
                        .page { margin: 0; padding: 15px; }
                    }
                </style>
            </head>
            <body>
                ${reportContent}
            </body>
            </html>
        `);

        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        setTimeout(() => {
            printWindow.focus();
            printWindow.print();
            printWindow.close();
        }, 500);

        showSuccessMessage(`تم إرسال تقرير سنة ${year} للطباعة`);
    }

    // دالة تصدير إلى PDF
    function exportToPDF() {
        const year = document.getElementById('yearSelect').value;
        console.log(`📄 تصدير تقرير سنة ${year} إلى PDF...`);

        // إنشاء نافذة PDF مخصصة
        const pdfWindow = window.open('', '_blank');
        const reportContent = generatePrintableReport(year);

        pdfWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير الاجتماعات - سنة ${year}</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
                    .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                    .header h1 { color: #2c3e50; margin: 0; }
                    .header h2 { color: #7f8c8d; margin: 5px 0; }
                    .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                    .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border: 1px solid #dee2e6; }
                    .stat-number { font-size: 2rem; font-weight: bold; color: #2c3e50; }
                    .stat-label { color: #6c757d; font-size: 0.9rem; }
                    .monthly-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .monthly-table th, .monthly-table td { border: 1px solid #dee2e6; padding: 10px; text-align: center; }
                    .monthly-table th { background: #f8f9fa; font-weight: bold; }
                    .footer { margin-top: 30px; text-align: center; color: #6c757d; font-size: 0.8rem; }
                    .pdf-note { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center; }
                </style>
            </head>
            <body>
                <div class="pdf-note">
                    <strong>💡 لحفظ هذا التقرير كملف PDF:</strong><br>
                    اضغط Ctrl+P (أو Cmd+P على Mac) ← اختر "حفظ كـ PDF" ← اضغط حفظ
                </div>
                ${reportContent}
            </body>
            </html>
        `);

        pdfWindow.document.close();

        // انتظار تحميل المحتوى ثم فتح حوار الطباعة/PDF
        setTimeout(() => {
            pdfWindow.focus();
            pdfWindow.print();
        }, 500);

        showSuccessMessage(`تم فتح تقرير سنة ${year} للتصدير كـ PDF`);
    }

    // دالة إنشاء محتوى التقرير القابل للطباعة (3 صفحات)
    function generatePrintableReport(year) {
        const currentDate = new Date().toLocaleDateString('ar-SA');

        // جلب البيانات الحالية من الصفحة
        const totalMeetings = document.getElementById('totalMeetings')?.textContent || '0';
        const completedMeetings = document.getElementById('completedMeetings')?.textContent || '0';
        const upcomingMeetings = document.getElementById('upcomingMeetings')?.textContent || '0';
        const cancelledMeetings = document.getElementById('cancelledMeetings')?.textContent || '0';
        const monthlyAverage = document.getElementById('monthlyAverage')?.textContent || '0';
        const mostActiveMonth = document.getElementById('mostActiveMonth')?.textContent || 'غير محدد';

        // جلب بيانات الجدول الشهري
        const monthlyRows = document.querySelectorAll('#monthlyTableRows .table-row');
        let monthlyTableHTML = '';

        monthlyRows.forEach(row => {
            const monthName = row.querySelector('.col-month')?.textContent?.trim() || '';
            const completed = row.querySelector('.col-count:nth-child(2) .count-badge')?.textContent || '0';
            const upcoming = row.querySelector('.col-count:nth-child(3) .count-badge')?.textContent || '0';
            const cancelled = row.querySelector('.col-count:nth-child(4) .count-badge')?.textContent || '0';
            const total = parseInt(completed) + parseInt(upcoming) + parseInt(cancelled);

            if (monthName && !monthName.includes('لا توجد بيانات') && !monthName.includes('جاري تحميل')) {
                monthlyTableHTML += `
                    <tr>
                        <td>${monthName}</td>
                        <td>${completed}</td>
                        <td>${upcoming}</td>
                        <td>${cancelled}</td>
                        <td>${total}</td>
                    </tr>
                `;
            }
        });

        // جلب بيانات الرسم البياني
        const chartData = window.meetingsData || {completed: 0, upcoming: 0, cancelled: 0};

        return `
            <!-- الصفحة الأولى: البطاقات الإحصائية -->
            <div class="page page-1">
                <div class="header">
                    <h1>🏛️ القوات المسلحة الأردنية</h1>
                    <h2>مديرية الدائرة المالية</h2>
                    <h2>تقرير الاجتماعات - سنة ${year}</h2>
                    <p>تاريخ التقرير: ${currentDate}</p>
                </div>

                <h3 style="text-align: center; margin: 30px 0; color: #2c3e50;">📊 الإحصائيات العامة</h3>

                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-number">${totalMeetings}</div>
                        <div class="stat-label">إجمالي الاجتماعات</div>
                        <div class="stat-icon">📋</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">${completedMeetings}</div>
                        <div class="stat-label">اجتماعات مكتملة</div>
                        <div class="stat-icon">✅</div>
                    </div>
                    <div class="stat-card info">
                        <div class="stat-number">${upcomingMeetings}</div>
                        <div class="stat-label">اجتماعات قادمة</div>
                        <div class="stat-icon">⏰</div>
                    </div>
                    <div class="stat-card danger">
                        <div class="stat-number">${cancelledMeetings}</div>
                        <div class="stat-label">اجتماعات ملغاة</div>
                        <div class="stat-icon">❌</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">${monthlyAverage}</div>
                        <div class="stat-label">متوسط شهري</div>
                        <div class="stat-icon">📈</div>
                    </div>
                    <div class="stat-card secondary">
                        <div class="stat-number">${mostActiveMonth}</div>
                        <div class="stat-label">أكثر الشهور نشاطاً</div>
                        <div class="stat-icon">🏆</div>
                    </div>
                </div>

                <div class="page-footer">
                    <p>الصفحة 1 من 3 - الإحصائيات العامة</p>
                </div>
            </div>

            <!-- الصفحة الثانية: الجدول الشهري -->
            <div class="page page-2">
                <div class="header">
                    <h1>🏛️ القوات المسلحة الأردنية</h1>
                    <h2>مديرية الدائرة المالية</h2>
                    <h2>التفصيل الشهري - سنة ${year}</h2>
                </div>

                <h3 style="text-align: center; margin: 30px 0; color: #2c3e50;">📅 التوزيع الشهري للاجتماعات</h3>

                <table class="monthly-table">
                    <thead>
                        <tr>
                            <th>الشهر</th>
                            <th>مكتملة</th>
                            <th>قادمة</th>
                            <th>ملغاة</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${monthlyTableHTML || '<tr><td colspan="5">لا توجد بيانات لهذه السنة</td></tr>'}
                    </tbody>
                </table>

                <div class="summary-box">
                    <h4>📋 ملخص السنة</h4>
                    <div class="summary-stats">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي الاجتماعات:</span>
                            <span class="summary-value">${totalMeetings}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">معدل الإنجاز:</span>
                            <span class="summary-value">${totalMeetings > 0 ? Math.round((completedMeetings / totalMeetings) * 100) : 0}%</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">معدل الإلغاء:</span>
                            <span class="summary-value">${totalMeetings > 0 ? Math.round((cancelledMeetings / totalMeetings) * 100) : 0}%</span>
                        </div>
                    </div>
                </div>

                <div class="page-footer">
                    <p>الصفحة 2 من 3 - التفصيل الشهري</p>
                </div>
            </div>

            <!-- الصفحة الثالثة: الرسم البياني والتحليل -->
            <div class="page page-3">
                <div class="header">
                    <h1>🏛️ القوات المسلحة الأردنية</h1>
                    <h2>مديرية الدائرة المالية</h2>
                    <h2>التحليل البياني - سنة ${year}</h2>
                </div>

                <h3 style="text-align: center; margin: 30px 0; color: #2c3e50;">📊 التوزيع البياني للاجتماعات</h3>

                <div class="chart-container">
                    <div class="chart-placeholder">
                        <div class="chart-title">الرسم البياني الدائري</div>
                        <div class="chart-data">
                            <div class="chart-segment completed">
                                <div class="segment-color" style="background: #28a745;"></div>
                                <span>مكتملة: ${chartData.completed}</span>
                            </div>
                            <div class="chart-segment upcoming">
                                <div class="segment-color" style="background: #007bff;"></div>
                                <span>قادمة: ${chartData.upcoming}</span>
                            </div>
                            <div class="chart-segment cancelled">
                                <div class="segment-color" style="background: #dc3545;"></div>
                                <span>ملغاة: ${chartData.cancelled}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analysis-section">
                    <h4>📈 التحليل والتوصيات</h4>
                    <div class="analysis-content">
                        <div class="analysis-item">
                            <strong>معدل الأداء:</strong>
                            ${totalMeetings > 0 ?
                                (completedMeetings / totalMeetings * 100 >= 80 ?
                                    'ممتاز - معدل إنجاز عالي' :
                                    completedMeetings / totalMeetings * 100 >= 60 ?
                                        'جيد - يمكن التحسين' :
                                        'يحتاج تحسين'
                                ) : 'لا توجد بيانات كافية'
                            }
                        </div>
                        <div class="analysis-item">
                            <strong>التوزيع الزمني:</strong>
                            ${monthlyAverage > 1 ? 'نشاط منتظم' : 'نشاط محدود'}
                        </div>
                        <div class="analysis-item">
                            <strong>الشهر الأكثر نشاطاً:</strong>
                            ${mostActiveMonth}
                        </div>
                    </div>
                </div>

                <div class="page-footer">
                    <p>الصفحة 3 من 3 - التحليل البياني</p>
                </div>
            </div>

            <div class="footer">
                <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة الاجتماعات</p>
                <p>القوات المسلحة الأردنية - مديرية الدائرة المالية</p>
            </div>
        `;
    }

    // دالة لعرض البيانات العامة في البطاقات
    function displayGeneralStats() {
        if (window.generalStats) {
            document.getElementById('totalMeetings').textContent = window.generalStats.total_meetings;
            document.getElementById('completedMeetings').textContent = window.generalStats.completed_meetings;
            document.getElementById('upcomingMeetings').textContent = window.generalStats.upcoming_meetings;
            document.getElementById('cancelledMeetings').textContent = window.generalStats.cancelled_meetings;
            document.getElementById('monthlyAverage').textContent = window.generalStats.avg_per_month;
            document.getElementById('mostActiveMonth').textContent = window.generalStats.most_active_month;

            console.log('✅ تم عرض البيانات العامة في البطاقات');
        }
    }

    // تشغيل التطبيق عند تحميل الصفحة - بدون طباعة تلقائية
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 بدء تحميل التقرير الشهري المفصل');

        // تأخير قصير للتأكد من تحميل العناصر
        setTimeout(async () => {
            try {
                // تحديث البيانات العامة أولاً
                await updateRealTimeData();

                // عرض البيانات العامة في البطاقات
                displayGeneralStats();

                // ثم تحديث الجدول الشهري والرسم البياني للسنة المحددة (2024)
                await updateMonthlyData();

                console.log('✅ تم تحميل التقرير بنجاح - بدون طباعة تلقائية');

                // إظهار رسالة ترحيب
                showSuccessMessage('تم تحميل التقارير بنجاح! يمكنك الآن اختيار السنة من القائمة المنسدلة');
            } catch (error) {
                console.error('❌ خطأ في تحميل التقرير:', error);

                // في حالة الخطأ، عرض بيانات فارغة
                meetingsData = { completed: 0, upcoming: 0, cancelled: 0 };
                draw3DChart();

                // إظهار رسالة خطأ
                showErrorMessage('حدث خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
            }
        }, 1000);
    });
    </script>
</div>
{% endblock %}
