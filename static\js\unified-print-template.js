/**
 * قالب طباعة موحد لجميع التقارير في النظام
 * نظام إدارة المواعيد - القوات المسلحة الأردنية
 */

// إعدادات القالب الموحد
const UNIFIED_PRINT_CONFIG = {
    colors: {
        primary: '#1B4332',
        secondary: '#2D5A3D', 
        success: '#28a745',
        info: '#17a2b8',
        warning: '#ffc107',
        danger: '#dc3545',
        light: '#f8f9fa',
        dark: '#343a40'
    },
    fonts: {
        primary: "'Cairo', 'Tahoma', 'Arial', sans-serif",
        size: {
            title: '18px',
            subtitle: '14px',
            content: '12px',
            small: '10px'
        }
    },
    spacing: {
        padding: '20px',
        margin: '15px',
        borderRadius: '12px'
    }
};

/**
 * إنشاء قالب HTML موحد للطباعة
 * @param {Object} data - بيانات التقرير
 * @returns {string} - HTML للطباعة
 */
function createUnifiedPrintTemplate(data) {
    const {
        title = 'تقرير النظام',
        subtitle = 'نظام إدارة المواعيد',
        content = [],
        showBorder = true,
        customStyles = ''
    } = data;

    // إنشاء محتوى التفاصيل
    const contentHTML = content.map(item => {
        const iconColor = item.color || getIconColor(item.icon);
        const boxedClass = item.boxed ? 'boxed-detail' : '';
        const valueStyle = item.color ? `color: ${item.color}; font-weight: bold;` : '';
        return `
            <div class="detail-row ${boxedClass}">
                <div class="detail-icon" style="color: ${iconColor};">
                    <i class="${item.icon}"></i>
                </div>
                <div class="detail-content">
                    <div class="detail-label">${item.label}</div>
                    <div class="detail-value" style="${valueStyle}">${item.value}</div>
                </div>
            </div>
        `;
    }).join('');

    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                ${getUnifiedPrintStyles(showBorder)}
                ${customStyles}
            </style>
        </head>
        <body>
            <div class="print-container ${showBorder ? 'with-border' : ''}">
                <!-- الهيدر المبسط بدون شعار -->
                <div class="print-header">
                    <div class="header-text">
                        <h1 class="main-title">${title}</h1>
                        <h2 class="sub-title">${subtitle}</h2>
                    </div>
                </div>

                <!-- عنوان تفاصيل الاجتماع -->
                <div class="meeting-details-header">
                    <h2 class="details-title">تفاصيل الاجتماع</h2>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="print-content">
                    ${contentHTML}
                </div>
                
                <!-- تذييل الصفحة مع التاريخ -->
                <div class="unified-print-footer">
                    <div class="unified-footer-info">
                        <div class="footer-left">
                            تاريخ الطباعة: 2025/01/01
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
    `;
}

/**
 * الحصول على لون الأيقونة حسب نوعها
 */
function getIconColor(iconClass) {
    const iconColorMap = {
        'fa-file-alt': '#17a2b8',      // أزرق فاتح
        'fa-tag': '#6f42c1',           // بنفسجي
        'fa-calendar': '#28a745',      // أخضر
        'fa-clock': '#ffc107',         // أصفر
        'fa-map-marker-alt': '#dc3545', // أحمر
        'fa-building': '#17a2b8',      // أزرق فاتح
        'fa-user-clock': '#fd7e14',    // برتقالي
        'fa-file': '#6c757d',          // رمادي
        'fa-calendar-check': '#20c997', // أخضر فاتح
        'fa-user-tie': '#e83e8c',      // وردي
        'fa-sticky-note': '#ffc107'    // أصفر
    };

    // البحث عن اللون المناسب
    for (const [icon, color] of Object.entries(iconColorMap)) {
        if (iconClass.includes(icon)) {
            return color;
        }
    }
    
    return UNIFIED_PRINT_CONFIG.colors.primary; // اللون الافتراضي
}

/**
 * الحصول على أنماط CSS الموحدة
 */
function getUnifiedPrintStyles(showBorder = true) {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ${UNIFIED_PRINT_CONFIG.fonts.primary};
            font-size: ${UNIFIED_PRINT_CONFIG.fonts.size.content};
            line-height: 1.6;
            color: #333;
            background: white;
            direction: rtl;
            text-align: right;
        }

        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: ${UNIFIED_PRINT_CONFIG.spacing.padding};
            background: white;
            min-height: 297mm;
            position: relative;
        }

        .print-container.with-border {
            border: 3px solid ${UNIFIED_PRINT_CONFIG.colors.primary};
            border-radius: ${UNIFIED_PRINT_CONFIG.spacing.borderRadius};
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .print-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid ${UNIFIED_PRINT_CONFIG.colors.primary};
        }

        .main-title {
            font-size: ${UNIFIED_PRINT_CONFIG.fonts.size.title};
            font-weight: bold;
            color: ${UNIFIED_PRINT_CONFIG.colors.primary};
            margin: 10px 0 5px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .sub-title {
            font-size: ${UNIFIED_PRINT_CONFIG.fonts.size.subtitle};
            color: ${UNIFIED_PRINT_CONFIG.colors.secondary};
            font-weight: 500;
        }

        .print-content {
            margin: 20px 0;
        }

        .detail-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 12px;
            background: ${UNIFIED_PRINT_CONFIG.colors.light};
            border-radius: 8px;
            border-right: 4px solid ${UNIFIED_PRINT_CONFIG.colors.primary};
        }

        .detail-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 50%;
            margin-left: 15px;
            font-size: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .detail-content {
            flex: 1;
        }

        .detail-label {
            font-weight: bold;
            color: ${UNIFIED_PRINT_CONFIG.colors.dark};
            margin-bottom: 3px;
            font-size: ${UNIFIED_PRINT_CONFIG.fonts.size.content};
        }

        .detail-value {
            color: #555;
            font-size: ${UNIFIED_PRINT_CONFIG.fonts.size.content};
            line-height: 1.4;
        }



        @media print {
            @page {
                size: A4;
                margin: 0.5cm;
            }
            
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .print-container {
                box-shadow: none;
                border: 2px solid ${UNIFIED_PRINT_CONFIG.colors.primary} !important;
            }
        }

        @media screen and (max-width: 768px) {
            .print-container {
                padding: 15px;
            }
        }
    `;
}

/**
 * طباعة تقرير موحد
 * @param {Object} data - بيانات التقرير
 */
function printUnifiedReport(data) {
    try {
        console.log('🖨️ بدء طباعة التقرير الموحد:', data.title);

        // إنشاء محتوى HTML
        const printHTML = createUnifiedPrintTemplate(data);

        // فتح نافذة طباعة جديدة
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        if (!printWindow) {
            throw new Error('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
        }

        // كتابة المحتوى
        printWindow.document.write(printHTML);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();

                // إغلاق النافذة بعد الطباعة (اختياري)
                printWindow.onafterprint = function() {
                    setTimeout(() => {
                        printWindow.close();
                    }, 1000);
                };
            }, 500);
        };

        console.log('✅ تم فتح نافذة الطباعة بنجاح');

    } catch (error) {
        console.error('❌ خطأ في الطباعة:', error);
        alert('حدث خطأ أثناء الطباعة: ' + error.message);
    }
}

/**
 * تنسيق الوقت مع إضافة صباحاً/مساءً
 * @param {string} timeStr - الوقت بصيغة نصية
 * @returns {string} - الوقت المنسق
 */
function formatTimeWithPeriod(timeStr) {
    try {
        if (!timeStr) return 'غير محدد';

        // تحويل الوقت إلى ساعة ودقيقة
        const [hours, minutes] = timeStr.split(':').map(Number);

        // تحديد الفترة
        let period = '';
        if (hours >= 0 && hours < 12) {
            period = 'صباحاً';
        } else {
            period = 'مساءً';
        }

        return `${timeStr} ${period}`;
    } catch (error) {
        console.error('خطأ في تنسيق الوقت:', error);
        return timeStr; // إرجاع الوقت الأصلي في حالة حدوث خطأ
    }
}

/**
 * تحديد لون الحالة
 * @param {string} status - حالة الاجتماع
 * @returns {string} - لون الحالة
 */
function getStatusColor(status) {
    switch(status) {
        case 'نشط':
        case 'active':
            return '#28a745'; // أخضر
        case 'مؤجل':
        case 'postponed':
            return '#ffc107'; // أصفر
        case 'ملغي':
        case 'cancelled':
            return '#dc3545'; // أحمر
        case 'منتهي':
        case 'completed':
            return '#6c757d'; // رمادي
        default:
            return '#17a2b8'; // أزرق
    }
}

/**
 * طباعة تفاصيل اجتماع
 * @param {Object} meeting - بيانات الاجتماع
 */
function printMeetingReport(meeting) {
    console.log('🖨️ طباعة تقرير الاجتماع:', meeting);
    console.log('🔍 فحص الحالة المستلمة:', {
        status: meeting.status,
        meeting_status: meeting.meeting_status,
        is_cancelled: meeting.is_cancelled,
        is_postponed: meeting.is_postponed
    });

    // تحديد عنوان الاجتماع الديناميكي
    let meetingTitle = meeting.subject || 'اجتماع';

    // تنظيف العنوان من كلمة "اجتماع" المكررة
    if (meetingTitle.toLowerCase().includes('اجتماع')) {
        meetingTitle = meetingTitle.replace(/اجتماع/gi, '').trim();
        meetingTitle = `اجتماع ${meetingTitle}`;
    } else {
        meetingTitle = `اجتماع ${meetingTitle}`;
    }

    // تحديد العنوان الفرعي حسب الحالة الحقيقية من قاعدة البيانات
    let subtitle = '';

    // فحص الحالة من عدة مصادر محتملة
    const status = meeting.status || meeting.meeting_status || getMeetingStatus(meeting);

    console.log('🔍 الحالة المحددة:', status);

    if (status === 'مؤجل' || status === 'postponed' || meeting.is_postponed) {
        subtitle = 'مؤجل';
    } else if (status === 'ملغي' || status === 'cancelled' || meeting.is_cancelled) {
        subtitle = 'ملغي';
    } else if (status === 'منتهي' || status === 'completed' || status === 'finished') {
        subtitle = 'منتهي';
    } else if (status === 'نشط' || status === 'active') {
        subtitle = 'نشط';
    } else {
        // تحديد الحالة بناءً على التاريخ إذا لم تكن محددة
        const meetingDate = new Date(meeting.meeting_date || meeting.date);
        const today = new Date();

        if (meetingDate < today) {
            subtitle = 'منتهي';
        } else {
            subtitle = 'نشط';
        }
    }

    console.log('📋 العنوان الفرعي المحدد:', subtitle);

    // إنشاء المحتوى الأساسي
    const content = [
        { icon: 'fas fa-file-alt', label: 'الموضوع', value: meeting.subject || 'غير محدد' },
        { icon: 'fas fa-tag', label: 'نوع الفعالية', value: meeting.meeting_type || meeting.type || 'غير محدد' },
        { icon: 'fas fa-info-circle', label: 'الحالة', value: subtitle.replace('اجتماع ', ''), color: getStatusColor(status) }
    ];

    // إضافة معلومات التاريخ والوقت حسب الحالة
    if (status === 'مؤجل' || status === 'postponed' || meeting.is_postponed) {
        // للاجتماعات المؤجلة - عرض التاريخ الأصلي والجديد
        content.push(
            { icon: 'fas fa-calendar', label: 'التاريخ الأصلي', value: formatDateYYYYMMDD(meeting.meeting_date || meeting.date) },
            { icon: 'fas fa-clock', label: 'الوقت الأصلي', value: formatTimeWithPeriod(meeting.meeting_time || meeting.time) || 'غير محدد' }
        );

        // إضافة معلومات التأجيل
        if (meeting.new_date || meeting.postpone_reason) {
            content.push({
                icon: 'fas fa-exclamation-triangle',
                label: 'حالة الاجتماع',
                value: meeting.postpone_reason || `مؤجل${meeting.new_date ? ` إلى ${meeting.new_date}` : ' إلى إشعار آخر'}${meeting.new_time ? ` الساعة ${meeting.new_time}` : ''}`,
                color: '#ffc107',
                boxed: true
            });
        }
    } else if (status === 'ملغي' || status === 'cancelled' || meeting.is_cancelled) {
        // للاجتماعات الملغية
        content.push(
            { icon: 'fas fa-calendar', label: 'التاريخ', value: formatDateYYYYMMDD(meeting.meeting_date || meeting.date) },
            { icon: 'fas fa-clock', label: 'الوقت', value: formatTimeWithPeriod(meeting.meeting_time || meeting.time) || 'غير محدد' },
            {
                icon: 'fas fa-times-circle',
                label: 'حالة الاجتماع',
                value: meeting.cancellation_reason || 'ملغي',
                color: '#dc3545',
                boxed: true
            }
        );
    } else {
        // للاجتماعات النشطة أو المنتهية
        content.push(
            { icon: 'fas fa-calendar', label: 'التاريخ', value: formatDateYYYYMMDD(meeting.meeting_date || meeting.date) },
            { icon: 'fas fa-clock', label: 'الوقت', value: formatTimeWithPeriod(meeting.meeting_time || meeting.time) || 'غير محدد' }
        );
    }

    // إضافة باقي المعلومات
    content.push(
        { icon: 'fas fa-map-marker-alt', label: 'المكان', value: meeting.location || 'غير محدد' },
        { icon: 'fas fa-building', label: 'جهة الدعوة', value: meeting.inviting_party || 'غير محدد' },
        { icon: 'fas fa-user-tie', label: 'نوع اللباس', value: meeting.dress_code || 'غير محدد' },
        { icon: 'fas fa-user-clock', label: 'الحضور قبل الموعد', value: `${meeting.arrival_time_before || 15} دقيقة` },
        { icon: 'fas fa-file', label: 'رقم الكتاب', value: meeting.book_number || 'غير محدد', boxed: true },
        { icon: 'fas fa-calendar-check', label: 'تاريخ الكتاب', value: formatDateYYYYMMDD(meeting.book_date), boxed: true }
    );

    // لا نحتاج لإضافة معلومات إضافية هنا لأنها تمت إضافتها أعلاه

    // إضافة الملاحظات إذا وجدت
    if (meeting.notes && meeting.notes.trim()) {
        content.push({
            icon: 'fas fa-sticky-note',
            label: 'ملاحظات',
            value: meeting.notes
        });
    }

    const data = {
        title: meetingTitle,
        subtitle: subtitle,
        content: content,
        showLogo: true,
        showBorder: true
    };

    printUnifiedReport(data);
}

/**
 * طباعة إحصائية معينة
 * @param {string} cardType - نوع البطاقة
 * @param {Object} cardData - بيانات البطاقة
 */
function printStatCard(cardType, cardData = null) {
    // إذا لم يتم تمرير البيانات، حاول الحصول عليها من الصفحة
    if (!cardData) {
        cardData = extractCardData(cardType);
    }

    if (!cardData) {
        console.error('❌ لم يتم العثور على بيانات البطاقة:', cardType);
        alert('لم يتم العثور على بيانات البطاقة للطباعة');
        return;
    }

    const content = [
        {
            icon: 'fas fa-chart-bar',
            label: 'القيمة',
            value: cardData.value
        },
        {
            icon: 'fas fa-info-circle',
            label: 'الوصف',
            value: cardData.description || 'لا يوجد وصف'
        }
    ];

    const data = {
        title: cardData.title,
        subtitle: 'تقرير إحصائي - نظام إدارة المواعيد',
        content: content,
        showLogo: true,
        showBorder: true,
        customStyles: `
            .detail-row:first-child .detail-value {
                font-size: 24px;
                font-weight: bold;
                color: ${cardData.color || UNIFIED_PRINT_CONFIG.colors.primary};
            }
        `
    };

    printUnifiedReport(data);
}

/**
 * استخراج بيانات البطاقة من الصفحة
 * @param {string} cardType - نوع البطاقة
 * @returns {Object|null} - بيانات البطاقة
 */
function extractCardData(cardType) {
    // قائمة معرفات العناصر المحتملة
    const possibleIds = [
        cardType,
        cardType.replace('-', ''),
        cardType.replace(/-/g, ''),
        cardType + 'Value',
        cardType + 'Count'
    ];

    let element = null;
    let cardElement = null;

    // البحث عن العنصر
    for (const id of possibleIds) {
        element = document.getElementById(id);
        if (element) break;
    }

    if (!element) {
        console.warn('⚠️ لم يتم العثور على عنصر البطاقة:', cardType);
        return null;
    }

    // البحث عن البطاقة الأب
    cardElement = element.closest('.stat-card, .card, .dashboard-card, .col-md-4, .col-lg-4');

    if (!cardElement) {
        console.warn('⚠️ لم يتم العثور على البطاقة الأب');
        return null;
    }

    // استخراج البيانات
    const titleElement = cardElement.querySelector('h3, h4, h5, .card-title, .stat-title');
    const valueElement = cardElement.querySelector('.stat-value, .card-value, .number') || element;
    const descElement = cardElement.querySelector('.stat-description, .card-description, .description');

    return {
        title: titleElement ? titleElement.textContent.trim() : 'إحصائية',
        value: valueElement ? valueElement.textContent.trim() : '0',
        description: descElement ? descElement.textContent.trim() : '',
        color: getComputedStyle(valueElement || element).color
    };
}

// تصدير الوظائف للاستخدام العام
window.printUnifiedReport = printUnifiedReport;
window.printMeetingReport = printMeetingReport;
window.printStatCard = printStatCard;
window.createUnifiedPrintTemplate = createUnifiedPrintTemplate;

/**
 * تنسيق التاريخ إلى صيغة YYYY/MM/DD
 * @param {string} dateString - التاريخ بصيغة YYYY-MM-DD أو أي صيغة أخرى
 * @returns {string} - التاريخ بصيغة YYYY/MM/DD
 */
function formatDateYYYYMMDD(dateString) {
    if (!dateString) return 'غير محدد';

    // إذا كان التاريخ بصيغة YYYY-MM-DD
    if (dateString.includes('-')) {
        return dateString.replace(/-/g, '/');
    }

    // إذا كان التاريخ بصيغة أخرى، محاولة تحويله
    try {
        const date = new Date(dateString);
        if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}/${month}/${day}`;
        }
    } catch (e) {
        console.warn('خطأ في تحويل التاريخ:', dateString);
    }

    return dateString;
}

/**
 * تحديد الحالة الحقيقية للاجتماع
 * @param {Object} meeting - بيانات الاجتماع
 * @returns {string} - الحالة الحقيقية
 */
function getMeetingStatus(meeting) {
    if (meeting.is_cancelled) return 'ملغي';
    if (meeting.is_postponed) return 'مؤجل';

    // فحص إذا كان الاجتماع منتهي بناءً على التاريخ
    const today = new Date();
    const meetingDate = new Date(meeting.meeting_date || meeting.date);

    if (meetingDate < today) {
        return 'منتهي';
    }

    return 'نشط';
}

console.log('✅ تم تحميل قالب الطباعة الموحد بنجاح');
