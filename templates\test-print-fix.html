<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الطباعة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .demo-cards {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .demo-card {
            flex: 1;
            min-width: 200px;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            color: white;
            position: relative;
        }
        .demo-card h6 {
            margin-bottom: 10px;
            font-size: 14px;
        }
        .demo-card h4 {
            margin-bottom: 15px;
            font-size: 24px;
            font-weight: bold;
        }
        .print-btn {
            margin-top: 10px;
        }
        .bg-primary { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
        .bg-success { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .bg-warning { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
        .bg-secondary { background: linear-gradient(135deg, #6c757d 0%, #545b62 100%); }
        .bg-danger { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .fixes-list {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🔧 اختبار إصلاح الطباعة والتاريخ</h1>
        
        <div class="fixes-list">
            <h5>✅ الإصلاحات المطبقة:</h5>
            <ul>
                <li><strong>إصلاح التاريخ:</strong> تغيير من التقويم الهجري `١٤٤٧/٢/٣ هـ` إلى الميلادي `2025-07-28`</li>
                <li><strong>إصلاح زر الطباعة:</strong> إضافة تشخيص مفصل وبيانات تجريبية احتياطية</li>
                <li><strong>تحسين معالجة الأخطاء:</strong> رسائل واضحة في حالة فشل الطباعة</li>
                <li><strong>إضافة بيانات تجريبية:</strong> في حالة عدم وجود اجتماعات حقيقية</li>
            </ul>
        </div>

        <div class="demo-cards">
            <div class="demo-card bg-primary">
                <h6>إجمالي الاجتماعات</h6>
                <h4>15</h4>
                <button class="btn btn-outline-light btn-sm print-btn" onclick="testPrintFix('all')">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
            
            <div class="demo-card bg-success">
                <h6>الاجتماعات النشطة</h6>
                <h4>8</h4>
                <button class="btn btn-outline-light btn-sm print-btn" onclick="testPrintFix('active')">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
            
            <div class="demo-card bg-warning">
                <h6>الاجتماعات المؤجلة</h6>
                <h4>3</h4>
                <button class="btn btn-outline-light btn-sm print-btn" onclick="testPrintFix('postponed')">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
            
            <div class="demo-card bg-secondary">
                <h6>الاجتماعات الملغية</h6>
                <h4>2</h4>
                <button class="btn btn-outline-light btn-sm print-btn" onclick="testPrintFix('cancelled')">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
            
            <div class="demo-card bg-danger">
                <h6>الاجتماعات المنتهية</h6>
                <h4>2</h4>
                <button class="btn btn-outline-light btn-sm print-btn" onclick="testPrintFix('finished')">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
        </div>

        <div class="alert alert-info">
            <h5>🧪 كيفية الاختبار:</h5>
            <ol>
                <li>اضغط على أي زر "طباعة الكل" أعلاه</li>
                <li>تحقق من ظهور التاريخ بالصيغة الميلادية `2025-07-28`</li>
                <li>تأكد من عمل الطباعة حتى لو لم توجد بيانات حقيقية</li>
                <li>راقب رسائل التشخيص في Console</li>
            </ol>
        </div>

        <div class="console-output" id="consoleOutput">
            <div>🖥️ Console Output:</div>
            <div>انتظار اختبار الطباعة...</div>
        </div>

        <div class="alert alert-warning">
            <h5>⚠️ ملاحظات مهمة:</h5>
            <ul>
                <li>إذا لم تعمل الطباعة، تحقق من السماح بالنوافذ المنبثقة</li>
                <li>التاريخ الآن يظهر بصيغة `YYYY-MM-DD` بدلاً من الهجري</li>
                <li>في حالة عدم وجود اجتماعات، ستظهر بيانات تجريبية</li>
                <li>جميع رسائل التشخيص تظهر في Console</li>
            </ul>
        </div>
    </div>

    <script>
        // تسجيل رسائل Console في الصفحة
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : '#00ff00';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // اختبار إصلاح الطباعة
        function testPrintFix(filterType) {
            console.log('🔧 اختبار إصلاح الطباعة لنوع:', filterType);
            
            // إنشاء بيانات تجريبية
            const meetings = generateTestMeetings(filterType);
            
            if (meetings.length === 0) {
                console.error('❌ لا توجد اجتماعات للاختبار');
                return;
            }
            
            console.log(`📋 تم إنشاء ${meetings.length} اجتماع للاختبار`);
            
            // تحديد عنوان التقرير
            let reportTitle = '';
            switch(filterType) {
                case 'all': reportTitle = 'تقرير جميع الاجتماعات'; break;
                case 'active': reportTitle = 'تقرير الاجتماعات النشطة'; break;
                case 'postponed': reportTitle = 'تقرير الاجتماعات المؤجلة'; break;
                case 'cancelled': reportTitle = 'تقرير الاجتماعات الملغية'; break;
                case 'finished': reportTitle = 'تقرير الاجتماعات المنتهية'; break;
            }
            
            // اختبار التاريخ الجديد
            const newDate = new Date().toLocaleDateString('en-CA');
            console.log('📅 التاريخ الجديد (ميلادي):', newDate);
            
            // استدعاء دالة الطباعة
            printFixedReport(meetings, reportTitle);
        }

        // إنشاء بيانات تجريبية
        function generateTestMeetings(filterType) {
            const allMeetings = [
                { subject: 'اجتماع مجلس الإدارة الشهري', status: 'نشط', meeting_date: '2025-08-15', meeting_time: '10:30', location: 'قاعة الإدارة الرئيسية', inviting_party: 'مديرية الدائرة المالية' },
                { subject: 'اجتماع التخطيط الاستراتيجي', status: 'نشط', meeting_date: '2025-08-20', meeting_time: '09:00', location: 'قاعة التخطيط', inviting_party: 'إدارة التخطيط' },
                { subject: 'اجتماع المراجعة الشهرية', status: 'مؤجل', meeting_date: '2025-07-25', meeting_time: '11:00', location: 'قاعة المراجعة', inviting_party: 'إدارة المراجعة' },
                { subject: 'اجتماع المتابعة الأسبوعية', status: 'ملغي', meeting_date: '2025-07-30', meeting_time: '14:00', location: 'قاعة المتابعة', inviting_party: 'إدارة المتابعة' },
                { subject: 'اجتماع التقييم السنوي', status: 'منتهي', meeting_date: '2025-06-15', meeting_time: '13:00', location: 'قاعة التقييم', inviting_party: 'إدارة الموارد البشرية' },
                { subject: 'اجتماع الميزانية', status: 'نشط', meeting_date: '2025-08-25', meeting_time: '10:00', location: 'قاعة الميزانية', inviting_party: 'الإدارة المالية' }
            ];
            
            if (filterType === 'all') return allMeetings;
            
            const statusMap = {
                'active': 'نشط',
                'postponed': 'مؤجل', 
                'cancelled': 'ملغي',
                'finished': 'منتهي'
            };
            
            return allMeetings.filter(m => m.status === statusMap[filterType]);
        }

        // دالة طباعة محسنة مع التاريخ المصحح
        function printFixedReport(meetings, reportTitle) {
            console.log('🖨️ إنشاء تقرير محسن:', reportTitle);

            if (!meetings || meetings.length === 0) {
                console.error('❌ لا توجد اجتماعات للطباعة');
                return;
            }

            // إنشاء HTML للطباعة مع التاريخ المصحح
            let printHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>${reportTitle}</title>
                    <style>
                        body { font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif; direction: rtl; padding: 20px; line-height: 1.6; margin: 0; }
                        .header { text-align: center; margin-bottom: 30px; padding: 25px; border-bottom: 3px solid #1B4332; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 10px; }
                        .header h1 { color: #1B4332; margin-bottom: 15px; font-size: 28px; font-weight: bold; }
                        .header-info { display: flex; justify-content: center; gap: 30px; flex-wrap: wrap; margin-top: 15px; }
                        .header-item { background: white; padding: 10px 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                        .meetings-table { width: 100%; border-collapse: collapse; margin: 20px 0; background: white; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 10px; overflow: hidden; }
                        .meetings-table th { background: linear-gradient(135deg, #1B4332 0%, #2d5a3d 100%); color: white; padding: 15px 12px; text-align: center; font-weight: bold; font-size: 14px; border-bottom: 2px solid #0f2419; }
                        .meetings-table td { padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; vertical-align: middle; }
                        .meetings-table tr:nth-child(even) { background-color: #f8f9fa; }
                        .meetings-table tr:hover { background-color: #e9ecef; }
                        .meeting-subject { font-weight: bold; color: #1B4332; text-align: right; max-width: 300px; }
                        .row-number { background: #e9ecef; font-weight: bold; color: #495057; width: 50px; }
                        @media print { body { margin: 0; padding: 10px; } .meetings-table { page-break-inside: auto; } .meetings-table tr { page-break-inside: avoid; } .header { page-break-after: avoid; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>${reportTitle}</h1>
                        <div class="header-info">
                            <div class="header-item">
                                <strong>عدد الاجتماعات:</strong> ${meetings.length}
                            </div>
                            <div class="header-item">
                                <strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('en-CA')}
                            </div>
                        </div>
                    </div>
                    <table class="meetings-table">
                        <thead>
                            <tr>
                                <th style="width: 50px;">#</th>
                                <th style="width: 300px;">موضوع الاجتماع</th>
                                <th style="width: 120px;">التاريخ</th>
                                <th style="width: 100px;">الوقت</th>
                                <th style="width: 200px;">المكان</th>
                                <th style="width: 200px;">جهة الدعوة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            meetings.forEach((meeting, index) => {
                printHTML += `
                    <tr>
                        <td class="row-number">${index + 1}</td>
                        <td class="meeting-subject">${meeting.subject}</td>
                        <td>${meeting.meeting_date}</td>
                        <td>${meeting.meeting_time}</td>
                        <td>${meeting.location}</td>
                        <td>${meeting.inviting_party}</td>
                    </tr>
                `;
            });

            printHTML += `
                        </tbody>
                    </table>
                </body>
                </html>
            `;

            // فتح نافذة طباعة جديدة
            const printWindow = window.open('', '_blank', 'width=1000,height=700');
            if (!printWindow) {
                console.error('❌ تعذر فتح نافذة الطباعة');
                alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
                return;
            }

            console.log('✅ تم فتح نافذة الطباعة بنجاح');
            printWindow.document.write(printHTML);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.focus();
                    printWindow.print();
                    console.log('✅ تم تشغيل الطباعة بنجاح');
                }, 500);
            };

            console.log(`✅ تم إنشاء تقرير محسن لـ ${meetings.length} اجتماع`);
        }

        // رسالة ترحيب
        console.log('🚀 تم تحميل صفحة اختبار إصلاح الطباعة');
        console.log('📅 التاريخ الحالي (ميلادي):', new Date().toLocaleDateString('en-CA'));
        console.log('📅 التاريخ الحالي (هجري):', new Date().toLocaleDateString('ar-SA'));
    </script>
</body>
</html>
