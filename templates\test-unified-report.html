<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تقرير اجتماع الخطوة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .success-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .test-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            margin: 10px;
            padding: 12px 20px;
            border-radius: 8px;
            border: none;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .btn-primary { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
        .btn-success { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .preview-image {
            max-width: 100%;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            margin: 20px 0;
        }
        .feature-list {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🎯 اختبار تقرير اجتماع الخطوة الموحد</h1>
        
        <div class="success-badge">
            <h5>✅ تم إنشاء تقرير اجتماع الخطوة الموحد!</h5>
            <p class="mb-0">تصميم احترافي + بيانات حقيقية من قاعدة البيانات</p>
        </div>

        <div class="feature-list">
            <h5>🎨 مميزات التقرير الموحد المحسن:</h5>
            <ul>
                <li><strong>✅ تصميم "اجتماع الخطوة":</strong> تصميم احترافي مطابق للصورة المرفقة</li>
                <li><strong>✅ بيانات حقيقية:</strong> جلب من قاعدة البيانات مباشرة عبر API</li>
                <li><strong>✅ تخطيط شبكي محسن:</strong> عمودين منظمين (تفاصيل الاجتماع + جهة الدعوة)</li>
                <li><strong>✅ أيقونات ملونة:</strong> كل حقل له أيقونة ولون مميز</li>
                <li><strong>✅ تفاصيل كاملة:</strong> جميع البيانات المطلوبة كما في الصورة</li>
                <li><strong>✅ عناوين الأقسام:</strong> تقسيم واضح للمعلومات</li>
                <li><strong>✅ معلومات إضافية:</strong> رقم الكتاب والملاحظات</li>
                <li><strong>✅ ملاحظات ثابتة:</strong> تعليمات الحضور</li>
                <li><strong>✅ تاريخ YYYY/MM/DD:</strong> صيغة موحدة للتواريخ</li>
                <li><strong>✅ طباعة محسنة:</strong> تخطيط مُحسن للطباعة</li>
            </ul>
        </div>

        <div class="test-section">
            <h4>🖨️ اختبار التقرير الموحد</h4>
            <p>اختبار تقرير "اجتماع الخطوة" بالبيانات الحقيقية:</p>
            
            <div class="d-flex gap-2 flex-wrap">
                <button class="test-button btn-primary" onclick="testUnifiedReport(1)">
                    <i class="fas fa-print me-1"></i>
                    اختبار الاجتماع رقم 1
                </button>
                
                <button class="test-button btn-success" onclick="testUnifiedReport(2)">
                    <i class="fas fa-print me-1"></i>
                    اختبار الاجتماع رقم 2
                </button>
                
                <button class="test-button btn-primary" onclick="testUnifiedReport(3)">
                    <i class="fas fa-print me-1"></i>
                    اختبار الاجتماع رقم 3
                </button>
                
                <button class="test-button btn-success" onclick="testSampleReport()">
                    <i class="fas fa-print me-1"></i>
                    اختبار بيانات تجريبية
                </button>
            </div>
        </div>

        <div class="test-section">
            <h4>📋 مثال على التقرير</h4>
            <p>هكذا سيبدو التقرير الموحد:</p>
            <div class="text-center">
                <img src="data:image/svg+xml;base64,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" 
                     alt="مثال على التقرير الموحد" 
                     class="preview-image">
            </div>
        </div>

        <div class="console-output" id="consoleOutput">
            <div>🖥️ Console Output:</div>
            <div>انتظار اختبار التقرير الموحد...</div>
        </div>

        <div class="alert alert-info">
            <h5>📋 كيفية الاستخدام:</h5>
            <ol>
                <li>انتقل إلى صفحة الاجتماعات الرئيسية</li>
                <li>انقر على زر "طباعة" الأحمر تحت أي بطاقة اجتماع</li>
                <li>سيتم فتح تقرير "اجتماع الخطوة" الموحد</li>
                <li>التقرير يحتوي على جميع البيانات الحقيقية من قاعدة البيانات</li>
                <li>يمكن طباعة التقرير أو حفظه كـ PDF</li>
            </ol>
        </div>
    </div>

    <script>
        // تسجيل رسائل Console في الصفحة
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : '#00ff00';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // اختبار التقرير الموحد بمعرف اجتماع حقيقي
        function testUnifiedReport(meetingId) {
            console.log(`🔍 اختبار التقرير الموحد للاجتماع رقم: ${meetingId}`);
            
            // جلب بيانات الاجتماع من قاعدة البيانات
            fetch(`/api/meeting/${meetingId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    console.log('✅ تم جلب بيانات الاجتماع بنجاح:', data.meeting.subject);
                    printUnifiedMeetingReport(data.meeting);
                } else {
                    throw new Error(data.message || 'خطأ في جلب البيانات');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في جلب بيانات الاجتماع:', error);
                alert(`خطأ في جلب بيانات الاجتماع رقم ${meetingId}`);
            });
        }

        // اختبار التقرير ببيانات تجريبية
        function testSampleReport() {
            console.log('🖨️ اختبار التقرير ببيانات تجريبية');
            
            const sampleMeeting = {
                subject: 'اجتماع بيانات النظام',
                meeting_type: 'اجتماع رسمي',
                meeting_date: '2025-08-15',
                meeting_time: '10:00',
                location: 'قاعة الاجتماعات الرئيسية',
                inviting_party: 'مديرية الدائرة المالية',
                book_number: '12345',
                book_date: '2025-07-27',
                notes: 'اجتماع مهم لمناقشة تطوير النظام'
            };
            
            printUnifiedMeetingReport(sampleMeeting);
        }

        // دالة إنشاء تقرير اجتماع الخطوة الموحد (نسخة من الملف الأصلي)
        function printUnifiedMeetingReport(meeting) {
            console.log('🖨️ إنشاء تقرير اجتماع الخطوة الموحد');
            
            // تحويل التاريخ إلى صيغة YYYY/MM/DD
            const formatDate = (dateStr) => {
                if (!dateStr) return 'غير محدد';
                const date = new Date(dateStr);
                return date.toISOString().split('T')[0].replace(/-/g, '/');
            };
            
            // تحويل الوقت إلى صيغة مقروءة
            const formatTime = (timeStr) => {
                if (!timeStr) return 'غير محدد';
                return timeStr;
            };
            
            // إنشاء HTML للتقرير الموحد
            const reportHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>اجتماع الخطوة</title>
                    <style>
                        body {
                            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 20px;
                            background: #f8f9fa;
                            line-height: 1.6;
                        }
                        .report-container {
                            max-width: 800px;
                            margin: 0 auto;
                            background: white;
                            border-radius: 15px;
                            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                            overflow: hidden;
                        }
                        .header {
                            background: linear-gradient(135deg, #1B4332 0%, #2d5a3d 100%);
                            color: white;
                            padding: 30px;
                            text-align: center;
                            position: relative;
                        }
                        .header::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
                        }
                        .header h1 {
                            margin: 0;
                            font-size: 28px;
                            font-weight: bold;
                            position: relative;
                            z-index: 1;
                        }
                        .header .subtitle {
                            margin: 10px 0 0 0;
                            font-size: 16px;
                            opacity: 0.9;
                            position: relative;
                            z-index: 1;
                        }
                        .content {
                            padding: 40px;
                        }
                        .details-grid {
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 30px;
                            margin-bottom: 30px;
                        }
                        .detail-section {
                            background: #f8f9fa;
                            border-radius: 12px;
                            padding: 20px;
                            border-right: 4px solid #1B4332;
                        }
                        .detail-item {
                            display: flex;
                            align-items: center;
                            margin-bottom: 15px;
                            padding: 12px;
                            background: white;
                            border-radius: 8px;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                        }
                        .detail-item:last-child {
                            margin-bottom: 0;
                        }
                        .detail-icon {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-left: 15px;
                            font-size: 16px;
                            color: white;
                            flex-shrink: 0;
                        }
                        .detail-content {
                            flex: 1;
                        }
                        .detail-label {
                            font-weight: bold;
                            color: #1B4332;
                            font-size: 14px;
                            margin-bottom: 4px;
                        }
                        .detail-value {
                            color: #333;
                            font-size: 16px;
                        }
                        .icon-blue { background: linear-gradient(135deg, #007bff, #0056b3); }
                        .icon-green { background: linear-gradient(135deg, #28a745, #1e7e34); }
                        .icon-orange { background: linear-gradient(135deg, #fd7e14, #e55a00); }
                        .icon-purple { background: linear-gradient(135deg, #6f42c1, #5a32a3); }
                        .icon-red { background: linear-gradient(135deg, #dc3545, #c82333); }
                        .icon-teal { background: linear-gradient(135deg, #20c997, #17a2b8); }
                        .footer {
                            background: #f8f9fa;
                            padding: 20px 40px;
                            text-align: center;
                            border-top: 1px solid #dee2e6;
                            color: #666;
                            font-size: 14px;
                        }
                        @media print {
                            body { background: white; padding: 0; }
                            .report-container { box-shadow: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="header">
                            <h1>اجتماع الخطوة</h1>
                            <div class="subtitle">تفاصيل الاجتماع</div>
                        </div>
                        
                        <div class="content">
                            <div class="details-grid">
                                <!-- العمود الأيمن - تفاصيل الاجتماع -->
                                <div class="detail-section">
                                    <h4 style="color: #1B4332; margin-bottom: 20px; text-align: center; border-bottom: 2px solid #1B4332; padding-bottom: 10px;">تفاصيل الاجتماع</h4>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-blue">📋</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الموضوع</div>
                                            <div class="detail-value">${meeting.subject || 'غير محدد'}</div>
                                        </div>
                                    </div>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-green">🏢</div>
                                        <div class="detail-content">
                                            <div class="detail-label">نوع الفعالية</div>
                                            <div class="detail-value">${meeting.meeting_type || 'اجتماع'}</div>
                                        </div>
                                    </div>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-orange">📍</div>
                                        <div class="detail-content">
                                            <div class="detail-label">المكان</div>
                                            <div class="detail-value">${meeting.location || 'قاعة الاجتماعات'}</div>
                                        </div>
                                    </div>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-red">📅</div>
                                        <div class="detail-content">
                                            <div class="detail-label">التاريخ</div>
                                            <div class="detail-value">${formatDate(meeting.meeting_date)}</div>
                                        </div>
                                    </div>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-teal">🕐</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الوقت</div>
                                            <div class="detail-value">${formatTime(meeting.meeting_time)} مساءً</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- العمود الأيسر - جهة الدعوة -->
                                <div class="detail-section">
                                    <h4 style="color: #1B4332; margin-bottom: 20px; text-align: center; border-bottom: 2px solid #1B4332; padding-bottom: 10px;">جهة الدعوة</h4>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-purple">🏛️</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الجهة</div>
                                            <div class="detail-value">${meeting.inviting_party || 'مديرية الدائرة المالية'}</div>
                                        </div>
                                    </div>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-green">👔</div>
                                        <div class="detail-content">
                                            <div class="detail-label">نوع اللباس</div>
                                            <div class="detail-value">${meeting.dress_code || 'رسمي'}</div>
                                        </div>
                                    </div>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-orange">⏰</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الحضور في الموعد</div>
                                            <div class="detail-value">مطلوب</div>
                                        </div>
                                    </div>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-blue">📋</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الحالة</div>
                                            <div class="detail-value">${meeting.is_cancelled ? 'ملغي' : meeting.is_postponed ? 'مؤجل' : 'نشط'}</div>
                                        </div>
                                    </div>

                                    <div class="detail-item">
                                        <div class="detail-icon icon-red">📍</div>
                                        <div class="detail-content">
                                            <div class="detail-label">المكان</div>
                                            <div class="detail-value">${meeting.location || 'قاعة الاجتماعات'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- قسم معلومات إضافية -->
                            ${meeting.book_number || meeting.notes ? `
                            <div class="detail-section" style="grid-column: 1 / -1; margin-top: 30px;">
                                <h4 style="color: #1B4332; margin-bottom: 20px; text-align: center; border-bottom: 2px solid #1B4332; padding-bottom: 10px;">معلومات إضافية</h4>

                                ${meeting.book_number ? `
                                <div class="detail-item">
                                    <div class="detail-icon icon-purple">📄</div>
                                    <div class="detail-content">
                                        <div class="detail-label">رقم الكتاب</div>
                                        <div class="detail-value">${meeting.book_number}</div>
                                    </div>
                                </div>
                                ` : ''}

                                ${meeting.book_date ? `
                                <div class="detail-item">
                                    <div class="detail-icon icon-green">📅</div>
                                    <div class="detail-content">
                                        <div class="detail-label">تاريخ الكتاب</div>
                                        <div class="detail-value">${formatDate(meeting.book_date)}</div>
                                    </div>
                                </div>
                                ` : ''}

                                ${meeting.notes ? `
                                <div class="detail-item">
                                    <div class="detail-icon icon-orange">📝</div>
                                    <div class="detail-content">
                                        <div class="detail-label">ملاحظات</div>
                                        <div class="detail-value">${meeting.notes}</div>
                                    </div>
                                </div>
                                ` : ''}
                            </div>
                            ` : ''}


                        </div>
                        
                        <div class="footer">
                            تاريخ الطباعة: ${new Date().toISOString().split('T')[0].replace(/-/g, '/')}
                        </div>
                    </div>
                </body>
                </html>
            `;
            
            // فتح نافذة طباعة جديدة
            const printWindow = window.open('', '_blank', 'width=900,height=700');
            if (!printWindow) {
                console.error('❌ تعذر فتح نافذة الطباعة');
                alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
                return;
            }

            console.log('✅ تم فتح نافذة الطباعة بنجاح');
            printWindow.document.write(reportHTML);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.focus();
                    printWindow.print();
                    console.log('✅ تم تشغيل طباعة تقرير اجتماع الخطوة');
                }, 500);
            };

            console.log('✅ تم إنشاء تقرير اجتماع الخطوة الموحد بنجاح');
        }

        // رسالة ترحيب
        console.log('🚀 تم تحميل صفحة اختبار تقرير اجتماع الخطوة');
        console.log('🎯 التقرير الموحد جاهز للاختبار');
    </script>
</body>
</html>
