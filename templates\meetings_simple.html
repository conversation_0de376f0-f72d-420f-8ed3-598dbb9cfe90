{% extends "base.html" %}

{% block title %}جميع الاجتماعات - نظام إدارة المواعيد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">

    </div>

    <!-- إحصائيات سريعة - البطاقات الخمس في صف واحد بأحجام متساوية -->
    <div class="row mb-4 mt-4">
        <div class="col">
            <div class="card bg-primary text-white clickable-card" onclick="filterMeetings('all')" style="cursor: pointer;">
                <div class="card-body text-center">
                    <h6>إجمالي الاجتماعات</h6>
                    <h4>{{ meetings|length }}</h4>
                </div>
            </div>
            <div class="text-center mt-2">
                <button class="btn btn-outline-primary btn-sm" onclick="printAllMeetings('all')" title="طباعة جميع الاجتماعات">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
        </div>
        <div class="col">
            <div class="card bg-success text-white clickable-card" onclick="filterMeetings('active')" style="cursor: pointer;">
                <div class="card-body text-center">
                    <h6>الاجتماعات النشطة</h6>
                    <h4>{{ active_meetings }}</h4>
                </div>
            </div>
            <div class="text-center mt-2">
                <button class="btn btn-outline-success btn-sm" onclick="printAllMeetings('active')" title="طباعة الاجتماعات النشطة">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
        </div>
        <div class="col">
            <div class="card bg-warning text-white clickable-card" onclick="filterMeetings('postponed')" style="cursor: pointer;">
                <div class="card-body text-center">
                    <h6>الاجتماعات المؤجلة</h6>
                    <h4>{{ postponed_meetings }}</h4>
                </div>
            </div>
            <div class="text-center mt-2">
                <button class="btn btn-outline-warning btn-sm" onclick="printAllMeetings('postponed')" title="طباعة الاجتماعات المؤجلة">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
        </div>
        <div class="col">
            <div class="card text-white clickable-card" onclick="filterMeetings('cancelled')" style="cursor: pointer; background: linear-gradient(135deg, #4b2b82 0%, #6a4c93 100%);">
                <div class="card-body text-center">
                    <h6>الاجتماعات الملغية</h6>
                    <h4>{{ cancelled_meetings }}</h4>
                </div>
            </div>
            <div class="text-center mt-2">
                <button class="btn btn-outline-secondary btn-sm" onclick="printAllMeetings('cancelled')" title="طباعة الاجتماعات الملغية">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
        </div>
        <div class="col">
            <div class="card text-white clickable-card finished-card" data-filter="finished" onclick="filterMeetings('finished')" style="cursor: pointer; background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);">
                <div class="card-body text-center">
                    <h6>الاجتماعات المنتهية</h6>
                    <h4>{{ finished_meetings }}</h4>
                </div>
            </div>
            <div class="text-center mt-2">
                <button class="btn btn-outline-danger btn-sm" onclick="printAllMeetings('finished')" title="طباعة الاجتماعات المنتهية">
                    <i class="fas fa-print me-1"></i>
                    طباعة الكل
                </button>
            </div>
        </div>
    </div>

    <!-- قائمة الاجتماعات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الاجتماعات ({{ meetings|length }} اجتماع)
                    </h5>
                </div>
                <div class="card-body">
                    {% if meetings %}
                        {% for meeting in meetings %}
                        <div class="meeting-card meeting-item"
                             data-status="{{ meeting.display_status }}"
                             data-date="{{ meeting.meeting_date.strftime('%Y-%m-%d') }}"
                             data-date-display="{% set day_names = {0: 'الاثنين', 1: 'الثلاثاء', 2: 'الأربعاء', 3: 'الخميس', 4: 'الجمعة', 5: 'السبت', 6: 'الأحد'} %}{{ day_names[meeting.meeting_date.weekday()] }} {{ meeting.meeting_date.year }}/{{ '%02d'|format(meeting.meeting_date.month) }}/{{ '%02d'|format(meeting.meeting_date.day) }}"
                             data-meeting-id="{{ meeting.id }}">

                            <!-- رأس البطاقة -->
                            <div class="meeting-header">
                                <!-- زر الحالة في أقصى اليمين -->
                                <div class="status-row">
                                    <div class="meeting-status-right">
                                        {% if meeting.display_status == 'cancelled' %}
                                            <span class="status-badge cancelled">ملغي</span>
                                        {% elif meeting.display_status == 'postponed' %}
                                            <span class="status-badge postponed">مؤجل</span>
                                        {% elif meeting.display_status == 'finished' %}
                                            <span class="status-badge finished">منتهي</span>
                                        {% elif meeting.display_status == 'active' %}
                                            <span class="status-badge active">نشط</span>
                                        {% else %}
                                            <span class="status-badge active">نشط</span>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- الموضوع -->
                                <div class="meeting-subject">
                                    <i class="fas fa-clipboard-list meeting-subject-icon"></i>
                                    {{ meeting.subject }}
                                </div>

                                <!-- الأزرار تحت الموضوع -->
                                <div class="action-buttons">
                                    <a href="{{ url_for('meeting_details', meeting_id=meeting.id) }}" class="action-btn details-btn">
                                        <i class="fas fa-eye"></i>
                                        <span>التفاصيل</span>
                                    </a>

                                    <button class="action-btn print-btn"
                                            data-meeting-id="{{ meeting.id }}"
                                            data-meeting-subject="{{ meeting.subject }}"
                                            data-meeting-type="{{ meeting.meeting_type }}"
                                            data-meeting-date="{{ meeting.meeting_date }}"
                                            data-meeting-time="{{ meeting.meeting_time }}"
                                            data-meeting-location="{{ meeting.location }}"
                                            data-meeting-party="{{ meeting.inviting_party }}"
                                            data-meeting-dress-code="{{ meeting.dress_code }}"
                                            onclick="printMeetingUnified(this)">
                                        <i class="fas fa-print"></i>
                                        <span>طباعة</span>
                                    </button>
                                </div>
                            </div>

                            <!-- محتوى البطاقة -->
                            <div class="meeting-content">
                                <div class="meeting-info-grid">
                                    <div class="info-item">
                                        <div class="info-icon date">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="info-details">
                                            <span class="info-label">التاريخ</span>
                                            <span class="info-value">
                                                {% set day_names = {0: 'الاثنين', 1: 'الثلاثاء', 2: 'الأربعاء', 3: 'الخميس', 4: 'الجمعة', 5: 'السبت', 6: 'الأحد'} %}
                                                {{ day_names[meeting.meeting_date.weekday()] }} {{ meeting.meeting_date.year }}/{{ '%02d'|format(meeting.meeting_date.month) }}/{{ '%02d'|format(meeting.meeting_date.day) }}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-icon time">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="info-details">
                                            <span class="info-label">الوقت</span>
                                            <span class="info-value">{{ meeting.meeting_time | twelve_hour_time }}</span>
                                        </div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-icon location">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div class="info-details">
                                            <span class="info-label">المكان</span>
                                            <span class="info-value">{{ meeting.location }}</span>
                                        </div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-icon organization">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="info-details">
                                            <span class="info-label">جهة الدعوة</span>
                                            <span class="info-value">{{ meeting.inviting_party }}</span>
                                        </div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-icon dress-code">
                                            <i class="fas fa-user-tie"></i>
                                        </div>
                                        <div class="info-details">
                                            <span class="info-label">نوع اللباس</span>
                                            <span class="info-value">{{ meeting.dress_code }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تم نقل الأزرار إلى أعلى تحت الحالة -->
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <h4>لا توجد اجتماعات مسجلة حالياً</h4>
                            <p>يمكنك إضافة اجتماع جديد من خلال النقر على زر "إضافة اجتماع جديد" أعلاه</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>



<style>
.clickable-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    height: 100%;
    min-height: 120px;
}

.clickable-card .card-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 80px;
}

.clickable-card h6 {
    font-size: 1.1rem;
    font-weight: bold;
    line-height: 1.3;
    margin-bottom: 10px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.clickable-card h4 {
    font-size: 2.2rem;
    font-weight: 900;
    margin-bottom: 0 !important;
}

.clickable-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.clickable-card.active {
    border: 2px solid #fff;
    box-shadow: 0 0 20px rgba(255,255,255,0.5);
}

/* ظلال ملونة لجميع البطاقات عند النقر */

/* بطاقة جميع الاجتماعات - أزرق */
.clickable-card[onclick*="'all'"].selected {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6), 0 0 30px rgba(102, 126, 234, 0.4);
    border: 2px solid rgba(102, 126, 234, 0.8) !important;
    animation: allPulse 2s ease-in-out infinite;
}

@keyframes allPulse {
    0%, 100% {
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6), 0 0 30px rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.8), 0 0 40px rgba(102, 126, 234, 0.6);
    }
}

/* بطاقة الاجتماعات النشطة - أخضر */
.clickable-card[onclick*="'active'"].selected {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 40px rgba(40, 167, 69, 0.6), 0 0 30px rgba(40, 167, 69, 0.4);
    border: 2px solid rgba(40, 167, 69, 0.8) !important;
    animation: activePulse 2s ease-in-out infinite;
}

@keyframes activePulse {
    0%, 100% {
        box-shadow: 0 15px 40px rgba(40, 167, 69, 0.6), 0 0 30px rgba(40, 167, 69, 0.4);
    }
    50% {
        box-shadow: 0 15px 40px rgba(40, 167, 69, 0.8), 0 0 40px rgba(40, 167, 69, 0.6);
    }
}

/* بطاقة الاجتماعات المؤجلة - برتقالي */
.clickable-card[onclick*="'postponed'"].selected {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 40px rgba(255, 193, 7, 0.6), 0 0 30px rgba(255, 193, 7, 0.4);
    border: 2px solid rgba(255, 193, 7, 0.8) !important;
    animation: postponedPulse 2s ease-in-out infinite;
}

@keyframes postponedPulse {
    0%, 100% {
        box-shadow: 0 15px 40px rgba(255, 193, 7, 0.6), 0 0 30px rgba(255, 193, 7, 0.4);
    }
    50% {
        box-shadow: 0 15px 40px rgba(255, 193, 7, 0.8), 0 0 40px rgba(255, 193, 7, 0.6);
    }
}

/* بطاقة الاجتماعات الملغية - سكني غامق */
.clickable-card[onclick*="'cancelled'"].selected {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 40px rgba(75, 43, 130, 0.6), 0 0 30px rgba(75, 43, 130, 0.4);
    border: 2px solid rgba(75, 43, 130, 0.8) !important;
    animation: cancelledPulse 2s ease-in-out infinite;
}

@keyframes cancelledPulse {
    0%, 100% {
        box-shadow: 0 15px 40px rgba(75, 43, 130, 0.6), 0 0 30px rgba(75, 43, 130, 0.4);
    }
    50% {
        box-shadow: 0 15px 40px rgba(75, 43, 130, 0.8), 0 0 40px rgba(75, 43, 130, 0.6);
    }
}

/* بطاقة الاجتماعات المنتهية - أحمر */
.clickable-card[onclick*="'finished'"].selected {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 40px rgba(220, 53, 69, 0.6), 0 0 30px rgba(220, 53, 69, 0.4);
    border: 2px solid rgba(220, 53, 69, 0.8) !important;
    animation: finishedPulse 2s ease-in-out infinite;
}

@keyframes finishedPulse {
    0%, 100% {
        box-shadow: 0 15px 40px rgba(220, 53, 69, 0.6), 0 0 30px rgba(220, 53, 69, 0.4);
    }
    50% {
        box-shadow: 0 15px 40px rgba(220, 53, 69, 0.8), 0 0 40px rgba(220, 53, 69, 0.6);
    }
}

/* تصميم البطاقات الجديد */
.meeting-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(218, 165, 32, 0.2);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

/* ظلال ملونة لبطاقات تفاصيل الاجتماعات حسب الحالة */

/* الاجتماعات النشطة - ظل أخضر مع نبضات */
.meeting-card[data-status="active"]:not(.finished-meeting) {
    box-shadow: 0 8px 30px rgba(40, 167, 69, 0.4), 0 0 20px rgba(40, 167, 69, 0.2);
    border: 2px solid rgba(40, 167, 69, 0.6);
    background: linear-gradient(135deg, #f8fff8 0%, #f0fff0 100%);
    animation: activeMeetingPulse 3s ease-in-out infinite;
}

.meeting-card[data-status="active"]:not(.finished-meeting)::before {
    background: linear-gradient(90deg, #28a745, #20c997, #28a745);
    height: 6px;
    animation: greenBarPulse 2s ease-in-out infinite;
}

@keyframes activeMeetingPulse {
    0%, 100% {
        box-shadow: 0 8px 30px rgba(40, 167, 69, 0.4), 0 0 20px rgba(40, 167, 69, 0.2);
    }
    50% {
        box-shadow: 0 12px 40px rgba(40, 167, 69, 0.6), 0 0 30px rgba(40, 167, 69, 0.4);
    }
}

@keyframes greenBarPulse {
    0%, 100% {
        opacity: 0.8;
        height: 6px;
    }
    50% {
        opacity: 1;
        height: 8px;
    }
}

/* الاجتماعات المؤجلة - ظل برتقالي مع نبضات */
.meeting-card[data-status="postponed"] {
    box-shadow: 0 8px 30px rgba(255, 193, 7, 0.4), 0 0 20px rgba(255, 193, 7, 0.2);
    border: 2px solid rgba(255, 193, 7, 0.6);
    background: linear-gradient(135deg, #fffdf8 0%, #fffbf0 100%);
    animation: postponedMeetingPulse 3s ease-in-out infinite;
}

.meeting-card[data-status="postponed"]::before {
    background: linear-gradient(90deg, #ffc107, #fd7e14, #ffc107);
    height: 6px;
    animation: orangeBarPulse 2s ease-in-out infinite;
}

@keyframes postponedMeetingPulse {
    0%, 100% {
        box-shadow: 0 8px 30px rgba(255, 193, 7, 0.4), 0 0 20px rgba(255, 193, 7, 0.2);
    }
    50% {
        box-shadow: 0 12px 40px rgba(255, 193, 7, 0.6), 0 0 30px rgba(255, 193, 7, 0.4);
    }
}

@keyframes orangeBarPulse {
    0%, 100% {
        opacity: 0.8;
        height: 6px;
    }
    50% {
        opacity: 1;
        height: 8px;
    }
}

/* الاجتماعات الملغية - ظل سكني غامق مع نبضات */
.meeting-card[data-status="cancelled"] {
    box-shadow: 0 8px 30px rgba(75, 43, 130, 0.4), 0 0 20px rgba(75, 43, 130, 0.2);
    border: 2px solid rgba(75, 43, 130, 0.6);
    background: #ffffff;
    color: #333333;
    animation: cancelledMeetingPulse 3s ease-in-out infinite;
}

.meeting-card[data-status="cancelled"]::before {
    background: linear-gradient(90deg, #4b2b82, #6a4c93, #4b2b82);
    height: 6px;
    animation: darkPurpleBarPulse 2s ease-in-out infinite;
}

@keyframes cancelledMeetingPulse {
    0%, 100% {
        box-shadow: 0 8px 30px rgba(75, 43, 130, 0.4), 0 0 20px rgba(75, 43, 130, 0.2);
    }
    50% {
        box-shadow: 0 12px 40px rgba(75, 43, 130, 0.6), 0 0 30px rgba(75, 43, 130, 0.4);
    }
}

@keyframes redBarPulse {
    0%, 100% {
        opacity: 0.8;
        height: 6px;
    }
    50% {
        opacity: 1;
        height: 8px;
    }
}

/* الاجتماعات المنتهية - ظل أحمر */
.meeting-card.finished-meeting,
.meeting-item.finished-meeting {
    box-shadow: 0 8px 30px rgba(220, 53, 69, 0.4), 0 0 20px rgba(220, 53, 69, 0.2);
    border: 2px solid rgba(220, 53, 69, 0.6);
    background: #ffffff;
    color: #333333;
    animation: finishedMeetingPulse 3s ease-in-out infinite;
}

@keyframes finishedMeetingPulse {
    0%, 100% {
        box-shadow: 0 8px 30px rgba(220, 53, 69, 0.4), 0 0 20px rgba(220, 53, 69, 0.2);
    }
    50% {
        box-shadow: 0 12px 40px rgba(220, 53, 69, 0.6), 0 0 30px rgba(220, 53, 69, 0.4);
    }
}

/* الاجتماعات المنتهية - ظل أحمر مع نبضات */
.meeting-card[data-status="finished"] {
    box-shadow: 0 8px 30px rgba(220, 53, 69, 0.4), 0 0 20px rgba(220, 53, 69, 0.2);
    border: 2px solid rgba(220, 53, 69, 0.6);
    background: linear-gradient(135deg, #fff8f8 0%, #fff0f0 100%);
    animation: finishedMeetingPulse 3s ease-in-out infinite;
}

.meeting-card[data-status="finished"]::before {
    background: linear-gradient(90deg, #dc3545, #e74c3c, #dc3545);
    height: 6px;
    animation: redBarPulse 2s ease-in-out infinite;
}

@keyframes finishedMeetingPulse {
    0%, 100% {
        box-shadow: 0 8px 30px rgba(220, 53, 69, 0.4), 0 0 20px rgba(220, 53, 69, 0.2);
    }
    50% {
        box-shadow: 0 12px 40px rgba(220, 53, 69, 0.6), 0 0 30px rgba(220, 53, 69, 0.4);
    }
}

/* تأثيرات hover مخصصة لكل نوع */
.meeting-card[data-status="active"]:not(.finished-meeting):hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4), 0 0 25px rgba(40, 167, 69, 0.3);
    border-color: rgba(40, 167, 69, 0.6);
}

.meeting-card[data-status="postponed"]:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(255, 193, 7, 0.4), 0 0 25px rgba(255, 193, 7, 0.3);
    border-color: rgba(255, 193, 7, 0.6);
}

.meeting-card[data-status="cancelled"]:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(75, 43, 130, 0.4), 0 0 25px rgba(75, 43, 130, 0.3);
    border-color: rgba(75, 43, 130, 0.6);
}

.meeting-card[data-status="finished"]:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(220, 53, 69, 0.4), 0 0 25px rgba(220, 53, 69, 0.3);
    border-color: rgba(220, 53, 69, 0.6);
}

.meeting-card.finished-meeting:hover,
.meeting-item.finished-meeting:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(220, 53, 69, 0.5), 0 0 25px rgba(220, 53, 69, 0.4);
    border-color: rgba(220, 53, 69, 0.8);
}

.meeting-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #DAA520, #FFD700, #DAA520);
}

/* شريط أحمر للاجتماعات المنتهية */
.meeting-card.finished-meeting::before,
.meeting-item.finished-meeting::before {
    background: linear-gradient(90deg, #dc3545, #e74c3c, #dc3545) !important;
    height: 6px;
    animation: redBarPulse 2s ease-in-out infinite;
}

@keyframes redBarPulse {
    0%, 100% {
        opacity: 0.8;
        height: 6px;
    }
    50% {
        opacity: 1;
        height: 8px;
    }
}

/* رأس البطاقة */
.meeting-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-row {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 12px;
    width: 100%;
}

.meeting-status-right {
    flex-shrink: 0;
    margin-left: auto;
}

.meeting-subject {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.3;
    margin-bottom: 16px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.meeting-subject-icon {
    color: #DAA520;
    font-size: 2rem;
    flex-shrink: 0;
    margin-left: 12px;
    text-shadow: 0 3px 6px rgba(218, 165, 32, 0.4);
    transform: scale(1.1);
}

.meeting-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.meeting-icon {
    color: #DAA520;
    font-size: 20px;
    padding: 8px;
    background: rgba(218, 165, 32, 0.1);
    border-radius: 8px;
}

.meeting-title h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.3;
}

/* شارات الحالة - بنفس حجم الأزرار */
.status-badge {
    padding: 10px 18px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 110px;
    max-width: 110px;
    min-height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.status-badge.active {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.status-badge.postponed {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.status-badge.cancelled {
    background: linear-gradient(135deg, #4b2b82, #6a4c93);
    color: white;
    box-shadow: 0 2px 8px rgba(75, 43, 130, 0.3);
}

.status-badge.finished {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

/* محتوى البطاقة */
.meeting-content {
    padding: 20px 24px;
}

.meeting-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(218, 165, 32, 0.05);
    transform: translateX(4px);
}

.info-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.info-icon.date {
    background: linear-gradient(135deg, #FFD700, #FFA500);
}

.info-icon.time {
    background: linear-gradient(135deg, #FFD700, #FFA500);
}

.info-icon.location {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.info-icon.organization {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.info-icon.dress-code {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.info-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.info-label {
    font-size: 16px;
    color: #1a1a1a;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 600;
}

/* أسفل البطاقة */
.meeting-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(248, 249, 250, 0.5);
}

.action-buttons {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    gap: 8px;
    margin-top: 0;
    margin-left: 0;
    position: absolute;
    left: 20px;
    top: 20px;
}

.action-btn {
    border: none;
    padding: 10px 18px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    color: white;
    position: relative;
    overflow: hidden;
    min-height: 36px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    flex-shrink: 0;
    width: 110px;
    max-width: 110px;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.details-btn {
    background: linear-gradient(135deg, #4f46e5, #3730a3);
    border: 1px solid rgba(79, 70, 229, 0.2);
}

.details-btn:hover {
    background: linear-gradient(135deg, #3730a3, #312e81);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

/* تم حذف CSS الخاص بزر المرفقات */

.print-btn {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

.print-btn:hover {
    background: linear-gradient(135deg, #b91c1c, #991b1b);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.action-btn:active {
    transform: translateY(-1px);
    transition: transform 0.1s;
}

.action-btn i {
    font-size: 16px;
    opacity: 0.9;
}

.action-btn span {
    font-weight: 700;
    letter-spacing: 0.025em;
}

/* تجاوب الأزرار على الشاشات الصغيرة */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .action-btn {
        padding: 14px 18px;
        font-size: 14px;
        justify-content: center;
        width: auto;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .action-buttons {
        gap: 8px;
        align-items: flex-start;
    }

    .action-btn {
        padding: 12px 16px;
        font-size: 13px;
        min-height: 40px;
        min-width: 100px;
    }
}

.meeting-item.hidden {
    display: none !important;
}

/* تأكد من أن جميع الأعمدة لها نفس الارتفاع */
.row.mb-4 {
    display: flex;
    align-items: stretch;
}

.row.mb-4 .col {
    display: flex;
    flex-direction: column;
}
</style>



{% endblock %}

{% block extra_js %}
<script>
console.log('🚀 تم تحميل صفحة meetings_simple');

// متغير لتتبع الفلتر الحالي
let currentFilter = 'active';

// وظيفة الفلترة
function filterMeetings(filterType) {
    console.log('🔍 تطبيق فلتر:', filterType);

    // تحديد الاجتماعات المنتهية أولاً
    markFinishedMeetings();

    // إزالة التفعيل من جميع البطاقات
    document.querySelectorAll('.clickable-card').forEach(card => {
        card.classList.remove('active', 'selected');
    });

    // تفعيل البطاقة المختارة
    const activeCard = document.querySelector(`[onclick="filterMeetings('${filterType}')"]`);
    if (activeCard) {
        activeCard.classList.add('active', 'selected');
        console.log('✅ تم تفعيل البطاقة:', activeCard);
    } else {
        console.warn('⚠️ لم يتم العثور على البطاقة للفلتر:', filterType);
    }

    // جلب جميع الاجتماعات
    const meetings = document.querySelectorAll('.meeting-item');
    console.log(`📋 تم العثور على ${meetings.length} اجتماع`);

    const today = new Date().toISOString().split('T')[0];
    let visibleCount = 0;

    meetings.forEach((meeting, index) => {
        const status = meeting.getAttribute('data-status');
        const meetingDate = meeting.getAttribute('data-date');
        let shouldShow = false;

        // تحديد ما إذا كان الاجتماع منتهياً
        const isFinished = meetingDate && meetingDate < today && status === 'active';

        console.log(`📋 اجتماع ${index + 1}:`, {
            status: status,
            meetingDate: meetingDate,
            today: today,
            isFinished: isFinished,
            filterType: filterType
        });

        switch(filterType) {
            case 'all':
                shouldShow = true;
                break;
            case 'active':
                shouldShow = (status === 'active' && !isFinished);
                break;
            case 'finished':
                // الاجتماعات المنتهية: إما محددة كـ finished أو نشطة لكن تاريخها مضى
                shouldShow = (status === 'finished') || isFinished;
                break;
            case 'postponed':
                shouldShow = (status === 'postponed');
                break;
            case 'cancelled':
                shouldShow = (status === 'cancelled');
                break;
        }

        console.log(`📊 اجتماع ${index + 1} - shouldShow:`, shouldShow);

        if (shouldShow) {
            meeting.classList.remove('hidden');
            meeting.style.display = 'block';
            visibleCount++;
            console.log(`✅ عرض اجتماع ${index + 1}`);
        } else {
            meeting.classList.add('hidden');
            meeting.style.display = 'none';
            console.log(`❌ إخفاء اجتماع ${index + 1}`);
        }
    });

    console.log(`✅ تم عرض ${visibleCount} من أصل ${meetings.length} اجتماع`);
    currentFilter = filterType;
}

// وظيفة تحديد الاجتماعات المنتهية
function markFinishedMeetings() {
    const today = new Date().toISOString().split('T')[0];
    const meetings = document.querySelectorAll('.meeting-item');

    console.log('🔍 فحص الاجتماعات المنتهية...');
    console.log('📅 تاريخ اليوم:', today);
    console.log('📋 عدد الاجتماعات:', meetings.length);

    let finishedCount = 0;
    meetings.forEach((meeting, index) => {
        const meetingDate = meeting.getAttribute('data-date');
        const status = meeting.getAttribute('data-status');

        console.log(`اجتماع ${index + 1}:`, {
            date: meetingDate,
            status: status,
            isDatePast: meetingDate && meetingDate < today,
            isActive: status === 'active'
        });

        if (meetingDate && meetingDate < today && status === 'active') {
            // تحديث data-status
            meeting.setAttribute('data-status', 'finished');

            // إضافة class للتصميم
            meeting.classList.add('finished-meeting');

            // تحديث زر الحالة
            const statusBadge = meeting.querySelector('.status-badge');
            if (statusBadge && statusBadge.classList.contains('active')) {
                statusBadge.textContent = 'منتهي';
                statusBadge.classList.remove('active');
                statusBadge.classList.add('finished');
                console.log(`🎨 تم تحديث زر الحالة للاجتماع ${index + 1}`);
            }

            finishedCount++;
            console.log(`🔴 تم تحديد اجتماع ${index + 1} كمنتهي`);
        }
    });

    console.log(`📊 تم العثور على ${finishedCount} اجتماع منتهي`);
}

// وظيفة طباعة موحدة للاجتماع
function printMeetingUnified(buttonElement) {
    console.log('🖨️ بدء طباعة الاجتماع الفردي');
    console.log('🔍 فحص الزر:', buttonElement);

    // استخراج البيانات من attributes الزر مباشرة
    const meetingData = {
        subject: buttonElement.getAttribute('data-meeting-subject') || 'غير محدد',
        meeting_date: buttonElement.getAttribute('data-meeting-date') || 'غير محدد',
        meeting_time: buttonElement.getAttribute('data-meeting-time') || 'غير محدد',
        location: buttonElement.getAttribute('data-meeting-location') || 'غير محدد',
        inviting_party: buttonElement.getAttribute('data-meeting-party') || 'غير محدد',
        status: 'نشط'
    };

    console.log('📄 البيانات المستخرجة من الزر:', meetingData);

    // التحقق من صحة البيانات
    if (!meetingData.subject || meetingData.subject === 'غير محدد' || meetingData.subject.length < 2) {
        console.error('❌ بيانات الاجتماع غير صالحة للطباعة:', meetingData);
        alert('خطأ: بيانات الاجتماع غير مكتملة');
        return;
    }

    // طباعة الاجتماع الفردي باستخدام التقرير المبسط
    console.log('✅ طباعة الاجتماع الفردي:', meetingData.subject);

    // إنشاء مصفوفة تحتوي على اجتماع واحد فقط
    const singleMeetingArray = [meetingData];

    // تحديد عنوان التقرير
    const reportTitle = `تقرير اجتماع: ${meetingData.subject}`;

    // استخدام دالة الطباعة المبسطة
    if (typeof printSimpleReport === 'function') {
        printSimpleReport(singleMeetingArray, reportTitle);
    } else {
        console.error('❌ دالة printSimpleReport غير متوفرة');
        alert('خطأ: دالة الطباعة غير متوفرة');
    }
}

// استخراج بيانات الاجتماع من عنصر HTML
function extractMeetingDataFromElement(element) {
    const getTextContent = (selector) => {
        const el = element.querySelector(selector);
        return el ? el.textContent.trim() : '';
    };

    // استخراج البيانات من بنية HTML
    const subject = getTextContent('.card-title') || getTextContent('h5') || 'غير محدد';
    const status = element.getAttribute('data-status') || 'غير محدد';

    // تحويل الحالة إلى نص عربي
    let statusText = '';
    switch(status) {
        case 'active':
            statusText = 'نشط';
            break;
        case 'postponed':
            statusText = 'مؤجل';
            break;
        case 'cancelled':
            statusText = 'ملغي';
            break;
        case 'finished':
            statusText = 'منتهي';
            break;
        default:
            statusText = 'غير محدد';
    }

    // استخراج باقي البيانات
    const dateElement = element.querySelector('.text-muted');
    const dateText = dateElement ? dateElement.textContent : '';

    // استخراج التاريخ والوقت من النص
    let meeting_date = element.getAttribute('data-date') || 'غير محدد';
    let meeting_time = 'غير محدد';

    if (dateText.includes('📅')) {
        const dateMatch = dateText.match(/📅\s*([^\s]+)/);
        if (dateMatch) meeting_date = dateMatch[1];
    }

    if (dateText.includes('🕐')) {
        const timeMatch = dateText.match(/🕐\s*([^\s]+)/);
        if (timeMatch) meeting_time = timeMatch[1];
    }

    return {
        subject: subject,
        status: statusText,
        meeting_date: meeting_date,
        meeting_time: meeting_time,
        date: meeting_date,
        time: meeting_time,
        location: getTextContent('.location') || 'غير محدد',
        inviting_party: getTextContent('.inviting-party') || 'مديرية الدائرة المالية',
        meeting_type: 'اجتماع إداري',
        is_cancelled: status === 'cancelled',
        is_postponed: status === 'postponed'
    };
}

// طباعة جميع الاجتماعات حسب النوع - البيانات الحقيقية من قاعدة البيانات
function printAllMeetings(filterType) {
    console.log('🖨️ طباعة جميع الاجتماعات من نوع:', filterType);

    // التحقق من وجود الدالة
    if (typeof printSimpleReport !== 'function') {
        console.error('❌ دالة printSimpleReport غير موجودة');
        alert('خطأ: دالة الطباعة غير متوفرة');
        return;
    }

    // جلب البيانات الحقيقية من قاعدة البيانات
    console.log('📡 جاري جلب البيانات الحقيقية من قاعدة البيانات...');

    fetch('/api/meetings/print-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            filter_type: filterType
        })
    })
    .then(response => {
        console.log('📡 استجابة الخادم:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log(`✅ تم جلب ${data.meetings.length} اجتماع من قاعدة البيانات`);

        if (data.meetings.length === 0) {
            alert(`لا توجد اجتماعات من نوع "${filterType}" في قاعدة البيانات`);
            return;
        }

        // طباعة التقرير بالبيانات الحقيقية
        printSimpleReport(data.meetings, data.report_title);
    })
    .catch(error => {
        console.error('❌ خطأ في جلب البيانات:', error);
        alert('خطأ في الاتصال بقاعدة البيانات. تأكد من تسجيل الدخول أولاً.');
    });
}



// إنشاء بيانات تجريبية للاختبار
function generateSampleMeetings(filterType) {
    console.log('🔧 إنشاء بيانات تجريبية لنوع:', filterType);

    const sampleMeetings = [
        { subject: 'اجتماع مجلس الإدارة الشهري', status: 'نشط', meeting_date: '2025-08-15', meeting_time: '10:30', location: 'قاعة الإدارة الرئيسية', inviting_party: 'مديرية الدائرة المالية' },
        { subject: 'اجتماع التخطيط الاستراتيجي', status: 'نشط', meeting_date: '2025-08-20', meeting_time: '09:00', location: 'قاعة التخطيط', inviting_party: 'إدارة التخطيط' },
        { subject: 'اجتماع المراجعة الشهرية', status: 'مؤجل', meeting_date: '2025-07-25', meeting_time: '11:00', location: 'قاعة المراجعة', inviting_party: 'إدارة المراجعة' },
        { subject: 'اجتماع المتابعة الأسبوعية', status: 'ملغي', meeting_date: '2025-07-30', meeting_time: '14:00', location: 'قاعة المتابعة', inviting_party: 'إدارة المتابعة' },
        { subject: 'اجتماع التقييم السنوي', status: 'منتهي', meeting_date: '2025-06-15', meeting_time: '13:00', location: 'قاعة التقييم', inviting_party: 'إدارة الموارد البشرية' },
        { subject: 'اجتماع الميزانية', status: 'نشط', meeting_date: '2025-08-25', meeting_time: '10:00', location: 'قاعة الميزانية', inviting_party: 'الإدارة المالية' },
        { subject: 'اجتماع التطوير', status: 'مؤجل', meeting_date: '2025-08-01', meeting_time: '15:30', location: 'قاعة التطوير', inviting_party: 'إدارة التطوير' },
        { subject: 'اجتماع الجودة', status: 'ملغي', meeting_date: '2025-07-28', meeting_time: '12:00', location: 'قاعة الجودة', inviting_party: 'إدارة الجودة' }
    ];

    if (filterType === 'all') {
        return sampleMeetings;
    }

    // فلترة حسب النوع
    const statusMap = {
        'active': 'نشط',
        'postponed': 'مؤجل',
        'cancelled': 'ملغي',
        'finished': 'منتهي'
    };

    const targetStatus = statusMap[filterType];
    if (!targetStatus) {
        return sampleMeetings; // إرجاع جميع الاجتماعات إذا كان النوع غير معروف
    }

    return sampleMeetings.filter(meeting => meeting.status === targetStatus);
}

// طباعة تقرير مبسط في شكل جدول
function printSimpleReport(meetings, reportTitle) {
    console.log('🖨️ إنشاء تقرير جدولي:', reportTitle);

    if (!meetings || meetings.length === 0) {
        alert('لا توجد اجتماعات للطباعة');
        return;
    }

    // إنشاء HTML للطباعة مع جدول
    let printHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${reportTitle}</title>
            <style>
                body {
                    font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
                    direction: rtl;
                    text-align: right;
                    padding: 20px;
                    line-height: 1.6;
                    margin: 0;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 25px;
                    border-bottom: 3px solid #1B4332;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-radius: 10px;
                }
                .header h1 {
                    color: #1B4332;
                    margin-bottom: 15px;
                    font-size: 28px;
                    font-weight: bold;
                }
                .header-info {
                    display: flex;
                    justify-content: space-around;
                    flex-wrap: wrap;
                    gap: 15px;
                    margin-top: 15px;
                }
                .header-item {
                    background: white;
                    padding: 10px 15px;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .meetings-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    background: white;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                    border-radius: 10px;
                    overflow: hidden;
                }
                .meetings-table th {
                    background: linear-gradient(135deg, #1B4332 0%, #2d5a3d 100%);
                    color: white;
                    padding: 15px 12px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 14px;
                    border-bottom: 2px solid #0f2419;
                }
                .meetings-table td {
                    padding: 12px;
                    text-align: center;
                    border-bottom: 1px solid #dee2e6;
                    vertical-align: middle;
                }
                .meetings-table tr:nth-child(even) {
                    background-color: #f8f9fa;
                }
                .meetings-table tr:hover {
                    background-color: #e9ecef;
                }

                .meeting-subject {
                    font-weight: bold;
                    color: #1B4332;
                    text-align: right;
                    max-width: 200px;
                }
                .row-number {
                    background: #e9ecef;
                    font-weight: bold;
                    color: #495057;
                    width: 50px;
                }

                @media print {
                    body { margin: 0; padding: 10px; }
                    .meetings-table { page-break-inside: auto; }
                    .meetings-table tr { page-break-inside: avoid; }
                    .header { page-break-after: avoid; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>${reportTitle}</h1>
                <div class="header-info">
                    <div class="header-item">
                        <strong>عدد الاجتماعات:</strong> ${meetings.length}
                    </div>
                    <div class="header-item">
                        <strong>تاريخ التقرير:</strong> ${new Date().toISOString().split('T')[0].replace(/-/g, '/')}
                    </div>
                </div>
            </div>

            <table class="meetings-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">#</th>
                        <th style="width: 300px;">موضوع الاجتماع</th>
                        <th style="width: 120px;">التاريخ</th>
                        <th style="width: 100px;">الوقت</th>
                        <th style="width: 200px;">المكان</th>
                        <th style="width: 200px;">جهة الدعوة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    meetings.forEach((meeting, index) => {
        printHTML += `
                    <tr>
                        <td class="row-number">${index + 1}</td>
                        <td class="meeting-subject">${meeting.subject}</td>
                        <td>${meeting.meeting_date}</td>
                        <td>${meeting.meeting_time}</td>
                        <td>${meeting.location}</td>
                        <td>${meeting.inviting_party}</td>
                    </tr>
        `;
    });

    printHTML += `
                </tbody>
            </table>
        </body>
        </html>
    `;

    // فتح نافذة طباعة جديدة
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (!printWindow) {
        alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
        return;
    }

    printWindow.document.write(printHTML);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم الطباعة
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.focus();
            printWindow.print();

            // إغلاق النافذة بعد الطباعة
            printWindow.onafterprint = function() {
                setTimeout(() => printWindow.close(), 1000);
            };
        }, 500);
    };

    console.log(`✅ تم إنشاء تقرير طباعة لـ ${meetings.length} اجتماع`);
}



// تطبيق الفلتر الافتراضي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 تم تحميل صفحة meetings_simple بنجاح');

    // تطبيق الفلتر الافتراضي
    setTimeout(() => {
        filterMeetings('active');
    }, 500);

    // التحقق من تحميل القالب الموحد
    setTimeout(() => {
        if (typeof printMeetingReport === 'function') {
            console.log('✅ تم تحميل القالب الموحد بنجاح');
        } else {
            console.log('⚠️ القالب الموحد غير متاح، سيتم استخدام الحل البديل');
        }
    }, 1000);
});
</script>

<!-- قالب الطباعة الموحد -->
<script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>
{% endblock %}
