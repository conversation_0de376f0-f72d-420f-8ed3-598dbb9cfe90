{% extends "base.html" %}

{% block title %}إعدادات الإشعارات - نظام إدارة المواعيد{% endblock %}

{% block extra_css %}
<style>
    .settings-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 20px;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        overflow: hidden;
    }
    
    .settings-header {
        background: rgba(255,255,255,0.1);
        padding: 30px;
        text-align: center;
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }
    
    .settings-body {
        padding: 40px;
        background: white;
        color: #333;
    }
    
    .whatsapp-icon {
        font-size: 4rem;
        color: #25D366;
        margin-bottom: 20px;
    }
    
    .form-floating .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .form-floating .form-control:focus {
        border-color: #25D366;
        box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
    }
    
    .form-check-input:checked {
        background-color: #25D366;
        border-color: #25D366;
    }
    
    .btn-whatsapp {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
    
    .btn-whatsapp:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        color: white;
    }
    
    .btn-test {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 10px 25px;
        border-radius: 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-test:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .notification-preview {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        font-family: monospace;
        direction: rtl;
        text-align: right;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
    }
    
    .status-enabled {
        background-color: #25D366;
        animation: pulse 2s infinite;
    }
    
    .status-disabled {
        background-color: #dc3545;
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    
    .feature-list {
        list-style: none;
        padding: 0;
    }
    
    .feature-list li {
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
    }
    
    .feature-list li:last-child {
        border-bottom: none;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-left: 15px;
        flex-shrink: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card settings-card">
                <div class="settings-header">
                    <i class="fab fa-whatsapp whatsapp-icon"></i>
                    <h2 class="mb-0">إعدادات إشعارات واتساب</h2>
                    <p class="mb-0 mt-2">إدارة الإشعارات التلقائية للاجتماعات</p>
                </div>
                
                <div class="settings-body">
                    <form method="POST" id="settingsForm">
                        <!-- حالة النظام -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded-3">
                                    <div>
                                        <h5 class="mb-1">
                                            <span class="status-indicator {% if config.ENABLED %}status-enabled{% else %}status-disabled{% endif %}"></span>
                                            حالة نظام الإشعارات
                                        </h5>
                                        <small class="text-muted">
                                            {% if config.ENABLED %}
                                                النظام مفعل وجاهز لإرسال الإشعارات
                                            {% else %}
                                                النظام معطل حالياً
                                            {% endif %}
                                        </small>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="notifications_enabled" 
                                               name="notifications_enabled" {% if config.ENABLED %}checked{% endif %}>
                                        <label class="form-check-label" for="notifications_enabled">
                                            تفعيل الإشعارات
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- رقم الواتساب -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                           value="{{ config.PHONE_NUMBER }}" placeholder="رقم الواتساب" required>
                                    <label for="phone_number">
                                        <i class="fab fa-whatsapp me-2"></i>
                                        رقم الواتساب (مع رمز الدولة)
                                    </label>
                                </div>
                                <div class="form-text">
                                    مثال: +962782146467 (يجب أن يبدأ بـ + ورمز الدولة)
                                </div>
                            </div>
                        </div>
                        
                        <!-- معاينة الإشعار -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>معاينة الإشعار</h5>
                                <div class="notification-preview" id="notificationPreview">
                                    <div style="text-align: center; font-weight: bold; font-size: 16px; margin-bottom: 15px;">
                                        معاينة الإشعار
                                    </div>
                                    <div style="border: 1px solid #ddd; padding: 15px; border-radius: 8px; background: white;">
                                        <div style="text-align: right; line-height: 1.8;">
                                            <strong>عطوفة المدير :-</strong><br>
                                            <strong>سيدي :</strong><br>
                                            تم إضافة اجتماع جديد لعطوفتكم و حسب التفصيلات التالية :<br><br>

                                            📋 <strong>الموضوع:</strong> اجتماع مجلس الإدارة<br>
                                            📅 <strong>التاريخ:</strong> 15/07/2025<br>
                                            🕐 <strong>الوقت:</strong> 10:00<br>
                                            📍 <strong>المكان:</strong> قاعة الاجتماعات الرئيسية<br>
                                            🏢 <strong>جهة الدعوة:</strong> مديرية الدائرة المالية<br><br>

                                            <strong>اللباس: موسمي</strong><br><br>

                                            <strong>و حسب الملف المرفق</strong>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="updatePreviewWithRealData()">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    تحديث بالبيانات الحقيقية
                                </button>
                            </div>
                        </div>
                        
                        <!-- الميزات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>الميزات المتاحة</h5>
                                <ul class="feature-list">
                                    <li>
                                        <div class="feature-icon">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                        <div>
                                            <strong>إشعار الاجتماع الجديد</strong><br>
                                            <small class="text-muted">يتم إرسال إشعار فور إضافة اجتماع جديد</small>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="feature-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div>
                                            <strong>إشعار التأجيل</strong><br>
                                            <small class="text-muted">يتم إرسال إشعار عند تأجيل اجتماع مع التاريخ الجديد</small>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="feature-icon">
                                            <i class="fas fa-times"></i>
                                        </div>
                                        <div>
                                            <strong>إشعار الإلغاء</strong><br>
                                            <small class="text-muted">يتم إرسال إشعار عند إلغاء اجتماع</small>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <button type="submit" class="btn btn-whatsapp w-100">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الإعدادات
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button type="button" class="btn btn-test w-100" onclick="testNotification()">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال رسالة اختبار
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        معلومات مهمة
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>متطلبات النظام:</h6>
                            <ul class="small">
                                <li>رقم واتساب صحيح ونشط</li>
                                <li>اتصال بالإنترنت</li>
                                <li>تفعيل الإشعارات في الإعدادات</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>أنواع الإشعارات:</h6>
                            <ul class="small">
                                <li>🆕 اجتماع جديد</li>
                                <li>⏰ تأجيل اجتماع</li>
                                <li>❌ إلغاء اجتماع</li>
                                <li>🧪 رسائل اختبار</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// اختبار إرسال إشعار
function testNotification() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    // تغيير النص أثناء الإرسال
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    btn.disabled = true;
    
    fetch('/api/notifications/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showSuccess('✅ ' + data.message);
            } else {
                alert('✅ ' + data.message);
            }
        } else {
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showError('❌ ' + data.message);
            } else {
                alert('❌ ' + data.message);
            }
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError('❌ حدث خطأ في إرسال رسالة الاختبار');
        } else {
            alert('❌ حدث خطأ في إرسال رسالة الاختبار');
        }
    })
    .finally(() => {
        // إعادة النص الأصلي
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// تحديث حالة النظام عند تغيير المفتاح
document.getElementById('notifications_enabled').addEventListener('change', function() {
    const indicator = document.querySelector('.status-indicator');
    const statusText = document.querySelector('.status-indicator').parentElement.querySelector('small');
    
    if (this.checked) {
        indicator.className = 'status-indicator status-enabled';
        statusText.textContent = 'النظام مفعل وجاهز لإرسال الإشعارات';
    } else {
        indicator.className = 'status-indicator status-disabled';
        statusText.textContent = 'النظام معطل حالياً';
    }
});

// التحقق من صحة رقم الهاتف
document.getElementById('phone_number').addEventListener('input', function() {
    const phone = this.value;
    const isValid = /^\+\d{10,15}$/.test(phone);

    if (phone && !isValid) {
        this.setCustomValidity('يرجى إدخال رقم هاتف صحيح يبدأ بـ + ورمز الدولة');
    } else {
        this.setCustomValidity('');
    }
});

// تحديث معاينة الإشعار بالبيانات الحقيقية
async function updatePreviewWithRealData() {
    try {
        // جلب أحدث اجتماع من قاعدة البيانات
        const response = await fetch('/api/meetings/latest');
        const data = await response.json();

        if (data.success && data.meeting) {
            const meeting = data.meeting;

            // تنسيق التاريخ
            const meetingDate = new Date(meeting.meeting_date);
            const formattedDate = meetingDate.toLocaleDateString('ar-SA', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });

            // تنسيق الوقت
            let formattedTime = 'غير محدد';
            if (meeting.meeting_time) {
                const timeStr = meeting.meeting_time;
                const timeParts = timeStr.split(':');
                if (timeParts.length >= 2) {
                    formattedTime = `${timeParts[0]}:${timeParts[1]}`;
                }
            }

            // تحديث المعاينة
            const previewDiv = document.getElementById('notificationPreview');
            previewDiv.innerHTML = `
                <div style="text-align: center; font-weight: bold; font-size: 16px; margin-bottom: 15px;">
                    معاينة الإشعار
                </div>
                <div style="border: 1px solid #ddd; padding: 15px; border-radius: 8px; background: white;">
                    <div style="text-align: right; line-height: 1.8;">
                        <strong>عطوفة المدير :-</strong><br>
                        <strong>سيدي :</strong><br>
                        تم إضافة اجتماع جديد لعطوفتكم و حسب التفصيلات التالية :<br><br>

                        📋 <strong>الموضوع:</strong> ${meeting.subject}<br>
                        📅 <strong>التاريخ:</strong> ${formattedDate}<br>
                        🕐 <strong>الوقت:</strong> ${formattedTime}<br>
                        📍 <strong>المكان:</strong> ${meeting.location || 'غير محدد'}<br>
                        🏢 <strong>جهة الدعوة:</strong> ${meeting.inviting_party || 'مديرية الدائرة المالية'}<br><br>

                        <strong>اللباس: موسمي</strong><br><br>

                        <strong>و حسب الملف المرفق</strong>
                    </div>
                </div>
            `;

            // إظهار رسالة نجاح
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showSuccess('✅ تم تحديث المعاينة بالبيانات الحقيقية');
            } else {
                alert('✅ تم تحديث المعاينة بالبيانات الحقيقية');
            }

        } else {
            // في حالة عدم وجود اجتماعات، استخدم بيانات تجريبية
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showInfo('ℹ️ لا توجد اجتماعات، تم عرض بيانات تجريبية');
            } else {
                alert('ℹ️ لا توجد اجتماعات، تم عرض بيانات تجريبية');
            }
        }

    } catch (error) {
        console.error('خطأ في جلب البيانات:', error);
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError('❌ حدث خطأ في جلب البيانات');
        } else {
            alert('❌ حدث خطأ في جلب البيانات');
        }
    }
}
</script>
{% endblock %}
