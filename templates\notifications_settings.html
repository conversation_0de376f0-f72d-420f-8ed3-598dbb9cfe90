{% extends "base.html" %}

{% block title %}إعدادات الإشعارات - نظام إدارة المواعيد{% endblock %}

{% block extra_css %}
<style>
    .settings-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 20px;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        overflow: hidden;
    }
    
    .settings-header {
        background: rgba(255,255,255,0.1);
        padding: 30px;
        text-align: center;
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }
    
    .settings-body {
        padding: 40px;
        background: white;
        color: #333;
    }
    
    .whatsapp-icon {
        font-size: 4rem;
        color: #25D366;
        margin-bottom: 20px;
    }
    
    .form-floating .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .form-floating .form-control:focus {
        border-color: #25D366;
        box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
    }
    
    .form-check-input:checked {
        background-color: #25D366;
        border-color: #25D366;
    }
    
    .btn-whatsapp {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
    
    .btn-whatsapp:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        color: white;
    }
    
    .btn-test {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 10px 25px;
        border-radius: 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-test:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .notification-preview {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        font-family: monospace;
        direction: rtl;
        text-align: right;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
    }
    
    .status-enabled {
        background-color: #25D366;
        animation: pulse 2s infinite;
    }
    
    .status-disabled {
        background-color: #dc3545;
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    
    .feature-list {
        list-style: none;
        padding: 0;
    }
    
    .feature-list li {
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
    }
    
    .feature-list li:last-child {
        border-bottom: none;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-left: 15px;
        flex-shrink: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card settings-card">
                <div class="settings-header">
                    <i class="fab fa-whatsapp whatsapp-icon"></i>
                    <h2 class="mb-0">إعدادات إشعارات واتساب</h2>
                    <p class="mb-0 mt-2">إدارة الإشعارات التلقائية للاجتماعات</p>
                </div>
                
                <div class="settings-body">
                    <form method="POST" id="settingsForm">
                        <!-- حالة النظام -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded-3">
                                    <div>
                                        <h5 class="mb-1">
                                            <span class="status-indicator {% if config.ENABLED %}status-enabled{% else %}status-disabled{% endif %}"></span>
                                            حالة نظام الإشعارات
                                        </h5>
                                        <small class="text-muted">
                                            {% if config.ENABLED %}
                                                النظام مفعل وجاهز لإرسال الإشعارات
                                            {% else %}
                                                النظام معطل حالياً
                                            {% endif %}
                                        </small>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="notifications_enabled" 
                                               name="notifications_enabled" {% if config.ENABLED %}checked{% endif %}>
                                        <label class="form-check-label" for="notifications_enabled">
                                            تفعيل الإشعارات
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- رقم الواتساب -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                           value="{{ config.PHONE_NUMBER }}" placeholder="رقم الواتساب" required>
                                    <label for="phone_number">
                                        <i class="fab fa-whatsapp me-2"></i>
                                        رقم الواتساب (مع رمز الدولة)
                                    </label>
                                </div>
                                <div class="form-text">
                                    مثال: +962782146467 (يجب أن يبدأ بـ + ورمز الدولة)
                                </div>
                            </div>
                        </div>
                        
                        <!-- معاينة الإشعار -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>معاينة الإشعار</h5>

                                <!-- اختيار نوع الإشعار -->
                                <div class="mb-3">
                                    <label class="form-label">نوع الإشعار:</label>
                                    <select class="form-select" id="notificationType" onchange="updatePreviewType()">
                                        <option value="new_meeting">🆕 اجتماع جديد</option>
                                        <option value="postponed_meeting">⏰ تأجيل اجتماع</option>
                                        <option value="cancelled_meeting">❌ إلغاء اجتماع</option>
                                        <option value="updated_meeting">✏️ تعديل اجتماع</option>
                                    </select>
                                </div>

                                <div class="notification-preview" id="notificationPreview">
                                    <div style="text-align: center; font-weight: bold; font-size: 16px; margin-bottom: 15px; color: #28a745;">
                                        🆕 معاينة الإشعار
                                    </div>
                                    <div style="border: 2px solid #28a745; padding: 15px; border-radius: 8px; background: white;">
                                        <div style="text-align: right; line-height: 1.6; white-space: pre-line;">
                                            <strong>عطوفة المدير :-</strong>
                                            <strong>سيدي :</strong>
                                            <span style="font-size: 18px; font-weight: bold; color: #2c3e50;">تم إضافة اجتماع جديد لعطوفتكم و حسب التفصيلات التالية :</span>

                                            🏢 <strong>جهة الدعوة:</strong> جاري التحميل...
                                            📅 <strong>التاريخ:</strong> --/--/----
                                            🕐 <strong>الوقت:</strong> --:-- -----
                                            📍 <strong>المكان:</strong> جاري التحميل...
                                            📋 <strong>الموضوع:</strong> جاري تحميل البيانات الحقيقية...
                                            👔 <strong>نوع اللباس:</strong> موسمي
                                            <strong>و حسب الملف المرفق</strong>

                                            📎 <strong>المرفقات:</strong>
                                            جاري تحميل المرفقات...
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="updatePreviewWithRealData()">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        تحديث بالبيانات الحقيقية
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm me-2" onclick="updatePreviewType()">
                                        <i class="fas fa-eye me-1"></i>
                                        معاينة النوع المحدد
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm me-2" onclick="testNotificationPreview()">
                                        <i class="fas fa-paper-plane me-1"></i>
                                        اختبار الإرسال
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="addSampleMeeting()">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة اجتماع تجريبي
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الميزات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>الميزات المتاحة</h5>
                                <ul class="feature-list">
                                    <li>
                                        <div class="feature-icon">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                        <div>
                                            <strong>إشعار الاجتماع الجديد</strong><br>
                                            <small class="text-muted">يتم إرسال إشعار فور إضافة اجتماع جديد</small>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="feature-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div>
                                            <strong>إشعار التأجيل</strong><br>
                                            <small class="text-muted">يتم إرسال إشعار عند تأجيل اجتماع مع التاريخ الجديد</small>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="feature-icon">
                                            <i class="fas fa-times"></i>
                                        </div>
                                        <div>
                                            <strong>إشعار الإلغاء</strong><br>
                                            <small class="text-muted">يتم إرسال إشعار عند إلغاء اجتماع</small>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <button type="button" class="btn btn-whatsapp w-100" onclick="saveSettings()">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الإعدادات
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button type="button" class="btn btn-test w-100" onclick="testNotification()">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال رسالة اختبار
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        معلومات مهمة
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>متطلبات النظام:</h6>
                            <ul class="small">
                                <li>رقم واتساب صحيح ونشط</li>
                                <li>اتصال بالإنترنت</li>
                                <li>تفعيل الإشعارات في الإعدادات</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>أنواع الإشعارات:</h6>
                            <ul class="small">
                                <li>🆕 اجتماع جديد</li>
                                <li>⏰ تأجيل اجتماع</li>
                                <li>❌ إلغاء اجتماع</li>
                                <li>🧪 رسائل اختبار</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/unified-notifications.js') }}"></script>
<script src="{{ url_for('static', filename='js/meeting-notifications.js') }}"></script>
<script>
// اختبار إرسال إشعار
function testNotification() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    // تغيير النص أثناء الإرسال
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    btn.disabled = true;
    
    // استخدام النظام الجديد لاختبار الإشعار
    testNotificationPreview()
        .finally(() => {
            // إعادة النص الأصلي
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

// تحديث حالة النظام عند تغيير المفتاح
document.getElementById('notifications_enabled').addEventListener('change', function() {
    const indicator = document.querySelector('.status-indicator');
    const statusText = document.querySelector('.status-indicator').parentElement.querySelector('small');
    
    if (this.checked) {
        indicator.className = 'status-indicator status-enabled';
        statusText.textContent = 'النظام مفعل وجاهز لإرسال الإشعارات';
    } else {
        indicator.className = 'status-indicator status-disabled';
        statusText.textContent = 'النظام معطل حالياً';
    }
});

// التحقق من صحة رقم الهاتف
document.getElementById('phone_number').addEventListener('input', function() {
    const phone = this.value;
    const isValid = /^\+\d{10,15}$/.test(phone);

    if (phone && !isValid) {
        this.setCustomValidity('يرجى إدخال رقم هاتف صحيح يبدأ بـ + ورمز الدولة');
    } else {
        this.setCustomValidity('');
    }
});

// تحديث معاينة الإشعار حسب النوع المحدد (بيانات حقيقية)
async function updatePreviewType() {
    const notificationType = document.getElementById('notificationType').value;

    try {
        // جلب أحدث اجتماع من قاعدة البيانات
        const response = await fetch('/api/meetings/latest-with-attachments');
        const data = await response.json();

        let meetingData, attachments;

        if (data.success && data.meeting) {
            // استخدام البيانات الحقيقية
            meetingData = data.meeting;
            attachments = data.attachments || [];
        } else {
            // في حالة عدم وجود بيانات، استخدام بيانات افتراضية
            meetingData = {
                subject: 'لا توجد اجتماعات في قاعدة البيانات',
                meeting_date: new Date().toISOString().split('T')[0],
                meeting_time: '10:00',
                location: 'غير محدد',
                inviting_party: 'مديرية الدائرة المالية'
            };
            attachments = [];
        }

        if (window.MeetingNotifications) {
            const previewHTML = window.MeetingNotifications.previewNotification(
                notificationType,
                meetingData,
                attachments
            );

            if (previewHTML) {
                document.getElementById('notificationPreview').innerHTML = previewHTML;
            }
        }

    } catch (error) {
        console.error('خطأ في جلب البيانات:', error);

        // في حالة الخطأ، استخدام بيانات افتراضية
        const fallbackMeeting = {
            subject: 'خطأ في جلب البيانات',
            meeting_date: new Date().toISOString().split('T')[0],
            meeting_time: '10:00',
            location: 'غير محدد',
            inviting_party: 'مديرية الدائرة المالية'
        };

        if (window.MeetingNotifications) {
            const previewHTML = window.MeetingNotifications.previewNotification(
                notificationType,
                fallbackMeeting,
                []
            );

            if (previewHTML) {
                document.getElementById('notificationPreview').innerHTML = previewHTML;
            }
        }
    }
}

// تحديث معاينة الإشعار بالبيانات الحقيقية
async function updatePreviewWithRealData() {
    try {
        const notificationType = document.getElementById('notificationType').value;

        // إظهار مؤشر التحميل
        document.getElementById('notificationPreview').innerHTML = `
            <div style="text-align: center; padding: 20px;">
                <i class="fas fa-spinner fa-spin fa-2x" style="color: #007bff;"></i>
                <p style="margin-top: 10px;">جاري تحميل البيانات الحقيقية...</p>
            </div>
        `;

        // جلب أحدث اجتماع من قاعدة البيانات
        const response = await fetch('/api/meetings/latest-with-attachments');
        const data = await response.json();

        if (data.success && data.meeting) {
            const meeting = data.meeting;
            const attachments = data.attachments || [];

            if (window.MeetingNotifications) {
                const previewHTML = window.MeetingNotifications.previewNotification(
                    notificationType,
                    meeting,
                    attachments
                );

                if (previewHTML) {
                    document.getElementById('notificationPreview').innerHTML = previewHTML;
                }
            }

            // إظهار رسالة نجاح
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showSuccess(`✅ تم تحديث المعاينة بالبيانات الحقيقية - الاجتماع: ${meeting.subject}`);
            } else {
                alert('✅ تم تحديث المعاينة بالبيانات الحقيقية');
            }

        } else {
            // في حالة عدم وجود اجتماعات
            document.getElementById('notificationPreview').innerHTML = `
                <div style="text-align: center; padding: 20px; color: #6c757d;">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <h5>لا توجد اجتماعات في قاعدة البيانات</h5>
                    <p>يرجى إضافة اجتماع أولاً لرؤية المعاينة الحقيقية</p>
                </div>
            `;

            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showInfo('ℹ️ لا توجد اجتماعات في قاعدة البيانات');
            } else {
                alert('ℹ️ لا توجد اجتماعات في قاعدة البيانات');
            }
        }

    } catch (error) {
        console.error('خطأ في جلب البيانات:', error);

        document.getElementById('notificationPreview').innerHTML = `
            <div style="text-align: center; padding: 20px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h5>حدث خطأ في تحميل البيانات</h5>
                <p>يرجى المحاولة مرة أخرى</p>
            </div>
        `;

        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError('❌ حدث خطأ في جلب البيانات');
        } else {
            alert('❌ حدث خطأ في جلب البيانات');
        }
    }
}

// اختبار إرسال الإشعار
async function testNotificationPreview() {
    try {
        const notificationType = document.getElementById('notificationType').value;

        const response = await fetch('/api/notifications/test-send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                type: notificationType
            })
        });

        const result = await response.json();

        if (result.success) {
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showSuccess('✅ تم إرسال الإشعار التجريبي بنجاح');
            } else {
                alert('✅ تم إرسال الإشعار التجريبي بنجاح');
            }
        } else {
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showError('❌ فشل في إرسال الإشعار: ' + result.error);
            } else {
                alert('❌ فشل في إرسال الإشعار: ' + result.error);
            }
        }

    } catch (error) {
        console.error('خطأ في اختبار الإشعار:', error);
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError('❌ حدث خطأ في اختبار الإشعار');
        } else {
            alert('❌ حدث خطأ في اختبار الإشعار');
        }
    }
}

// حفظ الإعدادات
async function saveSettings() {
    try {
        const formData = new FormData(document.getElementById('settingsForm'));

        const response = await fetch('/api/notifications/save-settings', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showSuccess('✅ تم حفظ الإعدادات بنجاح');
            } else {
                alert('✅ تم حفظ الإعدادات بنجاح');
            }
        } else {
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showError('❌ فشل في حفظ الإعدادات: ' + result.error);
            } else {
                alert('❌ فشل في حفظ الإعدادات: ' + result.error);
            }
        }

    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError('❌ حدث خطأ في حفظ الإعدادات');
        } else {
            alert('❌ حدث خطأ في حفظ الإعدادات');
        }
    }
}

// إعادة تعيين الإعدادات
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        document.getElementById('settingsForm').reset();
        updatePreviewType();

        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showInfo('ℹ️ تم إعادة تعيين الإعدادات');
        } else {
            alert('ℹ️ تم إعادة تعيين الإعدادات');
        }
    }
}

// إضافة اجتماع تجريبي للاختبار
async function addSampleMeeting() {
    try {
        if (!confirm('هل تريد إضافة اجتماع تجريبي لاختبار النظام؟')) {
            return;
        }

        const response = await fetch('/api/add-sample-meeting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showSuccess(`✅ تم إضافة اجتماع تجريبي: ${result.meeting.subject}`);
            } else {
                alert(`✅ تم إضافة اجتماع تجريبي: ${result.meeting.subject}`);
            }

            // تحديث المعاينة بالبيانات الجديدة
            setTimeout(() => {
                updatePreviewWithRealData();
            }, 1000);

        } else {
            if (window.UnifiedNotifications) {
                window.UnifiedNotifications.showError('❌ فشل في إضافة الاجتماع: ' + result.error);
            } else {
                alert('❌ فشل في إضافة الاجتماع: ' + result.error);
            }
        }

    } catch (error) {
        console.error('خطأ في إضافة الاجتماع:', error);
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError('❌ حدث خطأ في إضافة الاجتماع');
        } else {
            alert('❌ حدث خطأ في إضافة الاجتماع');
        }
    }
}

// تحميل المعاينة الافتراضية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        updatePreviewType();
    }, 500);
});
</script>
{% endblock %}
