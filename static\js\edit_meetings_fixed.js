/**
 * إصلاحات صفحة تعديل الاجتماعات
 * Edit Meetings Page Fixes for JAF Meeting System
 */

// متغيرات عامة
let editMeetingsData = [];
let currentEditingMeeting = null;

// تهيئة صفحة التعديل
function initializeEditMeetings() {
    console.log('✏️ تهيئة صفحة تعديل الاجتماعات...');
    
    // تحميل البيانات
    loadEditMeetingsData();
    
    // تهيئة الأحداث
    initializeEditEvents();
    
    // تهيئة المودالات
    initializeModals();
    
    console.log('✅ تم تهيئة صفحة تعديل الاجتماعات');
}

// تحميل بيانات الاجتماعات للتعديل
function loadEditMeetingsData() {
    console.log('📥 تحميل بيانات الاجتماعات للتعديل...');
    
    const meetingRows = document.querySelectorAll('#meetingsTableBody tr');
    editMeetingsData = Array.from(meetingRows).map((row, index) => {
        return {
            id: row.getAttribute('data-meeting-id') || index + 1,
            element: row,
            subject: row.querySelector('.subject-column .fw-bold')?.textContent?.trim() || '',
            date: row.querySelector('.date-column .date-display')?.textContent?.trim() || '',
            time: row.querySelector('.date-column .time-display')?.textContent?.trim() || '',
            location: row.querySelector('.location-column')?.textContent?.trim() || '',
            type: row.querySelector('.meeting-type-badge')?.textContent?.trim() || '',
            status: getRowStatus(row),
            invitingParty: row.querySelector('.inviting-party-column')?.textContent?.trim() || ''
        };
    });
    
    console.log(`✅ تم تحميل ${editMeetingsData.length} اجتماع للتعديل`);
}

// الحصول على حالة الصف
function getRowStatus(row) {
    if (row.classList.contains('cancelled')) return 'ملغي';
    if (row.classList.contains('postponed')) return 'مؤجل';
    return 'نشط';
}

// تهيئة أحداث التعديل
function initializeEditEvents() {
    console.log('🎯 تهيئة أحداث التعديل...');
    
    // أحداث البحث والفلترة
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleEditSearch);
        // تطبيق الفلتر عند التحميل إذا كان هناك قيمة
        if (searchInput.value.trim()) {
            setTimeout(filterEditMeetings, 100);
        }
    }

    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', handleEditFilter);
        // تطبيق الفلتر عند التحميل إذا كان هناك قيمة
        if (statusFilter.value) {
            setTimeout(filterEditMeetings, 100);
        }
    }

    const typeFilter = document.getElementById('typeFilter');
    if (typeFilter) {
        typeFilter.addEventListener('change', handleEditFilter);
        // تطبيق الفلتر عند التحميل إذا كان هناك قيمة
        if (typeFilter.value) {
            setTimeout(filterEditMeetings, 100);
        }
    }
    
    // أحداث أزرار الإجراءات
    document.addEventListener('click', handleEditActionClick);
    
    console.log('✅ تم تهيئة أحداث التعديل');
}

// تهيئة المودالات
function initializeModals() {
    console.log('💬 تهيئة المودالات...');
    
    // مودال التأجيل
    const postponeModal = document.getElementById('postponeMeetingModal');
    if (postponeModal) {
        // إضافة أحداث الأزرار السريعة
        initializeQuickButtons();
    }
    
    console.log('✅ تم تهيئة المودالات');
}

// تهيئة الأزرار السريعة
function initializeQuickButtons() {
    // أزرار التاريخ السريع
    const quickDateButtons = document.querySelectorAll('[onclick^="setPostponeQuickDate"]');
    quickDateButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const type = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            setPostponeQuickDate(type);
        });
    });
    
    // أزرار الوقت السريع
    const quickTimeButtons = document.querySelectorAll('[onclick^="setPostponeQuickTime"]');
    quickTimeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const time = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            setPostponeQuickTime(time);
        });
    });
}

// معالجة البحث في التعديل
function handleEditSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    console.log('🔍 البحث في التعديل:', searchTerm);
    
    filterEditMeetings();
}

// معالجة فلترة التعديل
function handleEditFilter(event) {
    console.log('🔄 فلترة التعديل:', event.target.id, event.target.value);
    
    filterEditMeetings();
}

// فلترة اجتماعات التعديل
function filterEditMeetings() {
    console.log('🔍 فلترة اجتماعات التعديل...');

    const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const typeFilter = document.getElementById('typeFilter')?.value || '';

    const tbody = document.querySelector('#meetingsTable tbody');
    if (!tbody) {
        console.error('❌ لم يتم العثور على جدول الاجتماعات');
        return;
    }

    // جمع الصفوف المطابقة والغير مطابقة
    const matchingRows = [];
    const nonMatchingRows = [];

    editMeetingsData.forEach(meeting => {
        const matchesSearch = !searchTerm ||
            meeting.subject.toLowerCase().includes(searchTerm) ||
            meeting.location.toLowerCase().includes(searchTerm);

        const matchesStatus = !statusFilter ||
            (statusFilter === 'active' && meeting.status === 'نشط') ||
            (statusFilter === 'cancelled' && meeting.status === 'ملغي') ||
            (statusFilter === 'postponed' && meeting.status === 'مؤجل');

        const matchesType = !typeFilter || meeting.type.includes(typeFilter);

        const shouldShow = matchesSearch && matchesStatus && matchesType;

        if (shouldShow) {
            matchingRows.push(meeting.element);
        } else {
            nonMatchingRows.push(meeting.element);
        }
    });

    // إزالة جميع الصفوف من الجدول
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }

    // إضافة الصفوف المطابقة فقط في الأعلى
    matchingRows.forEach(row => {
        row.style.display = '';
        tbody.appendChild(row);
    });

    // عرض إحصائيات الفلترة
    console.log(`🔍 عرض ${matchingRows.length} من ${editMeetingsData.length} اجتماع`);

    // إضافة رسالة إذا لم توجد نتائج
    if (matchingRows.length === 0) {
        const noResultsRow = document.createElement('tr');
        noResultsRow.innerHTML = `
            <td colspan="100%" style="text-align: center; padding: 20px; color: #666;">
                <i class="fas fa-search"></i> لا توجد نتائج مطابقة للبحث
            </td>
        `;
        tbody.appendChild(noResultsRow);
    }
}

// إعادة تعيين الفلاتر وإظهار جميع الاجتماعات
function resetEditFilters() {
    console.log('🔄 إعادة تعيين فلاتر التعديل...');

    // مسح قيم الفلاتر
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const typeFilter = document.getElementById('typeFilter');

    if (searchInput) searchInput.value = '';
    if (statusFilter) statusFilter.value = '';
    if (typeFilter) typeFilter.value = '';

    // إعادة عرض جميع الاجتماعات
    const tbody = document.querySelector('#meetingsTable tbody');
    if (tbody) {
        // إزالة جميع الصفوف
        while (tbody.firstChild) {
            tbody.removeChild(tbody.firstChild);
        }

        // إعادة إضافة جميع الصفوف
        editMeetingsData.forEach(meeting => {
            meeting.element.style.display = '';
            tbody.appendChild(meeting.element);
        });

        console.log(`✅ تم عرض جميع ${editMeetingsData.length} اجتماع`);
    }
}

// معالجة نقرات أزرار الإجراءات
function handleEditActionClick(event) {
    const button = event.target.closest('button');
    if (!button) return;
    
    const action = button.getAttribute('onclick');
    const meetingId = button.getAttribute('data-meeting-id');
    
    if (action && meetingId) {
        console.log('🔘 إجراء التعديل:', action, 'للاجتماع:', meetingId);
        
        // منع التنفيذ المزدوج
        event.preventDefault();
        event.stopPropagation();
        
        // تنفيذ الإجراء
        if (action.includes('editMeeting')) {
            editMeeting(meetingId);
        } else if (action.includes('postponeMeeting')) {
            postponeMeeting(meetingId);
        } else if (action.includes('cancelMeeting')) {
            cancelMeeting(meetingId);
        } else if (action.includes('deleteMeeting')) {
            deleteMeeting(meetingId);
        } else if (action.includes('reactivateMeeting')) {
            reactivateMeeting(meetingId);
        }
    }
}

// تحسين وظائف التأجيل
function setPostponeQuickDate(type) {
    const dateInput = document.getElementById('newDate');
    if (!dateInput) return;
    
    const today = new Date();
    let targetDate = new Date(today);
    
    switch(type) {
        case 'today':
            // اليوم
            break;
        case 'tomorrow':
            targetDate.setDate(today.getDate() + 1);
            break;
        case 'next_week':
            targetDate.setDate(today.getDate() + 7);
            break;
        default:
            return;
    }
    
    dateInput.value = targetDate.toISOString().split('T')[0];
    console.log(`📅 تم تعيين التاريخ: ${type} -> ${dateInput.value}`);
}

function setPostponeQuickTime(time) {
    const timeInput = document.getElementById('newTime');
    if (!timeInput) return;
    
    timeInput.value = time;
    console.log(`🕐 تم تعيين الوقت: ${time}`);
}

// تحسين وظائف الإجراءات
function editMeeting(meetingId) {
    console.log('✏️ تعديل الاجتماع:', meetingId);
    
    // البحث عن الاجتماع
    const meeting = editMeetingsData.find(m => m.id == meetingId);
    if (!meeting) {
        console.error('❌ لم يتم العثور على الاجتماع');
        return;
    }
    
    currentEditingMeeting = meeting;
    
    // فتح مودال التعديل أو الانتقال لصفحة التعديل
    if (typeof showEditModal === 'function') {
        showEditModal(meetingId);
    } else {
        // الانتقال لصفحة التعديل
        window.location.href = `/edit_meeting/${meetingId}`;
    }
}

function postponeMeeting(meetingId) {
    console.log('⏰ تأجيل الاجتماع:', meetingId);
    
    currentEditingMeeting = editMeetingsData.find(m => m.id == meetingId);
    
    if (typeof showPostponeModal === 'function') {
        showPostponeModal(meetingId);
    } else {
        // فتح مودال التأجيل يدوياً
        const modal = document.getElementById('postponeMeetingModal');
        if (modal) {
            modal.setAttribute('data-meeting-id', meetingId);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }
}

function cancelMeeting(meetingId) {
    console.log('❌ إلغاء الاجتماع:', meetingId);
    
    if (confirm('هل أنت متأكد من إلغاء هذا الاجتماع؟')) {
        // استدعاء API الإلغاء
        fetch(`/api/meeting/${meetingId}/cancel`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage('تم إلغاء الاجتماع بنجاح');
                location.reload();
            } else {
                showErrorMessage(data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في إلغاء الاجتماع:', error);
            showErrorMessage('خطأ في الاتصال بالخادم');
        });
    }
}

function reactivateMeeting(meetingId) {
    console.log('🔄 إعادة تفعيل الاجتماع:', meetingId);
    
    if (confirm('هل أنت متأكد من إعادة تفعيل هذا الاجتماع؟')) {
        // استدعاء API إعادة التفعيل
        fetch(`/api/meeting/${meetingId}/reactivate`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage('تم إعادة تفعيل الاجتماع بنجاح');
                location.reload();
            } else {
                showErrorMessage(data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في إعادة تفعيل الاجتماع:', error);
            showErrorMessage('خطأ في الاتصال بالخادم');
        });
    }
}

// وظائف الرسائل - استخدام النظام الموحد
function showSuccessMessage(message) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showSuccess(message);
    } else {
        console.log('نجاح:', message);
        alert(message);
    }
}

function showErrorMessage(message) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showError(message);
    } else {
        console.log('خطأ:', message);
        alert(message);
    }
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل صفحة تعديل الاجتماعات');
    
    // تهيئة الصفحة
    initializeEditMeetings();
});

console.log('✏️ تم تحميل إصلاحات تعديل الاجتماعات');
