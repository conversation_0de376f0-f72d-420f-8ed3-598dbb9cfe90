<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة تفاصيل الاجتماع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .success-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .test-button {
            margin: 10px;
            padding: 15px 25px;
            border-radius: 8px;
            border: none;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .btn-primary { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
        .btn-danger { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .feature-list {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🖨️ اختبار طباعة تفاصيل الاجتماع</h1>
        
        <div class="success-badge">
            <h5>✅ تم تحديث نظام الطباعة!</h5>
            <p class="mb-0">الآن يتم جلب البيانات الحقيقية من قاعدة البيانات</p>
        </div>

        <div class="feature-list">
            <h5>🎨 التحسينات الجديدة:</h5>
            <ul>
                <li><strong>✅ بيانات حقيقية:</strong> يتم جلب البيانات من قاعدة البيانات عبر API</li>
                <li><strong>✅ تقرير محسن:</strong> نفس تصميم "تفاصيل الاجتماع" من الصورة</li>
                <li><strong>✅ معالجة الأخطاء:</strong> في حالة فشل API يتم استخدام البيانات المتاحة</li>
                <li><strong>✅ تصميم موحد:</strong> نفس التصميم في جميع صفحات الطباعة</li>
                <li><strong>✅ طباعة محسنة:</strong> صفحة واحدة مع جميع التفاصيل</li>
            </ul>
        </div>

        <div class="text-center">
            <h4>🖨️ اختبار الطباعة</h4>
            <p>اختبار طباعة تفاصيل الاجتماع بالبيانات الحقيقية:</p>
            
            <div class="d-flex gap-2 flex-wrap justify-content-center">
                <button class="test-button btn-primary" onclick="testMeetingDetailsPrint(1)">
                    <i class="fas fa-print me-1"></i>
                    اختبار الاجتماع رقم 1
                </button>
                
                <button class="test-button btn-danger" onclick="testMeetingDetailsPrint(2)">
                    <i class="fas fa-print me-1"></i>
                    اختبار الاجتماع رقم 2
                </button>
                
                <button class="test-button btn-primary" onclick="testSampleMeetingPrint()">
                    <i class="fas fa-print me-1"></i>
                    اختبار بيانات تجريبية
                </button>
            </div>
        </div>

        <div class="alert alert-info mt-4">
            <h5>📋 كيفية الاستخدام:</h5>
            <ol>
                <li>انتقل إلى صفحة تفاصيل أي اجتماع</li>
                <li>انقر على زر "طباعة" الأحمر</li>
                <li>سيتم جلب البيانات الحقيقية من قاعدة البيانات</li>
                <li>سيظهر تقرير "تفاصيل الاجتماع" المحسن</li>
                <li>يمكن طباعة التقرير أو حفظه كـ PDF</li>
            </ol>
        </div>

        <div class="alert alert-warning mt-4">
            <h5>⚠️ ملاحظة مهمة:</h5>
            <p>إذا لم تعمل الطباعة من صفحة التفاصيل، تأكد من:</p>
            <ul>
                <li>وجود اجتماعات في قاعدة البيانات</li>
                <li>تسجيل الدخول بحساب صحيح</li>
                <li>السماح بالنوافذ المنبثقة في المتصفح</li>
            </ul>
        </div>
    </div>

    <script>
        // اختبار طباعة تفاصيل الاجتماع بمعرف حقيقي
        function testMeetingDetailsPrint(meetingId) {
            console.log(`🔍 اختبار طباعة تفاصيل الاجتماع رقم: ${meetingId}`);
            
            // جلب بيانات الاجتماع من قاعدة البيانات
            fetch(`/api/meeting/${meetingId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    console.log('✅ تم جلب بيانات الاجتماع بنجاح:', data.meeting.subject);
                    printUnifiedMeetingReport(data.meeting);
                } else {
                    throw new Error(data.message || 'خطأ في جلب البيانات');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في جلب بيانات الاجتماع:', error);
                alert(`خطأ في جلب بيانات الاجتماع رقم ${meetingId}: ${error.message}`);
            });
        }

        // اختبار الطباعة ببيانات تجريبية
        function testSampleMeetingPrint() {
            console.log('🖨️ اختبار الطباعة ببيانات تجريبية');
            
            const sampleMeeting = {
                subject: 'اجتماع اختبار النظام المحدث',
                meeting_type: 'اجتماع رسمي',
                meeting_date: '2025-08-15',
                meeting_time: '10:00',
                location: 'قاعة الاجتماعات الرئيسية',
                inviting_party: 'مديرية الدائرة المالية',
                dress_code: 'رسمي',
                book_number: '12345',
                book_date: '2025-07-27',
                notes: 'اجتماع مهم لاختبار النظام المحدث',
                is_cancelled: false,
                is_postponed: false
            };
            
            printUnifiedMeetingReport(sampleMeeting);
        }

        // دالة إنشاء تقرير تفاصيل الاجتماع المحسن (نسخة محدثة)
        function printUnifiedMeetingReport(meeting) {
            console.log('🖨️ إنشاء تقرير تفاصيل الاجتماع المحسن');
            
            // تحويل التاريخ إلى صيغة YYYY/MM/DD
            const formatDate = (dateStr) => {
                if (!dateStr) return 'غير محدد';
                const date = new Date(dateStr);
                return date.toISOString().split('T')[0].replace(/-/g, '/');
            };
            
            // تحويل الوقت إلى صيغة مقروءة
            const formatTime = (timeStr) => {
                if (!timeStr) return 'غير محدد';
                return timeStr;
            };
            
            // إنشاء HTML للتقرير المحسن
            const reportHTML = \`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>تفاصيل الاجتماع</title>
                    <style>
                        body {
                            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 15px;
                            background: #f8f9fa;
                            line-height: 1.4;
                            font-size: 14px;
                        }
                        .report-container {
                            max-width: 210mm;
                            min-height: 297mm;
                            margin: 0 auto;
                            background: white;
                            border-radius: 10px;
                            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                            overflow: hidden;
                            page-break-inside: avoid;
                        }
                        .header {
                            background: linear-gradient(135deg, #1B4332 0%, #2d5a3d 100%);
                            color: white;
                            padding: 20px;
                            text-align: center;
                        }
                        .header h1 {
                            margin: 0;
                            font-size: 24px;
                            font-weight: bold;
                        }
                        .header .subtitle {
                            margin: 8px 0 0 0;
                            font-size: 14px;
                            opacity: 0.9;
                        }
                        .content {
                            padding: 25px;
                        }
                        .details-grid {
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 20px;
                            margin-bottom: 20px;
                        }
                        .detail-section {
                            background: #f8f9fa;
                            border-radius: 8px;
                            padding: 15px;
                            border-right: 3px solid #1B4332;
                        }
                        .detail-item {
                            display: flex;
                            align-items: center;
                            margin-bottom: 10px;
                            padding: 8px;
                            background: white;
                            border-radius: 6px;
                            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                        }
                        .detail-item:last-child {
                            margin-bottom: 0;
                        }
                        .detail-icon {
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-left: 12px;
                            font-size: 14px;
                            color: white;
                            flex-shrink: 0;
                        }
                        .detail-content {
                            flex: 1;
                        }
                        .detail-label {
                            font-weight: bold;
                            color: #1B4332;
                            font-size: 12px;
                            margin-bottom: 2px;
                        }
                        .detail-value {
                            color: #333;
                            font-size: 14px;
                        }
                        .icon-blue { background: linear-gradient(135deg, #007bff, #0056b3); }
                        .icon-green { background: linear-gradient(135deg, #28a745, #1e7e34); }
                        .icon-orange { background: linear-gradient(135deg, #fd7e14, #e55a00); }
                        .icon-purple { background: linear-gradient(135deg, #6f42c1, #5a32a3); }
                        .icon-red { background: linear-gradient(135deg, #dc3545, #c82333); }
                        .icon-teal { background: linear-gradient(135deg, #20c997, #17a2b8); }
                        .footer {
                            background: #f8f9fa;
                            padding: 15px 25px;
                            text-align: center;
                            border-top: 1px solid #dee2e6;
                            color: #666;
                            font-size: 12px;
                        }
                        @media print {
                            body { 
                                background: white; 
                                padding: 0; 
                                margin: 0;
                                font-size: 12px;
                            }
                            .report-container { 
                                box-shadow: none; 
                                max-width: 100%;
                                margin: 0;
                                border-radius: 0;
                                page-break-inside: avoid;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="header">
                            <h1>تفاصيل الاجتماع</h1>
                        </div>
                        
                        <div class="content">
                            <div class="details-grid">
                                <div class="detail-section">
                                    <h3 style="color: #1B4332; margin-bottom: 15px; text-align: center; border-bottom: 3px solid #1B4332; padding-bottom: 8px; font-size: 18px; font-weight: bold;">تفاصيل الاجتماع</h3>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-blue">📋</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الموضوع</div>
                                            <div class="detail-value">\${meeting.subject || 'غير محدد'}</div>
                                        </div>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-green">🏢</div>
                                        <div class="detail-content">
                                            <div class="detail-label">نوع الفعالية</div>
                                            <div class="detail-value">\${meeting.meeting_type || 'اجتماع'}</div>
                                        </div>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-orange">📍</div>
                                        <div class="detail-content">
                                            <div class="detail-label">المكان</div>
                                            <div class="detail-value">\${meeting.location || 'قاعة الاجتماعات'}</div>
                                        </div>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-red">📅</div>
                                        <div class="detail-content">
                                            <div class="detail-label">التاريخ</div>
                                            <div class="detail-value">\${formatDate(meeting.meeting_date)}</div>
                                        </div>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-teal">🕐</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الوقت</div>
                                            <div class="detail-value">\${formatTime(meeting.meeting_time)} مساءً</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="detail-section">
                                    <h3 style="color: #1B4332; margin-bottom: 15px; text-align: center; border-bottom: 3px solid #1B4332; padding-bottom: 8px; font-size: 18px; font-weight: bold;">جهة الدعوة</h3>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-purple">🏛️</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الجهة</div>
                                            <div class="detail-value">\${meeting.inviting_party || 'مديرية الدائرة المالية'}</div>
                                        </div>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-green">👔</div>
                                        <div class="detail-content">
                                            <div class="detail-label">نوع اللباس</div>
                                            <div class="detail-value">\${meeting.dress_code || 'رسمي'}</div>
                                        </div>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-orange">⏰</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الحضور في الموعد</div>
                                            <div class="detail-value">مطلوب</div>
                                        </div>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-blue">📋</div>
                                        <div class="detail-content">
                                            <div class="detail-label">الحالة</div>
                                            <div class="detail-value">\${meeting.is_cancelled ? 'ملغي' : meeting.is_postponed ? 'مؤجل' : 'نشط'}</div>
                                        </div>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <div class="detail-icon icon-red">📍</div>
                                        <div class="detail-content">
                                            <div class="detail-label">المكان</div>
                                            <div class="detail-value">\${meeting.location || 'قاعة الاجتماعات'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            \${meeting.book_number || meeting.notes ? \`
                            <div class="detail-section" style="grid-column: 1 / -1; margin-top: 15px;">
                                <h3 style="color: #1B4332; margin-bottom: 15px; text-align: center; border-bottom: 3px solid #1B4332; padding-bottom: 8px; font-size: 18px; font-weight: bold;">معلومات إضافية</h3>
                                
                                \${meeting.book_number ? \`
                                <div class="detail-item">
                                    <div class="detail-icon icon-purple">📄</div>
                                    <div class="detail-content">
                                        <div class="detail-label">رقم الكتاب</div>
                                        <div class="detail-value">\${meeting.book_number}</div>
                                    </div>
                                </div>
                                \` : ''}
                                
                                \${meeting.book_date ? \`
                                <div class="detail-item">
                                    <div class="detail-icon icon-green">📅</div>
                                    <div class="detail-content">
                                        <div class="detail-label">تاريخ الكتاب</div>
                                        <div class="detail-value">\${formatDate(meeting.book_date)}</div>
                                    </div>
                                </div>
                                \` : ''}
                                
                                \${meeting.notes ? \`
                                <div class="detail-item">
                                    <div class="detail-icon icon-orange">📝</div>
                                    <div class="detail-content">
                                        <div class="detail-label">ملاحظات</div>
                                        <div class="detail-value">\${meeting.notes}</div>
                                    </div>
                                </div>
                                \` : ''}
                            </div>
                            \` : ''}
                        </div>
                        
                        <div class="footer">
                            تاريخ الطباعة: \${new Date().toISOString().split('T')[0].replace(/-/g, '/')}
                        </div>
                    </div>
                </body>
                </html>
            \`;
            
            // فتح نافذة طباعة جديدة
            const printWindow = window.open('', '_blank', 'width=900,height=700');
            if (!printWindow) {
                console.error('❌ تعذر فتح نافذة الطباعة');
                alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
                return;
            }

            console.log('✅ تم فتح نافذة الطباعة بنجاح');
            printWindow.document.write(reportHTML);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.focus();
                    printWindow.print();
                    console.log('✅ تم تشغيل طباعة تقرير تفاصيل الاجتماع');
                }, 500);
            };

            console.log('✅ تم إنشاء التقرير المحسن بنجاح');
        }

        // رسالة ترحيب
        console.log('🚀 تم تحميل صفحة اختبار طباعة تفاصيل الاجتماع');
        console.log('🎯 النظام يجلب البيانات الحقيقية من قاعدة البيانات');
    </script>
</body>
</html>
