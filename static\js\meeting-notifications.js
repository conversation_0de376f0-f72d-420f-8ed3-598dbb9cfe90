/**
 * نظام إشعارات الاجتماعات الموحد
 * القوات المسلحة الأردنية - مديرية الدائرة المالية
 */

class MeetingNotifications {
    constructor() {
        this.templates = {};
        this.loadTemplates();
    }

    // تحميل قوالب الإشعارات المختلفة
    loadTemplates() {
        this.templates = {
            // قالب إضافة اجتماع جديد
            new_meeting: {
                title: '🆕 تم إضافة اجتماع جديد',
                header: 'عطوفة المدير :-\nسيدي :\nتم إضافة اجتماع جديد لعطوفتكم و حسب التفصيلات التالية :',
                footer: '',
                color: '#28a745',
                icon: '🆕'
            },
            // قالب تأجيل اجتماع
            postponed_meeting: {
                title: '⏰ تم تأجيل اجتماع',
                header: 'عطوفة المدير :-\nسيدي :\nتم تأجيل الاجتماع التالي إلى موعد جديد حسب التفصيلات التالية :',
                footer: '\n⚠️ يرجى الانتباه للموعد الجديد',
                color: '#ffc107',
                icon: '⏰'
            },
            // قالب إلغاء اجتماع
            cancelled_meeting: {
                title: '❌ تم إلغاء اجتماع',
                header: 'عطوفة المدير :-\nسيدي :\nتم إلغاء الاجتماع التالي نهائياً :',
                footer: '\nتم إلغاء هذا الاجتماع نهائياً\nشكراً لتفهمكم',
                color: '#dc3545',
                icon: '❌'
            },
            // قالب تعديل اجتماع
            updated_meeting: {
                title: '✏️ تم تعديل اجتماع',
                header: 'عطوفة المدير :-\nسيدي :\nتم تعديل تفاصيل الاجتماع التالي :',
                footer: '\n📝 يرجى مراجعة التفاصيل المحدثة',
                color: '#17a2b8',
                icon: '✏️'
            }
        };
    }

    // إنشاء رسالة إشعار موحدة
    generateNotificationMessage(type, meetingData, attachments = []) {
        const template = this.templates[type];
        if (!template) {
            console.error('نوع الإشعار غير مدعوم:', type);
            return null;
        }

        // تنسيق التاريخ (اليوم ثم التاريخ)
        const meetingDate = new Date(meetingData.meeting_date);
        const dayName = meetingDate.toLocaleDateString('ar-SA', { weekday: 'long' });
        const formattedDate = `${dayName} ${meetingDate.getFullYear()}/${(meetingDate.getMonth() + 1).toString().padStart(2, '0')}/${meetingDate.getDate().toString().padStart(2, '0')}`;

        // تنسيق الوقت (مع صباحاً/مساءً)
        let formattedTime = 'غير محدد';
        if (meetingData.meeting_time) {
            const timeStr = meetingData.meeting_time;
            const timeParts = timeStr.split(':');
            if (timeParts.length >= 2) {
                const hour = parseInt(timeParts[0]);
                const minute = timeParts[1];
                const period = hour < 12 ? 'صباحاً' : 'مساءً';
                const displayHour = hour === 0 ? 12 : (hour > 12 ? hour - 12 : hour);
                formattedTime = `${displayHour}:${minute} ${period}`;
            }
        }

        // بناء تفاصيل الاجتماع بالترتيب المطلوب
        let meetingDetails = `🏢 جهة الدعوة: ${meetingData.inviting_party || 'مديرية الدائرة المالية'}\n`;
        meetingDetails += `📅 التاريخ: ${formattedDate}\n`;
        meetingDetails += `🕐 الوقت: ${formattedTime}\n`;
        meetingDetails += `📍 المكان: ${meetingData.location || 'غير محدد'}\n`;
        meetingDetails += `📋 الموضوع: ${meetingData.subject}\n`;
        meetingDetails += `👔 نوع اللباس: موسمي\n`;
        meetingDetails += `و حسب الملف المرفق`;

        // إضافة معلومات المرفقات
        let attachmentInfo = '';
        if (attachments && attachments.length > 0) {
            attachmentInfo = '\n\n📎 المرفقات:\n';
            attachments.forEach((attachment, index) => {
                attachmentInfo += `${index + 1}. ${attachment.filename}\n`;
            });
        }

        // بناء الرسالة الكاملة
        const fullMessage = `${template.header}\n\n${meetingDetails}${attachmentInfo}`;

        return {
            title: template.title,
            message: fullMessage,
            color: template.color,
            icon: template.icon,
            type: type
        };
    }

    // معاينة الإشعار في واجهة المستخدم
    previewNotification(type, meetingData, attachments = []) {
        const notification = this.generateNotificationMessage(type, meetingData, attachments);
        if (!notification) return null;

        // تنسيق الرسالة مع تكبير وتغميق النص المطلوب
        let formattedMessage = notification.message;

        // تكبير وتغميق "تم إضافة اجتماع جديد لعطوفتكم و حسب التفصيلات التالية"
        formattedMessage = formattedMessage.replace(
            /(تم إضافة اجتماع جديد لعطوفتكم و حسب التفصيلات التالية)/g,
            '<span style="font-size: 18px; font-weight: bold; color: #2c3e50;">$1</span>'
        );

        // تكبير وتغميق "تم تأجيل الاجتماع التالي إلى موعد جديد حسب التفصيلات التالية"
        formattedMessage = formattedMessage.replace(
            /(تم تأجيل الاجتماع التالي إلى موعد جديد حسب التفصيلات التالية)/g,
            '<span style="font-size: 18px; font-weight: bold; color: #2c3e50;">$1</span>'
        );

        // تكبير وتغميق "تم إلغاء الاجتماع التالي نهائياً"
        formattedMessage = formattedMessage.replace(
            /(تم إلغاء الاجتماع التالي نهائياً)/g,
            '<span style="font-size: 18px; font-weight: bold; color: #2c3e50;">$1</span>'
        );

        // تكبير وتغميق "تم تعديل تفاصيل الاجتماع التالي"
        formattedMessage = formattedMessage.replace(
            /(تم تعديل تفاصيل الاجتماع التالي)/g,
            '<span style="font-size: 18px; font-weight: bold; color: #2c3e50;">$1</span>'
        );

        return `
            <div style="text-align: center; font-weight: bold; font-size: 16px; margin-bottom: 15px; color: ${notification.color};">
                ${notification.icon} معاينة الإشعار
            </div>
            <div style="border: 2px solid ${notification.color}; padding: 15px; border-radius: 8px; background: white;">
                <div style="text-align: right; line-height: 1.6; white-space: pre-line;">
                    ${formattedMessage}
                </div>
            </div>
        `;
    }

    // إرسال إشعار واتساب
    async sendWhatsAppNotification(type, meetingData, phoneNumber, attachments = []) {
        try {
            const notification = this.generateNotificationMessage(type, meetingData, attachments);
            if (!notification) {
                throw new Error('فشل في إنشاء الإشعار');
            }

            const payload = {
                phone: phoneNumber,
                message: notification.message,
                type: type,
                meeting_id: meetingData.id,
                attachments: attachments
            };

            const response = await fetch('/api/notifications/send-whatsapp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('✅ تم إرسال الإشعار بنجاح');
                return { success: true, message: 'تم إرسال الإشعار بنجاح' };
            } else {
                console.error('❌ فشل في إرسال الإشعار:', result.error);
                return { success: false, error: result.error };
            }

        } catch (error) {
            console.error('❌ خطأ في إرسال الإشعار:', error);
            return { success: false, error: error.message };
        }
    }

    // إرسال إشعار تلقائي عند إضافة اجتماع
    async notifyNewMeeting(meetingData, attachments = []) {
        return await this.sendNotificationToAll('new_meeting', meetingData, attachments);
    }

    // إرسال إشعار تلقائي عند تأجيل اجتماع
    async notifyPostponedMeeting(meetingData, attachments = []) {
        return await this.sendNotificationToAll('postponed_meeting', meetingData, attachments);
    }

    // إرسال إشعار تلقائي عند إلغاء اجتماع
    async notifyCancelledMeeting(meetingData, attachments = []) {
        return await this.sendNotificationToAll('cancelled_meeting', meetingData, attachments);
    }

    // إرسال إشعار تلقائي عند تعديل اجتماع
    async notifyUpdatedMeeting(meetingData, attachments = []) {
        return await this.sendNotificationToAll('updated_meeting', meetingData, attachments);
    }

    // إرسال إشعار لجميع المستخدمين المفعلين
    async sendNotificationToAll(type, meetingData, attachments = []) {
        try {
            const response = await fetch('/api/notifications/send-to-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: type,
                    meeting_data: meetingData,
                    attachments: attachments
                })
            });

            const result = await response.json();
            return result;

        } catch (error) {
            console.error('❌ خطأ في إرسال الإشعارات:', error);
            return { success: false, error: error.message };
        }
    }
}

// إنشاء مثيل عام للاستخدام
window.MeetingNotifications = new MeetingNotifications();

// دوال مساعدة للاستخدام السريع
window.notifyNewMeeting = (meetingData, attachments) => {
    return window.MeetingNotifications.notifyNewMeeting(meetingData, attachments);
};

window.notifyPostponedMeeting = (meetingData, attachments) => {
    return window.MeetingNotifications.notifyPostponedMeeting(meetingData, attachments);
};

window.notifyCancelledMeeting = (meetingData, attachments) => {
    return window.MeetingNotifications.notifyCancelledMeeting(meetingData, attachments);
};

window.notifyUpdatedMeeting = (meetingData, attachments) => {
    return window.MeetingNotifications.notifyUpdatedMeeting(meetingData, attachments);
};

console.log('🔔 تم تحميل نظام إشعارات الاجتماعات الموحد');
