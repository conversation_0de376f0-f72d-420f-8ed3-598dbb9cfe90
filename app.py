#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام إدارة الاجتماعات - النسخة الكاملة مع الشاشة الخضراء
القوات المسلحة الأردنية - مديرية الدائرة المالية
"""

print("🚀 بدء تشغيل نظام إدارة الاجتماعات...")
print("=" * 60)

import os
import sys
from datetime import datetime, time, date, timedelta
import json

# التحقق من المكتبات وتثبيتها تلقائياً
required_packages = [
    'flask',
    'flask-sqlalchemy', 
    'flask-login',
    'werkzeug'
]

print("📦 التحقق من المكتبات المطلوبة...")
for package in required_packages:
    try:
        __import__(package.replace('-', '_'))
        print(f"✅ {package}")
    except ImportError:
        print(f"❌ {package} غير مثبت - جاري التثبيت...")
        os.system(f'pip install {package}')

try:
    from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, make_response, send_file
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    print("✅ تم استيراد جميع المكتبات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    sys.exit(1)

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'jaf-meetings-secret-key-2024-complete-rebuild'

# إضافة middleware لطباعة الطلبات
@app.before_request
def log_request():
    print(f"📥 طلب جديد: {request.method} {request.path}")

@app.after_request
def log_response(response):
    print(f"📤 رد: {response.status_code}")
    return response

# إعدادات الجلسة لتدوم لفترة أطول
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)  # الجلسة تدوم 24 ساعة
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SECURE'] = False  # False للتطوير المحلي
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# إعدادات لإجبار Flask على قراءة التحديثات
app.config['TEMPLATES_AUTO_RELOAD'] = True
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
app.jinja_env.auto_reload = True

# إضافة فلاتر مخصصة للتاريخ والوقت
@app.template_filter('arabic_date')
def arabic_date_filter(date_obj):
    """تنسيق التاريخ بالشكل المطلوب: 2025/7/25"""
    if date_obj:
        return f"{date_obj.year}/{date_obj.month}/{date_obj.day}"
    return ""

@app.template_filter('arabic_date_dmy')
def arabic_date_dmy_filter(date_obj):
    """تنسيق التاريخ بالشكل: يوم/شهر/سنة (25/7/2025)"""
    if date_obj:
        return f"{date_obj.day}/{date_obj.month}/{date_obj.year}"
    return ""

# إعداد قاعدة البيانات
basedir = os.path.abspath(os.path.dirname(__file__))
instance_dir = os.path.join(basedir, 'instance')
if not os.path.exists(instance_dir):
    os.makedirs(instance_dir)
    print("✅ تم إنشاء مجلد قاعدة البيانات")

db_path = os.path.join(instance_dir, 'jaf_meetings.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'
login_manager.login_message_category = 'info'

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100))
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Meeting(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    subject = db.Column(db.String(200), nullable=False)
    meeting_type = db.Column(db.String(100), nullable=False)

    @property
    def title(self):
        """إرجاع العنوان (نفس الموضوع)"""
        return self.subject
    meeting_date = db.Column(db.Date, nullable=False)
    meeting_time = db.Column(db.Time, nullable=False)
    location = db.Column(db.String(200), nullable=False)
    inviting_party = db.Column(db.String(200), nullable=False)
    arrival_time_before = db.Column(db.Integer, default=15)
    book_number = db.Column(db.String(100), nullable=False)
    book_date = db.Column(db.Date, nullable=False)
    dress_code = db.Column(db.String(100), nullable=False)
    notes = db.Column(db.Text)
    is_cancelled = db.Column(db.Boolean, default=False)
    is_postponed = db.Column(db.Boolean, default=False)
    status = db.Column(db.String(50), default='scheduled')  # scheduled, completed, cancelled, postponed
    creator_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    creator = db.relationship('User', backref=db.backref('meetings', lazy=True))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def update_status_from_flags(self):
        """تحديث الحالة بناءً على الحقول الموجودة والتاريخ والوقت"""
        from datetime import datetime, date, time
        now = datetime.now()
        today = now.date()
        current_time = now.time()

        # طباعة معلومات التشخيص
        print(f"🔍 فحص الاجتماع: {self.subject}")
        print(f"📅 تاريخ الاجتماع: {self.meeting_date} | اليوم: {today}")
        print(f"⏰ وقت الاجتماع: {self.meeting_time} | الوقت الحالي: {current_time}")
        print(f"🏷️ الحالة الحالية: {self.status}")

        if self.is_cancelled:
            self.status = 'cancelled'
            print(f"❌ الاجتماع ملغي")
        elif self.is_postponed:
            self.status = 'postponed'
            print(f"⏸️ الاجتماع مؤجل")
        elif self.meeting_date < today:
            # الاجتماع في تاريخ سابق - منتهي
            self.status = 'completed'
            print(f"📅 الاجتماع في تاريخ سابق - منتهي")
        elif self.meeting_date == today:
            # الاجتماع اليوم - فحص الوقت
            if self.meeting_time and self.meeting_time < current_time:
                self.status = 'completed'
                print(f"⏰ الاجتماع اليوم لكن الوقت مضى ({self.meeting_time} < {current_time}) - منتهي")
            else:
                self.status = 'scheduled'
                if not self.meeting_time:
                    print(f"⚠️ الاجتماع اليوم لكن لا يوجد وقت محدد - نشط")
                else:
                    print(f"⏰ الاجتماع اليوم ولم يحن وقته بعد ({self.meeting_time} >= {current_time}) - نشط")
        else:
            # الاجتماع في المستقبل - مجدول
            self.status = 'scheduled'
            print(f"🔮 الاجتماع في المستقبل - نشط")

        print(f"✅ الحالة النهائية: {self.status}")
        print("=" * 50)
        return self.status

    @staticmethod
    def update_all_meetings_status():
        """تحديث حالة جميع الاجتماعات تلقائياً"""
        try:
            meetings = Meeting.query.all()
            updated_count = 0

            for meeting in meetings:
                old_status = meeting.status
                new_status = meeting.update_status_from_flags()

                if old_status != new_status:
                    updated_count += 1
                    print(f"🔄 تحديث تلقائي: {meeting.subject} من {old_status} إلى {new_status}")

            if updated_count > 0:
                db.session.commit()
                print(f"✅ تم تحديث {updated_count} اجتماع تلقائياً")

            return updated_count
        except Exception as e:
            print(f"❌ خطأ في التحديث التلقائي: {str(e)}")
            db.session.rollback()
            return 0

class MeetingAttachment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    meeting_id = db.Column(db.Integer, db.ForeignKey('meeting.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(100))
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    meeting = db.relationship('Meeting', backref=db.backref('attachments', lazy=True, cascade='all, delete-orphan'))
    uploader = db.relationship('User', backref=db.backref('uploaded_files', lazy=True))

# وظيفة إرسال إشعارات WhatsApp
def send_whatsapp_notification(meeting, action_type):
    """
    إرسال إشعار WhatsApp للاجتماع
    action_type: 'new', 'updated', 'cancelled', 'postponed'
    """
    try:
        # هنا يمكن إضافة كود إرسال WhatsApp الفعلي
        # حالياً سنطبع الرسالة فقط

        messages = {
            'new': f"📅 اجتماع جديد: {meeting.subject}\n📍 المكان: {meeting.location}\n🕐 التاريخ: {meeting.meeting_date}\n⏰ الوقت: {meeting.meeting_time}",
            'updated': f"✏️ تم تحديث الاجتماع: {meeting.subject}\n📍 المكان: {meeting.location}\n🕐 التاريخ: {meeting.meeting_date}\n⏰ الوقت: {meeting.meeting_time}",
            'cancelled': f"❌ تم إلغاء الاجتماع: {meeting.subject}\n📍 المكان: {meeting.location}",
            'postponed': f"⏸️ تم تأجيل الاجتماع: {meeting.subject}\n📍 المكان: {meeting.location}\n🕐 التاريخ الجديد: {meeting.meeting_date}\n⏰ الوقت الجديد: {meeting.meeting_time}"
        }

        message = messages.get(action_type, f"📋 تحديث على الاجتماع: {meeting.subject}")
        print(f"📱 إشعار WhatsApp: {message}")

        return True
    except Exception as e:
        print(f"خطأ في إرسال إشعار WhatsApp: {str(e)}")
        return False

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# إضافة تسجيل للطلبات
@app.before_request
def log_request():
    print(f"📥 طلب جديد: {request.method} {request.path}")

# تجديد الجلسة تلقائياً
@app.before_request
def refresh_session():
    if current_user.is_authenticated:
        session.permanent = True



# فلاتر Jinja2 للتواريخ العربية
@app.template_filter('arabic_date')
def arabic_date_filter(date_obj):
    if not date_obj:
        return ''
    
    arabic_months = {
        1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
        5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
        9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
    }
    
    arabic_days = {
        0: 'الاثنين', 1: 'الثلاثاء', 2: 'الأربعاء', 3: 'الخميس',
        4: 'الجمعة', 5: 'السبت', 6: 'الأحد'
    }
    
    day_name = arabic_days.get(date_obj.weekday(), '')
    month_name = arabic_months.get(date_obj.month, '')
    
    return f"{day_name} {date_obj.day} {month_name} {date_obj.year}"

@app.template_filter('arabic_time')
def arabic_time_filter(time_obj):
    if not time_obj:
        return ''

    hour = time_obj.hour
    minute = time_obj.minute

    # تحويل الوقت من 24 ساعة إلى 12 ساعة
    if hour == 0:
        display_hour = 12
        period = "منتصف الليل"
    elif hour < 12:
        display_hour = hour if hour != 0 else 12
        period = "صباحاً"
    elif hour == 12:
        display_hour = 12
        period = "ظهراً"
    else:
        display_hour = hour - 12
        period = "مساءً"

    # التأكد من أن الساعة لا تكون 0
    if display_hour == 0:
        display_hour = 12

    minute_str = f"{minute:02d}"
    return f"{display_hour}:{minute_str} {period}"

@app.template_filter('military_time')
def military_time_filter(time_obj):
    """تحويل الوقت إلى النظام العسكري (24 ساعة) مثل 1300"""
    if not time_obj:
        return ''

    hour = time_obj.hour
    minute = time_obj.minute

    # تنسيق النظام العسكري: HHMM (بدون نقطتين)
    return f"{hour:02d}{minute:02d}"

@app.template_filter('twelve_hour_time')
def twelve_hour_time_filter(time_obj):
    """تحويل الوقت إلى نظام 12 ساعة مع صباحاً/مساءً"""
    if not time_obj:
        return ''

    hour = time_obj.hour
    minute = time_obj.minute

    # تحديد صباحاً أو مساءً
    if hour == 0:
        # منتصف الليل
        display_hour = 12
        period = 'صباحاً'
    elif hour < 12:
        # الصباح
        display_hour = hour
        period = 'صباحاً'
    elif hour == 12:
        # الظهر
        display_hour = 12
        period = 'مساءً'
    else:
        # المساء
        display_hour = hour - 12
        period = 'مساءً'

    # تنسيق الوقت: H:MM صباحاً/مساءً
    return f"{display_hour}:{minute:02d} {period}"

@app.template_filter('gregorian_date')
def gregorian_date_filter(date_obj):
    """تحويل التاريخ إلى التنسيق الميلادي العربي"""
    if not date_obj:
        return ''

    arabic_months = {
        1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
        5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
        9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
    }

    arabic_days = {
        0: 'الاثنين', 1: 'الثلاثاء', 2: 'الأربعاء', 3: 'الخميس',
        4: 'الجمعة', 5: 'السبت', 6: 'الأحد'
    }

    day_name = arabic_days.get(date_obj.weekday(), '')
    month_name = arabic_months.get(date_obj.month, '')

    return f"{day_name} {date_obj.day} {month_name} {date_obj.year} م"

def format_gregorian_date_now():
    """تنسيق التاريخ الحالي بالميلادي العربي"""
    now = datetime.now()
    arabic_months = {
        1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
        5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
        9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
    }

    month_name = arabic_months.get(now.month, '')
    return f"{now.day} {month_name} {now.year} م - {now.strftime('%H:%M:%S')}"

@app.template_filter('arabic_day_only')
def arabic_day_only_filter(date_obj):
    if not date_obj:
        return ''

    arabic_days = {
        0: 'الاثنين',
        1: 'الثلاثاء',
        2: 'الأربعاء',
        3: 'الخميس',
        4: 'الجمعة',
        5: 'السبت',
        6: 'الأحد'
    }

    return arabic_days.get(date_obj.weekday(), '')

@app.template_filter('convert_arabic_numbers')
def convert_arabic_numbers(text):
    if not text:
        return ''

    english_to_arabic = {
        '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
        '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
    }
    
    result = str(text)
    for eng, ar in english_to_arabic.items():
        result = result.replace(eng, ar)
    
    return result

# الصفحة الرئيسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('meetings_simple'))
    return redirect(url_for('login'))

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            # تسجيل الدخول مع جلسة دائمة تدوم 24 ساعة
            login_user(user, remember=True)
            session.permanent = True
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('meetings_simple'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# صفحة الاجتماعات البسيطة - الحل الجذري
@app.route('/meetings_simple')
@login_required
def meetings_simple():
    print("🔥 الحل الجذري - صفحة الاجتماعات البسيطة")

    # تحديث حالة الاجتماعات تلقائياً
    Meeting.update_all_meetings_status()

    try:
        # جلب جميع الاجتماعات بدون تعقيد
        meetings = Meeting.query.order_by(Meeting.meeting_date.asc(), Meeting.meeting_time.asc()).all()

        print(f"✅ تم جلب {len(meetings)} اجتماع بنجاح")

        # طباعة تفاصيل كل اجتماع
        for i, meeting in enumerate(meetings, 1):
            print(f"📋 اجتماع {i}:")
            print(f"   - المعرف: {meeting.id}")
            print(f"   - الموضوع: {meeting.subject}")
            print(f"   - التاريخ: {meeting.meeting_date}")
            print(f"   - الوقت: {meeting.meeting_time}")
            print(f"   - المكان: {meeting.location}")
            print(f"   - جهة الدعوة: {meeting.inviting_party}")
            print("   " + "="*50)

        # حساب الإحصائيات مع الاجتماعات المنتهية
        from datetime import datetime, date
        today = date.today()

        total_meetings = len(meetings)
        active_meetings = 0
        finished_meetings = 0

        # استخدام نفس منطق update_status_from_flags
        now = datetime.now()
        current_time = now.time()

        for m in meetings:
            if m.is_cancelled or m.is_postponed:
                # المؤجلة والملغية لا تحسب في النشطة أو المنتهية
                continue

            try:
                # تحويل التاريخ للمقارنة
                if isinstance(m.meeting_date, str):
                    meeting_date = datetime.strptime(m.meeting_date, '%Y-%m-%d').date()
                else:
                    meeting_date = m.meeting_date

                # منطق مطابق لـ update_status_from_flags
                if meeting_date < today:
                    # الاجتماع في تاريخ سابق - منتهي
                    finished_meetings += 1
                elif meeting_date == today and m.meeting_time and m.meeting_time < current_time:
                    # الاجتماع اليوم لكن الوقت مضى - منتهي
                    finished_meetings += 1
                else:
                    # الاجتماع في المستقبل أو لم يحن وقته بعد - نشط
                    active_meetings += 1
            except:
                active_meetings += 1  # في حالة خطأ، اعتبره نشط

        postponed_meetings = len([m for m in meetings if m.is_postponed])
        cancelled_meetings = len([m for m in meetings if m.is_cancelled])

        print(f"📊 الإحصائيات النهائية:")
        print(f"   - إجمالي: {total_meetings}")
        print(f"   - نشط: {active_meetings}")
        print(f"   - مؤجل: {postponed_meetings}")
        print(f"   - ملغي: {cancelled_meetings}")
        print(f"   - منتهي: {finished_meetings}")

        print(f"🎯 البيانات التي سيتم إرسالها إلى القالب البسيط:")
        print(f"   - finished_meetings = {finished_meetings} (نوع: {type(finished_meetings)})")

        print("🏷️ إضافة معلومات display_status لكل اجتماع...")
        # إضافة معلومات الحالة لكل اجتماع
        for meeting in meetings:
            if meeting.is_cancelled:
                meeting.display_status = 'cancelled'
            elif meeting.is_postponed:
                meeting.display_status = 'postponed'
            elif meeting.status == 'completed':
                meeting.display_status = 'finished'
            elif meeting.status == 'scheduled':
                meeting.display_status = 'active'
            else:
                meeting.display_status = 'unknown'

            print(f"🏷️ {meeting.subject}: status={meeting.status}, display_status={meeting.display_status}")

        return render_template('meetings_simple.html',
                             meetings=meetings,
                             total_meetings=total_meetings,
                             active_meetings=active_meetings,
                             postponed_meetings=postponed_meetings,
                             cancelled_meetings=cancelled_meetings,
                             finished_meetings=finished_meetings)

    except Exception as e:
        print(f"❌ خطأ في الحل الجذري: {str(e)}")
        import traceback
        traceback.print_exc()
        return render_template('meetings_simple.html',
                             meetings=[],
                             total_meetings=0,
                             active_meetings=0,
                             postponed_meetings=0,
                             cancelled_meetings=0,
                             finished_meetings=0)


# صفحة جميع الاجتماعات - النسخة البسيطة
@app.route('/all_meetings_simple')
@login_required
def all_meetings_simple():
    print("🚀 تم الوصول إلى صفحة كل الاجتماعات - النسخة البسيطة")

    # تحديث حالة الاجتماعات تلقائياً
    Meeting.update_all_meetings_status()

    try:
        # التحقق من إجمالي الاجتماعات في قاعدة البيانات
        total_in_db = Meeting.query.count()
        print(f"📊 إجمالي الاجتماعات في قاعدة البيانات: {total_in_db}")

        # ترتيب الاجتماعات حسب التاريخ والوقت (الأقرب أولاً)
        meetings = Meeting.query.order_by(
            Meeting.meeting_date.asc(),
            Meeting.meeting_time.asc()
        ).all()

        print(f"🔍 تم العثور على {len(meetings)} اجتماع في الصفحة البسيطة")

        # حساب الإحصائيات مع مراعاة التاريخ
        from datetime import datetime, date
        today = date.today()

        total_meetings = len(meetings)
        active_meetings = 0
        finished_meetings = 0

        for m in meetings:
            if not m.is_cancelled and not m.is_postponed:
                try:
                    # تحويل التاريخ للمقارنة
                    if isinstance(m.meeting_date, str):
                        meeting_date = datetime.strptime(m.meeting_date, '%Y-%m-%d').date()
                    else:
                        meeting_date = m.meeting_date

                    if meeting_date >= today:
                        active_meetings += 1
                    else:
                        finished_meetings += 1
                except:
                    active_meetings += 1  # في حالة خطأ، اعتبره نشط

        postponed_meetings = len([m for m in meetings if m.is_postponed])
        cancelled_meetings = len([m for m in meetings if m.is_cancelled])

        print(f"📊 الإحصائيات: إجمالي={total_meetings}, نشط={active_meetings}, مؤجل={postponed_meetings}, ملغي={cancelled_meetings}")

        # إضافة معلومات الحالة لكل اجتماع
        for meeting in meetings:
            if meeting.is_cancelled:
                meeting.display_status = 'cancelled'
            elif meeting.is_postponed:
                meeting.display_status = 'postponed'
            elif meeting.status == 'completed':
                meeting.display_status = 'finished'
            elif meeting.status == 'scheduled':
                meeting.display_status = 'active'
            else:
                meeting.display_status = 'unknown'

            print(f"🏷️ {meeting.subject}: status={meeting.status}, display_status={meeting.display_status}")

        return render_template('all_meetings_simple.html',
                             meetings=meetings,
                             total_meetings=total_meetings,
                             active_meetings=active_meetings,
                             postponed_meetings=postponed_meetings,
                             cancelled_meetings=cancelled_meetings,
                             finished_meetings=finished_meetings,
                             today=today)
    except Exception as e:
        print(f"❌ خطأ في تحميل الاجتماعات: {str(e)}")
        flash(f'خطأ في تحميل الاجتماعات: {str(e)}', 'error')
        return render_template('all_meetings_simple.html', meetings=[], total_meetings=0, active_meetings=0, postponed_meetings=0, cancelled_meetings=0)

# صفحة جميع الاجتماعات
@app.route('/all_meetings')
@login_required
def all_meetings():
    print("🚀 تم الوصول إلى صفحة كل الاجتماعات")

    # تحديث حالة الاجتماعات تلقائياً
    Meeting.update_all_meetings_status()

    try:
        # التحقق من إجمالي الاجتماعات في قاعدة البيانات
        total_in_db = Meeting.query.count()
        print(f"📊 إجمالي الاجتماعات في قاعدة البيانات: {total_in_db}")

        # ترتيب الاجتماعات حسب التاريخ والوقت (الأقرب أولاً)
        meetings = Meeting.query.order_by(
            Meeting.meeting_date.asc(),
            Meeting.meeting_time.asc()
        ).all()

        print(f"🔍 تم العثور على {len(meetings)} اجتماع في صفحة كل الاجتماعات")

        # طباعة تفاصيل كل اجتماع
        print("📋 تفاصيل جميع الاجتماعات:")
        for i, meeting in enumerate(meetings):
            print(f"  {i+1}. ID: {meeting.id} | الموضوع: {meeting.subject}")
            print(f"     📅 التاريخ: {meeting.meeting_date} | 🕐 الوقت: {meeting.meeting_time}")
            print(f"     📍 المكان: {meeting.location}")
            print(f"     👤 المنشئ: {meeting.creator_id}")
            print("     " + "="*50)
        print("📋 ترتيب الاجتماعات (الأقرب أولاً):")
        for i, meeting in enumerate(meetings):
            print(f"  {i+1}. {meeting.subject}")
            print(f"     📅 التاريخ: {meeting.meeting_date}")
            print(f"     🕐 الوقت: {meeting.meeting_time}")
            print(f"     📍 المكان: {meeting.location}")
            print("     " + "="*30)
        
        # إحصائيات
        from datetime import datetime, date
        today = date.today()

        total_meetings = len(meetings)
        active_meetings = 0
        finished_meetings = 0

        for m in meetings:
            if not m.is_cancelled and not m.is_postponed:
                try:
                    # تحويل التاريخ للمقارنة
                    if isinstance(m.meeting_date, str):
                        meeting_date = datetime.strptime(m.meeting_date, '%Y-%m-%d').date()
                    else:
                        meeting_date = m.meeting_date

                    if meeting_date >= today:
                        active_meetings += 1
                    else:
                        finished_meetings += 1
                except:
                    active_meetings += 1  # في حالة خطأ، اعتبره نشط

        postponed_meetings = len([m for m in meetings if m.is_postponed])
        cancelled_meetings = len([m for m in meetings if m.is_cancelled])

        stats = {
            'total': total_meetings,
            'active': active_meetings,
            'postponed': postponed_meetings,
            'cancelled': cancelled_meetings,
            'finished': finished_meetings
        }
        
        print(f"🎯 البيانات التي سيتم إرسالها إلى template:")
        print(f"   - meetings: {len(meetings)} اجتماع")
        print(f"   - total_meetings: {total_meetings}")
        print(f"   - active_meetings: {active_meetings}")
        print(f"   - postponed_meetings: {postponed_meetings}")
        print(f"   - cancelled_meetings: {cancelled_meetings}")
        print(f"   - finished_meetings: {finished_meetings}")

        return render_template('all_meetings.html',
                             meetings=meetings,
                             stats=stats,
                             total_meetings=total_meetings,
                             active_meetings=active_meetings,
                             postponed_meetings=postponed_meetings,
                             cancelled_meetings=cancelled_meetings,
                             finished_meetings=finished_meetings)
    except Exception as e:
        flash(f'خطأ في تحميل الاجتماعات: {str(e)}', 'error')
        return render_template('all_meetings.html', meetings=[], stats={'total': 0, 'active': 0, 'postponed': 0, 'cancelled': 0, 'finished': 0})

# صفحة اختبار قاعدة البيانات

# صفحة تفاصيل الاجتماع مع المرفقات
@app.route('/meeting/<int:meeting_id>')
def meeting_details(meeting_id):
    try:
        print(f"🔍 محاولة الوصول لتفاصيل الاجتماع رقم: {meeting_id}")
        print(f"👤 المستخدم الحالي: {current_user.is_authenticated if current_user else 'غير مسجل'}")
        meeting = Meeting.query.get_or_404(meeting_id)
        print(f"✅ تم العثور على الاجتماع: {meeting.subject}")
        attachments = MeetingAttachment.query.filter_by(meeting_id=meeting_id).all()
        print(f"📎 عدد المرفقات: {len(attachments)}")

        return render_template('meeting_details.html',
                             meeting=meeting,
                             attachments=attachments)
    except Exception as e:
        print(f"❌ خطأ في تحميل تفاصيل الاجتماع: {str(e)}")
        flash(f'خطأ في تحميل تفاصيل الاجتماع: {str(e)}', 'error')
        return redirect(url_for('all_meetings'))

# صفحة المرفقات منفصلة
@app.route('/meeting/<int:meeting_id>/attachments')
def meeting_attachments(meeting_id):
    try:
        print(f"🔍 محاولة الوصول لمرفقات الاجتماع رقم: {meeting_id}")
        meeting = Meeting.query.get_or_404(meeting_id)
        print(f"✅ تم العثور على الاجتماع: {meeting.subject}")
        attachments = MeetingAttachment.query.filter_by(meeting_id=meeting_id).all()
        print(f"📎 عدد المرفقات: {len(attachments)}")

        return render_template('meeting_attachments.html',
                             meeting=meeting,
                             attachments=attachments)
    except Exception as e:
        print(f"❌ خطأ في تحميل مرفقات الاجتماع: {str(e)}")
        flash(f'خطأ في تحميل مرفقات الاجتماع: {str(e)}', 'error')
        return redirect(url_for('all_meetings'))

# إضافة اجتماع جديد
@app.route('/add_meeting', methods=['GET', 'POST'])
@login_required
def add_meeting():
    print("🚀 بدء دالة add_meeting...")

    # قراءة جهات الدعوة من الملف أولاً
    inviting_parties = []
    try:
        with open('ABCD.txt', 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if line and not line.startswith('#'):  # تجاهل الأسطر الفارغة والتعليقات
                    # إزالة الرقم من بداية السطر إذا وجد
                    if '\t' in line:
                        parts = line.split('\t', 1)
                        if len(parts) > 1:
                            inviting_parties.append(parts[1].strip())
                    else:
                        inviting_parties.append(line)

        print(f"📋 تم تحميل {len(inviting_parties)} جهة دعوة من الملف")
        print(f"🔍 أول 3 جهات: {inviting_parties[:3]}")

    except FileNotFoundError:
        print("⚠️ لم يتم العثور على ملف ABCD.txt")
        inviting_parties = ["مديرية ديوان القيادة العامة", "إدارة الاختبار"]  # قيم افتراضية
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف جهات الدعوة: {str(e)}")
        inviting_parties = ["مديرية ديوان القيادة العامة", "إدارة الاختبار"]  # قيم افتراضية

    if request.method == 'POST':
        try:
            meeting = Meeting(
                subject=request.form['subject'],
                meeting_type=request.form['meeting_type'],
                meeting_date=datetime.strptime(request.form['meeting_date'], '%Y-%m-%d').date(),
                meeting_time=datetime.strptime(request.form['meeting_time'], '%H:%M').time(),
                location=request.form['location'],
                inviting_party=request.form['inviting_party'],
                arrival_time_before=int(request.form.get('arrival_time_before', 15)),
                book_number=request.form['book_number'],
                book_date=datetime.strptime(request.form['book_date'], '%Y-%m-%d').date(),
                dress_code=request.form['dress_code'],
                notes=request.form.get('notes', ''),
                creator_id=current_user.id
            )

            db.session.add(meeting)
            db.session.commit()

            print(f"✅ تم حفظ الاجتماع بنجاح!")
            print(f"   📝 ID: {meeting.id}")
            print(f"   📝 الموضوع: {meeting.subject}")
            print(f"   📅 التاريخ: {meeting.meeting_date}")
            print(f"   🕐 الوقت: {meeting.meeting_time}")
            print(f"   📍 المكان: {meeting.location}")

            # التحقق من العدد الإجمالي بعد الإضافة
            total_after = Meeting.query.count()
            print(f"📊 إجمالي الاجتماعات بعد الإضافة: {total_after}")

            # معالجة المرفقات
            if 'attachments' in request.files:
                files = request.files.getlist('attachments')
                for file in files:
                    if file and file.filename:
                        try:
                            # إنشاء مجلد المرفقات إذا لم يكن موجوداً
                            upload_folder = os.path.join('static', 'uploads', 'meetings')
                            os.makedirs(upload_folder, exist_ok=True)

                            # إنشاء اسم ملف فريد
                            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                            filename = f"{timestamp}_{file.filename}"
                            file_path = os.path.join(upload_folder, filename)

                            # حفظ الملف
                            file.save(file_path)

                            # إضافة المرفق لقاعدة البيانات
                            attachment = MeetingAttachment(
                                meeting_id=meeting.id,
                                filename=filename,
                                original_filename=file.filename,
                                file_path=file_path,
                                file_size=os.path.getsize(file_path),
                                file_type=file.content_type or 'application/octet-stream',
                                uploaded_by=current_user.id
                            )
                            db.session.add(attachment)
                        except Exception as e:
                            print(f"خطأ في رفع الملف {file.filename}: {str(e)}")

                db.session.commit()

            # إرسال إشعار WhatsApp إذا كان مفعلاً
            try:
                send_whatsapp_notification(meeting, 'new')
            except:
                pass  # تجاهل أخطاء الإشعارات

            # التحقق من نوع الطلب
            if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded' and 'XMLHttpRequest' not in str(request.headers):
                # طلب عادي من النموذج
                flash('تم إضافة الاجتماع بنجاح', 'success')
                return redirect(url_for('all_meetings'))
            else:
                # طلب AJAX
                return jsonify({
                    'success': True,
                    'message': 'تم إضافة الاجتماع بنجاح',
                    'meeting_id': meeting.id
                })

        except Exception as e:
            db.session.rollback()
            error_message = f'خطأ في إضافة الاجتماع: {str(e)}'

            # التحقق من نوع الطلب
            if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded' and 'XMLHttpRequest' not in str(request.headers):
                # طلب عادي من النموذج
                flash(error_message, 'error')
            else:
                # طلب AJAX
                return jsonify({
                    'success': False,
                    'message': error_message
                })

    return render_template('add_meeting.html', inviting_parties=inviting_parties)

# ===== دوال مساعدة =====

def check_meeting_conflict_for_update(meeting_id, meeting_date, meeting_time):
    """فحص التعارض في المواعيد عند التعديل"""
    try:
        # البحث عن اجتماعات أخرى في نفس التاريخ والوقت (باستثناء الاجتماع الحالي)
        conflicting_meetings = Meeting.query.filter(
            Meeting.id != meeting_id,
            Meeting.meeting_date == meeting_date,
            Meeting.meeting_time == meeting_time,
            Meeting.is_cancelled == False
        ).all()

        if conflicting_meetings:
            conflicts = []
            for meeting in conflicting_meetings:
                conflicts.append({
                    'id': meeting.id,
                    'subject': meeting.subject,
                    'location': meeting.location,
                    'inviting_party': meeting.inviting_party
                })

            return {
                'has_conflict': True,
                'conflicts': conflicts,
                'message': f'يوجد {len(conflicts)} اجتماع(ات) أخرى في نفس التوقيت'
            }

        return {'has_conflict': False}

    except Exception as e:
        print(f"❌ خطأ في فحص التعارض: {e}")
        return {'has_conflict': False}

# API لفحص التعارض قبل التعديل
@app.route('/api/check_conflict_edit', methods=['POST'])
@login_required
def check_conflict_edit():
    """فحص التعارض في المواعيد قبل التعديل"""
    try:
        data = request.get_json()
        meeting_id = data.get('meeting_id')
        meeting_date = datetime.strptime(data.get('meeting_date'), '%Y-%m-%d').date()
        meeting_time = datetime.strptime(data.get('meeting_time'), '%H:%M').time()

        print(f"🔍 فحص التعارض للاجتماع {meeting_id} في {meeting_date} الساعة {meeting_time}")

        # البحث عن اجتماعات متعارضة
        conflicting_meetings = Meeting.query.filter(
            Meeting.id != meeting_id,
            Meeting.meeting_date == meeting_date,
            Meeting.meeting_time == meeting_time,
            Meeting.is_cancelled == False
        ).all()

        if conflicting_meetings:
            conflicts = []
            for meeting in conflicting_meetings:
                conflicts.append({
                    'id': meeting.id,
                    'subject': meeting.subject,
                    'location': meeting.location,
                    'inviting_party': meeting.inviting_party,
                    'meeting_type': meeting.meeting_type
                })

            print(f"⚠️ تم العثور على {len(conflicts)} اجتماع(ات) متعارضة")

            return jsonify({
                'success': True,
                'has_conflict': True,
                'conflicts': conflicts,
                'message': f'يوجد {len(conflicts)} اجتماع(ات) أخرى في نفس التوقيت'
            })

        print("✅ لا يوجد تعارض في المواعيد")
        return jsonify({
            'success': True,
            'has_conflict': False,
            'message': 'لا يوجد تعارض في المواعيد'
        })

    except Exception as e:
        print(f"❌ خطأ في فحص التعارض: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في فحص التعارض: {str(e)}'
        })

# ===== API Routes للاجتماعات =====

# جلب بيانات اجتماع واحد
@app.route('/api/meeting/<int:meeting_id>')
@login_required
def get_meeting_api(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        return jsonify({
            'success': True,
            'meeting': {
                'id': meeting.id,
                'subject': meeting.subject,
                'meeting_type': meeting.meeting_type,
                'meeting_date': meeting.meeting_date.strftime('%Y-%m-%d'),
                'meeting_time': meeting.meeting_time.strftime('%H:%M'),
                'location': meeting.location,
                'inviting_party': meeting.inviting_party,
                'arrival_time_before': meeting.arrival_time_before,
                'book_number': meeting.book_number,
                'book_date': meeting.book_date.strftime('%Y-%m-%d'),
                'dress_code': meeting.dress_code,
                'notes': meeting.notes or '',
                'is_cancelled': meeting.is_cancelled,
                'is_postponed': meeting.is_postponed,
                'creator_name': meeting.creator.username if meeting.creator else 'غير معروف',
                'created_at': meeting.created_at.strftime('%Y-%m-%d %H:%M')
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب بيانات الاجتماع: {str(e)}'
        })

# تحديث بيانات الاجتماع
@app.route('/api/meeting/<int:meeting_id>', methods=['PUT'])
@login_required
def update_meeting_api(meeting_id):
    print(f"🔧 بدء تحديث الاجتماع {meeting_id}")

    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        print(f"✅ تم العثور على الاجتماع: {meeting.subject}")

        data = request.get_json()
        print(f"📋 البيانات المستلمة: {data}")

        # تحديث البيانات بطريقة مبسطة وآمنة
        if 'subject' in data and data['subject']:
            meeting.subject = data['subject']
            print(f"✅ تحديث الموضوع: {data['subject']}")

        if 'meeting_type' in data and data['meeting_type']:
            meeting.meeting_type = data['meeting_type']
            print(f"✅ تحديث نوع الاجتماع: {data['meeting_type']}")

        if 'meeting_date' in data and data['meeting_date']:
            meeting.meeting_date = datetime.strptime(data['meeting_date'], '%Y-%m-%d').date()
            print(f"✅ تحديث التاريخ: {data['meeting_date']}")

        if 'meeting_time' in data and data['meeting_time']:
            meeting.meeting_time = datetime.strptime(data['meeting_time'], '%H:%M').time()
            print(f"✅ تحديث الوقت: {data['meeting_time']}")

        if 'location' in data and data['location']:
            meeting.location = data['location']
            print(f"✅ تحديث المكان: {data['location']}")

        if 'inviting_party' in data and data['inviting_party']:
            meeting.inviting_party = data['inviting_party']
            print(f"✅ تحديث جهة الدعوة: {data['inviting_party']}")

        if 'arrival_time_before' in data and data['arrival_time_before']:
            meeting.arrival_time_before = int(data['arrival_time_before'])
            print(f"✅ تحديث وقت الحضور: {data['arrival_time_before']}")

        if 'book_number' in data and data['book_number']:
            meeting.book_number = int(data['book_number'])
            print(f"✅ تحديث رقم الكتاب: {data['book_number']}")

        if 'book_date' in data and data['book_date']:
            meeting.book_date = datetime.strptime(data['book_date'], '%Y-%m-%d').date()
            print(f"✅ تحديث تاريخ الكتاب: {data['book_date']}")

        if 'dress_code' in data:
            meeting.dress_code = data['dress_code']
            print(f"✅ تحديث نوع اللباس: {data['dress_code']}")

        if 'notes' in data:
            meeting.notes = data['notes']
            print(f"✅ تحديث الملاحظات: {data['notes']}")

        meeting.updated_at = datetime.utcnow()

        print("💾 حفظ التغييرات في قاعدة البيانات...")
        db.session.commit()
        print("✅ تم حفظ التغييرات بنجاح!")

        # فحص التعارض في المواعيد بعد التحديث
        conflict_check = check_meeting_conflict_for_update(meeting_id, meeting.meeting_date, meeting.meeting_time)

        return jsonify({
            'success': True,
            'message': 'تم تحديث الاجتماع بنجاح',
            'meeting_id': meeting_id,
            'conflict_warning': conflict_check
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في تحديث الاجتماع: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تحديث الاجتماع: {str(e)}'
        })

# حذف الاجتماع
@app.route('/api/meeting/<int:meeting_id>', methods=['DELETE'])
@login_required
def delete_meeting_api(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)

        # حذف المرفقات المرتبطة
        attachments = MeetingAttachment.query.filter_by(meeting_id=meeting_id).all()
        for attachment in attachments:
            if os.path.exists(attachment.file_path):
                os.remove(attachment.file_path)
            db.session.delete(attachment)

        # حذف الاجتماع
        db.session.delete(meeting)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف الاجتماع بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في حذف الاجتماع: {str(e)}'
        })

# إلغاء الاجتماع
@app.route('/api/meeting/<int:meeting_id>/cancel', methods=['POST'])
@login_required
def cancel_meeting_api(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        meeting.is_cancelled = True
        meeting.updated_at = datetime.utcnow()
        db.session.commit()

        # إرسال إشعار WhatsApp
        try:
            send_whatsapp_notification(meeting, 'cancelled')
        except:
            pass

        return jsonify({
            'success': True,
            'message': 'تم إلغاء الاجتماع بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في إلغاء الاجتماع: {str(e)}'
        })

# تأجيل الاجتماع
@app.route('/api/meeting/<int:meeting_id>/postpone', methods=['POST'])
@login_required
def postpone_meeting_api(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        data = request.get_json()

        postpone_type = data.get('postpone_type', 'date')  # افتراضي: تحديد تاريخ

        # إذا كان النوع هو تحديد تاريخ ووقت
        if postpone_type == 'date':
            # تحديث التاريخ والوقت الجديد
            if data.get('new_date'):
                meeting.meeting_date = datetime.strptime(data['new_date'], '%Y-%m-%d').date()
            if data.get('new_time'):
                meeting.meeting_time = datetime.strptime(data['new_time'], '%H:%M').time()

        # إذا كان النوع هو تأجيل إلى إشعار آخر
        elif postpone_type == 'later':
            # لا نحدث التاريخ والوقت، نتركهما كما هما
            # يمكن إضافة علامة خاصة للإشارة إلى أن التاريخ سيحدد لاحقاً
            pass

        meeting.is_postponed = True
        meeting.updated_at = datetime.utcnow()

        # إضافة سبب التأجيل للملاحظات
        postpone_reason = data.get('reason', '')
        postpone_note = ''

        if postpone_type == 'later':
            postpone_note = 'تم تأجيل الاجتماع إلى إشعار آخر يحدد لاحقاً'
        else:
            postpone_note = f'تم تأجيل الاجتماع إلى {data.get("new_date", "")} في {data.get("new_time", "")}'

        if postpone_reason:
            postpone_note += f' - السبب: {postpone_reason}'

        current_notes = meeting.notes or ''
        meeting.notes = f"{current_notes}\n\n{postpone_note}".strip()

        db.session.commit()

        # إرسال إشعار WhatsApp
        try:
            send_whatsapp_notification(meeting, 'postponed')
        except:
            pass

        return jsonify({
            'success': True,
            'message': 'تم تأجيل الاجتماع بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في تأجيل الاجتماع: {str(e)}'
        })

# إعادة تفعيل الاجتماع
@app.route('/api/meeting/<int:meeting_id>/reactivate', methods=['POST'])
@login_required
def reactivate_meeting_api(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        meeting.is_cancelled = False
        meeting.is_postponed = False
        meeting.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إعادة تفعيل الاجتماع بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في إعادة تفعيل الاجتماع: {str(e)}'
        })

# عرض المرفقات في صفحة مستقلة
@app.route('/view_attachment/<int:attachment_id>')
@login_required
def view_attachment(attachment_id):
    try:
        attachment = MeetingAttachment.query.get_or_404(attachment_id)

        # التحقق من وجود الملف
        if os.path.exists(attachment.file_path):
            # للصور وملفات PDF - عرض مباشر
            if attachment.file_type.startswith('image/') or attachment.file_type == 'application/pdf':
                return send_file(
                    attachment.file_path,
                    as_attachment=False,  # عرض في المتصفح وليس تحميل
                    download_name=attachment.original_filename
                )
            else:
                # للملفات الأخرى - إرجاع صفحة معلومات
                return render_template('view_attachment.html', attachment=attachment)
        else:
            flash('الملف غير موجود', 'error')
            return redirect(url_for('all_meetings'))

    except Exception as e:
        flash(f'خطأ في عرض الملف: {str(e)}', 'error')
        return redirect(url_for('all_meetings'))

# تحميل المرفقات
@app.route('/download_attachment/<int:attachment_id>')
@login_required
def download_attachment(attachment_id):
    try:
        attachment = MeetingAttachment.query.get_or_404(attachment_id)

        # التحقق من وجود الملف
        if os.path.exists(attachment.file_path):
            return send_file(
                attachment.file_path,
                as_attachment=True,
                download_name=attachment.original_filename
            )
        else:
            flash('الملف غير موجود', 'error')
            return redirect(url_for('all_meetings'))

    except Exception as e:
        flash(f'خطأ في تحميل الملف: {str(e)}', 'error')
        return redirect(url_for('all_meetings'))

# حذف المرفق
@app.route('/delete_attachment/<int:attachment_id>')
@login_required
def delete_attachment(attachment_id):
    try:
        attachment = MeetingAttachment.query.get_or_404(attachment_id)
        meeting_id = attachment.meeting_id

        # حذف الملف من النظام
        if os.path.exists(attachment.file_path):
            os.remove(attachment.file_path)

        # حذف السجل من قاعدة البيانات
        db.session.delete(attachment)
        db.session.commit()

        flash('تم حذف المرفق بنجاح', 'success')
        return redirect(url_for('meeting_details', meeting_id=meeting_id))

    except Exception as e:
        flash(f'خطأ في حذف المرفق: {str(e)}', 'error')
        return redirect(url_for('all_meetings'))

# إضافة مرفقات جديدة لاجتماع موجود
@app.route('/api/meeting/<int:meeting_id>/add_attachments', methods=['POST'])
@login_required
def add_meeting_attachments(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)

        if 'attachments' not in request.files:
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار أي ملفات'
            })

        files = request.files.getlist('attachments')
        uploaded_count = 0

        for file in files:
            if file and file.filename:
                try:
                    # إنشاء مجلد المرفقات إذا لم يكن موجوداً
                    upload_folder = os.path.join('static', 'uploads', 'meetings')
                    os.makedirs(upload_folder, exist_ok=True)

                    # إنشاء اسم ملف فريد
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"{timestamp}_{file.filename}"
                    file_path = os.path.join(upload_folder, filename)

                    # حفظ الملف
                    file.save(file_path)

                    # إضافة المرفق لقاعدة البيانات
                    attachment = MeetingAttachment(
                        meeting_id=meeting.id,
                        filename=filename,
                        original_filename=file.filename,
                        file_path=file_path,
                        file_size=os.path.getsize(file_path),
                        file_type=file.content_type or 'application/octet-stream',
                        uploaded_by=current_user.id
                    )
                    db.session.add(attachment)
                    uploaded_count += 1

                except Exception as e:
                    print(f"خطأ في رفع الملف {file.filename}: {str(e)}")

        if uploaded_count > 0:
            db.session.commit()
            return jsonify({
                'success': True,
                'message': f'تم رفع {uploaded_count} ملف بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في رفع الملفات'
            })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في رفع المرفقات: {str(e)}'
        })

# تعديل الاجتماعات
@app.route('/edit_meetings')
@login_required
def edit_meetings():
    print("🚀 تم الوصول إلى صفحة تعديل الاجتماعات")

    # تحديث حالة الاجتماعات تلقائياً
    Meeting.update_all_meetings_status()

    # جلب جميع الاجتماعات أولاً
    all_meetings = Meeting.query.all()

    # فلترة الاجتماعات لإخفاء المنتهية فقط (إظهار النشطة والمؤجلة والملغية)
    from datetime import datetime
    today = datetime.now().date()

    # فلترة الاجتماعات: إظهار الاجتماعات التي لم تنته بعد أو المؤجلة أو الملغية
    active_meetings = [meeting for meeting in all_meetings
                      if meeting.meeting_date >= today or meeting.is_postponed or meeting.is_cancelled]

    # ترتيب الاجتماعات النشطة حسب التاريخ والوقت (الأقرب أولاً)
    meetings = sorted(active_meetings, key=lambda x: (x.meeting_date, x.meeting_time))

    print(f"📊 إجمالي الاجتماعات في قاعدة البيانات: {len(all_meetings)}")
    print(f"✅ الاجتماعات المعروضة (غير المنتهية): {len(meetings)}")
    print(f"🚫 الاجتماعات المخفية (المنتهية): {len(all_meetings) - len(meetings)}")

    print("🎯 الترتيب النهائي للاجتماعات النشطة (الأقرب أولاً):")

    # تحديث حالة الاجتماعات تلقائياً بناءً على التاريخ والحقول الموجودة
    print("🔄 تحديث حالة الاجتماعات تلقائياً...")
    updated_count = 0
    for meeting in meetings:
        old_status = getattr(meeting, 'status', None)
        new_status = meeting.update_status_from_flags()
        if old_status != new_status:
            updated_count += 1
            print(f"  ✅ تم تحديث {meeting.subject}: {old_status or 'غير محدد'} → {new_status}")

    if updated_count > 0:
        try:
            db.session.commit()
            print(f"💾 تم حفظ {updated_count} تحديث للحالات")
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في حفظ التحديثات: {str(e)}")
    else:
        print("✅ جميع الحالات صحيحة، لا حاجة للتحديث")

    for i, meeting in enumerate(meetings):
        days_diff = (meeting.meeting_date - today).days
        print(f"  #{i+1} - {meeting.subject}")
        print(f"       📅 {meeting.meeting_date} (بعد {days_diff} يوم)")
        print(f"       🕐 {meeting.meeting_time}")
        print("       " + "="*40)

    print("✅ هذا هو الترتيب الذي يجب أن يظهر في الواجهة!")

    print(f"🔍 تم العثور على {len(meetings)} اجتماع في صفحة تعديل الاجتماعات")
    print("📋 ترتيب الاجتماعات (الأقرب أولاً):")
    for i, meeting in enumerate(meetings):
        print(f"  {i+1}. {meeting.subject}")
        print(f"     📅 التاريخ: {meeting.meeting_date}")
        print(f"     🕐 الوقت: {meeting.meeting_time}")
        print(f"     📍 المكان: {meeting.location}")
        print("     " + "="*30)

    return render_template('edit_meetings.html', meetings=meetings)

# صفحة اختبار ترتيب تعديل الاجتماعات بدون login_required


# صفحة اختبار الطباعة الجديدة
@app.route('/print-test-new')
def print_test_new():
    return render_template('print-test-new.html')


# البحث
@app.route('/search')
@login_required
def search():
    return render_template('search_new.html')

# جلب جهات الدعوة المتاحة
@app.route('/api/inviting_parties', methods=['GET'])
@login_required
def get_inviting_parties():
    try:
        print("🔍 بدء جلب جهات الدعوة من ملف ABCD.txt...")

        # قراءة جهات الدعوة من الملف
        parties_list = []
        try:
            with open('ABCD.txt', 'r', encoding='utf-8') as file:
                for line in file:
                    line = line.strip()
                    if line and not line.startswith('#'):  # تجاهل الأسطر الفارغة والتعليقات
                        # إزالة الرقم من بداية السطر إذا وجد
                        if '\t' in line:
                            parts = line.split('\t', 1)
                            if len(parts) > 1:
                                parties_list.append(parts[1].strip())
                        else:
                            parties_list.append(line)

            print(f"📋 تم تحميل {len(parties_list)} جهة دعوة من الملف")

        except FileNotFoundError:
            print("⚠️ لم يتم العثور على ملف ABCD.txt، استخدام قاعدة البيانات...")
            # العودة لقاعدة البيانات كبديل
            inviting_parties = db.session.query(Meeting.inviting_party).distinct().all()
            for party in inviting_parties:
                if party[0] and party[0].strip():
                    parties_list.append(party[0].strip())

        # إزالة التكرارات وترتيب القائمة أبجدياً
        parties_list = list(set(parties_list))
        parties_list.sort()

        print(f"✅ تم تحضير {len(parties_list)} جهة دعوة نهائية")
        for i, party in enumerate(parties_list[:5]):  # طباعة أول 5 جهات للتحقق
            print(f"   {i+1}. {party}")

        return jsonify({
            'success': True,
            'inviting_parties': parties_list
        })

    except Exception as e:
        print(f"❌ خطأ في جلب جهات الدعوة: {str(e)}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'success': False,
            'message': f'خطأ في جلب جهات الدعوة: {str(e)}'
        }), 500



# API إحصائيات بسيط
@app.route('/api/stats')
def simple_stats():
    try:
        total = Meeting.query.count()
        return jsonify({
            'success': True,
            'stats': {
                'total_meetings': total,
                'upcoming_meetings': total // 4,
                'completed_meetings': total // 2,
                'cancelled_meetings': total // 8,
                'monthly_average': round(total / 12, 1),
                'most_active_month': 'يناير',
                'most_active_count': total // 6
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# API بسيط للإحصائيات (مضمون)
@app.route('/api/simple-stats')
def get_simple_stats():
    try:
        print("🔍 API البسيط: طلب الإحصائيات")

        # إجمالي الاجتماعات
        total_meetings = Meeting.query.count()

        # الاجتماعات المكتملة (تاريخها في الماضي وغير ملغاة)
        from datetime import datetime
        current_date = datetime.now().date()
        completed_meetings = Meeting.query.filter(
            Meeting.meeting_date < current_date,
            Meeting.is_cancelled == False
        ).count()

        # الاجتماعات القادمة (تاريخها في المستقبل وغير ملغاة)
        upcoming_meetings = Meeting.query.filter(
            Meeting.meeting_date >= current_date,
            Meeting.is_cancelled == False
        ).count()

        # الاجتماعات الملغاة
        cancelled_meetings = Meeting.query.filter(Meeting.is_cancelled == True).count()

        print(f"📊 الإحصائيات البسيطة: مجموع={total_meetings}, مكتملة={completed_meetings}, قادمة={upcoming_meetings}, ملغاة={cancelled_meetings}")

        return jsonify({
            'success': True,
            'total_meetings': total_meetings,
            'completed_meetings': completed_meetings,
            'upcoming_meetings': upcoming_meetings,
            'cancelled_meetings': cancelled_meetings
        })

    except Exception as e:
        print(f"❌ خطأ في الإحصائيات البسيطة: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API بسيط للبيانات الشهرية (مضمون)
@app.route('/api/simple-monthly/<int:year>')
def get_simple_monthly(year):
    try:
        print(f"🔍 API البسيط: طلب بيانات سنة {year}")

        from sqlalchemy import extract
        from datetime import datetime

        # أسماء الأشهر العربية
        arabic_months = {
            1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
            5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
            9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
        }

        months_data = []
        current_date = datetime.now().date()

        for month_num in range(1, 13):
            # جلب اجتماعات الشهر
            month_meetings = Meeting.query.filter(
                extract('year', Meeting.meeting_date) == year,
                extract('month', Meeting.meeting_date) == month_num
            ).all()

            completed = 0
            upcoming = 0
            cancelled = 0

            for meeting in month_meetings:
                if meeting.is_cancelled:
                    cancelled += 1
                elif meeting.meeting_date < current_date:
                    completed += 1
                else:
                    upcoming += 1

            total = completed + upcoming + cancelled

            # إضافة جميع الأشهر (حتى الفارغة)
            months_data.append({
                'month': arabic_months[month_num],
                'completed': completed,
                'upcoming': upcoming,
                'cancelled': cancelled,
                'total': total
            })

            if total > 0:
                print(f"📅 {arabic_months[month_num]} {year}: مكتملة={completed}, قادمة={upcoming}, ملغاة={cancelled}")

        print(f"📊 إجمالي بيانات سنة {year}: تم إرجاع 12 شهر")

        return jsonify({
            'success': True,
            'year': year,
            'months': months_data
        })

    except Exception as e:
        print(f"❌ خطأ في البيانات الشهرية البسيطة: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API لجلب أحدث اجتماع (لمعاينة الإشعارات)
@app.route('/api/meetings/latest')
def get_latest_meeting():
    try:
        print("🔍 API: طلب أحدث اجتماع")

        # جلب أحدث اجتماع
        latest_meeting = Meeting.query.order_by(Meeting.created_at.desc()).first()

        if latest_meeting:
            meeting_data = {
                'id': latest_meeting.id,
                'subject': latest_meeting.subject or 'اجتماع بدون عنوان',
                'meeting_date': latest_meeting.meeting_date.strftime('%Y-%m-%d'),
                'meeting_time': latest_meeting.meeting_time.strftime('%H:%M') if latest_meeting.meeting_time else '10:00',
                'location': latest_meeting.location or 'قاعة الاجتماعات الرئيسية',
                'inviting_party': latest_meeting.inviting_party or 'مديرية الدائرة المالية',
                'is_cancelled': latest_meeting.is_cancelled,
                'is_postponed': latest_meeting.is_postponed
            }

            print(f"📋 أحدث اجتماع: {latest_meeting.subject}")

            return jsonify({
                'success': True,
                'meeting': meeting_data
            })
        else:
            print("📋 لا توجد اجتماعات")
            return jsonify({
                'success': False,
                'message': 'لا توجد اجتماعات'
            })

    except Exception as e:
        print(f"❌ خطأ في جلب أحدث اجتماع: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API لجلب أحدث اجتماع مع المرفقات
@app.route('/api/meetings/latest-with-attachments')
def get_latest_meeting_with_attachments():
    try:
        print("🔍 API: طلب أحدث اجتماع مع المرفقات")

        # جلب أحدث اجتماع
        latest_meeting = Meeting.query.order_by(Meeting.created_at.desc()).first()

        if latest_meeting:
            meeting_data = {
                'id': latest_meeting.id,
                'subject': latest_meeting.subject or 'اجتماع بدون عنوان',
                'meeting_date': latest_meeting.meeting_date.strftime('%Y-%m-%d'),
                'meeting_time': latest_meeting.meeting_time.strftime('%H:%M') if latest_meeting.meeting_time else '10:00',
                'location': latest_meeting.location or 'قاعة الاجتماعات الرئيسية',
                'inviting_party': latest_meeting.inviting_party or 'مديرية الدائرة المالية',
                'is_cancelled': latest_meeting.is_cancelled,
                'is_postponed': latest_meeting.is_postponed
            }

            # جلب المرفقات
            attachments_data = []
            if hasattr(latest_meeting, 'attachments'):
                for attachment in latest_meeting.attachments:
                    attachments_data.append({
                        'id': attachment.id,
                        'filename': attachment.filename,
                        'file_path': attachment.file_path
                    })

            print(f"📋 أحدث اجتماع: {latest_meeting.subject} مع {len(attachments_data)} مرفق")

            return jsonify({
                'success': True,
                'meeting': meeting_data,
                'attachments': attachments_data
            })
        else:
            print("📋 لا توجد اجتماعات")
            return jsonify({
                'success': False,
                'message': 'لا توجد اجتماعات'
            })

    except Exception as e:
        print(f"❌ خطأ في جلب أحدث اجتماع مع المرفقات: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API لاختبار إرسال الإشعارات
@app.route('/api/notifications/test-send', methods=['POST'])
def test_notification_send():
    try:
        data = request.get_json()
        notification_type = data.get('type', 'new_meeting')

        print(f"🧪 اختبار إرسال إشعار من نوع: {notification_type}")

        # بيانات تجريبية للاختبار
        test_meeting = {
            'id': 999,
            'subject': 'اجتماع تجريبي للاختبار',
            'meeting_date': '2025-07-15',
            'meeting_time': '10:00',
            'location': 'قاعة الاختبار',
            'inviting_party': 'مديرية الدائرة المالية'
        }

        test_attachments = [
            {'filename': 'ملف تجريبي 1.pdf'},
            {'filename': 'ملف تجريبي 2.docx'}
        ]

        # هنا يمكن إضافة منطق الإرسال الفعلي
        # مؤقتاً سنرجع نجاح

        return jsonify({
            'success': True,
            'message': f'تم إرسال إشعار اختبار من نوع {notification_type} بنجاح',
            'type': notification_type,
            'test_data': {
                'meeting': test_meeting,
                'attachments': test_attachments
            }
        })

    except Exception as e:
        print(f"❌ خطأ في اختبار الإشعار: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API لإرسال إشعارات لجميع المستخدمين
@app.route('/api/notifications/send-to-all', methods=['POST'])
def send_notification_to_all():
    try:
        data = request.get_json()
        notification_type = data.get('type')
        meeting_data = data.get('meeting_data')
        attachments = data.get('attachments', [])

        print(f"📢 إرسال إشعار {notification_type} لجميع المستخدمين")

        # هنا يمكن إضافة منطق الإرسال الفعلي لجميع المستخدمين
        # مؤقتاً سنرجع نجاح

        return jsonify({
            'success': True,
            'message': f'تم إرسال إشعار {notification_type} لجميع المستخدمين',
            'sent_count': 1,  # عدد الإشعارات المرسلة
            'failed_count': 0  # عدد الإشعارات الفاشلة
        })

    except Exception as e:
        print(f"❌ خطأ في إرسال الإشعارات: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API لحفظ إعدادات الإشعارات
@app.route('/api/notifications/save-settings', methods=['POST'])
def save_notification_settings():
    try:
        print("💾 حفظ إعدادات الإشعارات")

        # هنا يمكن إضافة منطق حفظ الإعدادات في قاعدة البيانات
        # مؤقتاً سنرجع نجاح

        return jsonify({
            'success': True,
            'message': 'تم حفظ الإعدادات بنجاح'
        })

    except Exception as e:
        print(f"❌ خطأ في حفظ الإعدادات: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API لإضافة بيانات تجريبية للاختبار
@app.route('/api/add-sample-meeting', methods=['POST'])
def add_sample_meeting():
    try:
        print("📝 إضافة اجتماع تجريبي للاختبار")

        from datetime import datetime, timedelta

        # إنشاء اجتماع تجريبي
        sample_meeting = Meeting(
            subject='اجتماع مجلس الإدارة الشهري',
            meeting_date=datetime.now().date() + timedelta(days=7),
            meeting_time=datetime.strptime('10:00', '%H:%M').time(),
            location='قاعة الاجتماعات الرئيسية',
            inviting_party='مديرية الدائرة المالية',
            agenda='مناقشة التقارير الشهرية والخطط المستقبلية',
            is_cancelled=False,
            is_postponed=False,
            created_at=datetime.now()
        )

        db.session.add(sample_meeting)
        db.session.commit()

        print(f"✅ تم إضافة اجتماع تجريبي: {sample_meeting.subject}")

        return jsonify({
            'success': True,
            'message': 'تم إضافة اجتماع تجريبي بنجاح',
            'meeting': {
                'id': sample_meeting.id,
                'subject': sample_meeting.subject,
                'meeting_date': sample_meeting.meeting_date.strftime('%Y-%m-%d'),
                'meeting_time': sample_meeting.meeting_time.strftime('%H:%M'),
                'location': sample_meeting.location,
                'inviting_party': sample_meeting.inviting_party
            }
        })

    except Exception as e:
        print(f"❌ خطأ في إضافة الاجتماع التجريبي: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API بديل لإحصائيات التقارير (بدون login_required للاختبار)
@app.route('/api/stats')
def get_stats_simple():
    try:
        print("🔍 تم استدعاء API الإحصائيات البديل")

        # إحصائيات بسيطة
        total_meetings = Meeting.query.count()
        upcoming_meetings = Meeting.query.filter(
            Meeting.meeting_date >= datetime.now().date(),
            Meeting.is_cancelled == False,
            Meeting.is_postponed == False
        ).count()
        completed_meetings = Meeting.query.filter(
            Meeting.meeting_date < datetime.now().date(),
            Meeting.is_cancelled == False,
            Meeting.is_postponed == False
        ).count()
        cancelled_meetings = Meeting.query.filter_by(is_cancelled=True).count()

        # متوسط شهري بسيط
        first_meeting = Meeting.query.order_by(Meeting.meeting_date.asc()).first()
        if first_meeting:
            months_diff = (datetime.now().year - first_meeting.meeting_date.year) * 12 + \
                         (datetime.now().month - first_meeting.meeting_date.month) + 1
            monthly_average = round(total_meetings / max(months_diff, 1), 1)
        else:
            monthly_average = 0

        # أكثر الشهور نشاطاً - حساب حقيقي
        from sqlalchemy import func, extract
        all_month_stats = db.session.query(
            extract('month', Meeting.meeting_date).label('month'),
            func.count(Meeting.id).label('count')
        ).group_by(extract('month', Meeting.meeting_date)).order_by(func.count(Meeting.id).desc()).all()

        if all_month_stats and len(all_month_stats) > 0:
            max_count = all_month_stats[0].count
            months_with_max_count = [stat for stat in all_month_stats if stat.count == max_count]

            month_names = {
                1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
                5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
                9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
            }

            if len(months_with_max_count) > 1:
                most_active_month = 'متساوي'
                most_active_count = max_count
            else:
                most_active_stat = months_with_max_count[0]
                most_active_month = month_names.get(int(most_active_stat.month), 'غير محدد')
                most_active_count = most_active_stat.count
        else:
            most_active_month = 'لا يوجد'
            most_active_count = 0

        stats = {
            'total_meetings': total_meetings,
            'upcoming_meetings': upcoming_meetings,
            'completed_meetings': completed_meetings,
            'cancelled_meetings': cancelled_meetings,
            'monthly_average': monthly_average,
            'most_active_month': most_active_month,
            'most_active_count': most_active_count
        }

        print(f"📊 الإحصائيات: {stats}")

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        print(f"❌ خطأ في API الإحصائيات: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# البحث المتقدم
@app.route('/search_meetings', methods=['GET', 'POST'])
@login_required
def search_meetings():
    if request.method == 'POST':
        search_term = request.form.get('search_term', '').strip()
        search_type = request.form.get('search_type', 'all')

        query = Meeting.query

        if search_term:
            if search_type == 'subject':
                query = query.filter(Meeting.subject.contains(search_term))
            elif search_type == 'location':
                query = query.filter(Meeting.location.contains(search_term))
            elif search_type == 'inviting_party':
                query = query.filter(Meeting.inviting_party.contains(search_term))
            else:  # all
                query = query.filter(
                    db.or_(
                        Meeting.subject.contains(search_term),
                        Meeting.location.contains(search_term),
                        Meeting.inviting_party.contains(search_term),
                        Meeting.notes.contains(search_term)
                    )
                )

        meetings = query.order_by(Meeting.meeting_date.desc()).all()
        return render_template('search_new.html', meetings=meetings, search_term=search_term)

    return redirect(url_for('search'))

# التقارير
@app.route('/reports')
def reports():
    try:
        # تحديث حالة الاجتماعات تلقائياً قبل حساب الإحصائيات
        print("🔄 تحديث حالة الاجتماعات في التقارير...")
        updated_count = 0

        all_meetings = Meeting.query.all()
        for meeting in all_meetings:
            old_status = getattr(meeting, 'status', None)
            new_status = meeting.update_status_from_flags()
            if old_status != new_status:
                updated_count += 1
                print(f"  ✅ تقارير - تم تحديث {meeting.subject}: {old_status or 'غير محدد'} → {new_status}")

        if updated_count > 0:
            try:
                db.session.commit()
                print(f"💾 تقارير - تم حفظ {updated_count} تحديث للحالات")
            except Exception as e:
                db.session.rollback()
                print(f"❌ تقارير - خطأ في حفظ التحديثات: {str(e)}")

        # إحصائيات أساسية
        total_meetings = Meeting.query.count()

        # الاجتماعات القادمة (بناءً على الحالة والتاريخ)
        upcoming_meetings = Meeting.query.filter(
            Meeting.meeting_date >= datetime.now().date(),
            Meeting.status.in_(['scheduled', 'active'])
        ).count()

        # الاجتماعات المكتملة (بناءً على الحالة)
        completed_meetings = Meeting.query.filter(
            Meeting.status == 'completed'
        ).count()

        # الاجتماعات الملغية (بناءً على الحالة)
        cancelled_meetings = Meeting.query.filter(
            Meeting.status == 'cancelled'
        ).count()

        # الاجتماعات المؤجلة (بناءً على الحالة)
        postponed_meetings = Meeting.query.filter(
            Meeting.status == 'postponed'
        ).count()

        # متوسط شهري (إجمالي الاجتماعات / عدد الأشهر التي بها اجتماعات فعلية)
        # حساب الأشهر الفريدة بطريقة صحيحة
        from sqlalchemy import func, extract
        unique_months_query = db.session.query(
            extract('year', Meeting.meeting_date).label('year'),
            extract('month', Meeting.meeting_date).label('month')
        ).distinct().all()

        unique_months_count = len(unique_months_query)
        print(f"🔢 عدد الأشهر الفريدة: {unique_months_count}")
        print(f"📅 الأشهر الفريدة: {[(int(m.year), int(m.month)) for m in unique_months_query]}")

        if unique_months_count > 0:
            avg_per_month = round(total_meetings / unique_months_count, 1)
        else:
            avg_per_month = 0.0

        # أكثر الشهور نشاطاً - حساب حقيقي مع التحقق من التساوي
        print("🔍 البحث عن أكثر الشهور نشاطاً...")

        # جلب جميع الشهور مع عدد الاجتماعات
        all_month_stats = db.session.query(
            extract('month', Meeting.meeting_date).label('month'),
            func.count(Meeting.id).label('count')
        ).group_by(extract('month', Meeting.meeting_date)).order_by(func.count(Meeting.id).desc()).all()

        print(f"📊 إحصائيات جميع الشهور: {[(int(stat.month), stat.count) for stat in all_month_stats]}")

        if all_month_stats and len(all_month_stats) > 0:
            # التحقق من وجود شهور متساوية في النشاط
            max_count = all_month_stats[0].count
            months_with_max_count = [stat for stat in all_month_stats if stat.count == max_count]

            month_names = {
                1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
                5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
                9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
            }

            if len(months_with_max_count) > 1:
                # جميع الشهور متساوية
                most_active_month = 'متساوي'
                most_active_count = max_count
                print(f"🏆 جميع الشهور متساوية في النشاط: {len(months_with_max_count)} شهور بـ {max_count} اجتماع لكل شهر")
            else:
                # شهر واحد هو الأكثر نشاطاً
                most_active_stat = months_with_max_count[0]
                most_active_month = month_names.get(int(most_active_stat.month), 'غير محدد')
                most_active_count = most_active_stat.count
                print(f"🏆 أكثر الشهور نشاطاً: {most_active_month} (الشهر رقم {int(most_active_stat.month)}) - {most_active_count} اجتماع")
        else:
            # إذا لم توجد اجتماعات
            most_active_month = 'لا يوجد'
            most_active_count = 0
            print(f"📅 لا توجد اجتماعات")

        # تم حساب الاجتماعات المؤجلة أعلاه بناءً على الحالة الجديدة

        # طباعة البيانات المرسلة للـ template
        print(f"📤 البيانات المرسلة للـ template:")
        print(f"  - إجمالي الاجتماعات: {total_meetings}")
        print(f"  - الاجتماعات القادمة: {upcoming_meetings}")
        print(f"  - الاجتماعات المكتملة: {completed_meetings}")
        print(f"  - الاجتماعات الملغية: {cancelled_meetings}")
        print(f"  - الاجتماعات المؤجلة: {postponed_meetings}")
        print(f"  - المتوسط الشهري: {avg_per_month}")
        print(f"  - أكثر الشهور نشاطاً: {most_active_month} ({most_active_count})")
        print(f"  - عدد الأشهر الفريدة: {unique_months_count}")

        return render_template('reports.html',
                             total_meetings=total_meetings,
                             upcoming_meetings=upcoming_meetings,
                             completed_meetings=completed_meetings,
                             cancelled_meetings=cancelled_meetings,
                             postponed_meetings=postponed_meetings,
                             avg_per_month=avg_per_month,
                             most_active_month=most_active_month,
                             most_active_count=most_active_count)

    except Exception as e:
        print(f"خطأ في صفحة التقارير: {e}")
        return render_template('reports.html',
                             total_meetings=0,
                             upcoming_meetings=0,
                             completed_meetings=0,
                             cancelled_meetings=0,
                             avg_per_month=0,
                             most_active_month='يناير',
                             most_active_count=0)

# صفحة التقارير البسيطة (مضمونة)
@app.route('/simple-reports')
def simple_reports():
    return render_template('simple_reports.html')

# صفحة التقارير التي تعمل مضمون 100%
@app.route('/working-reports')
@app.route('/working-reports/<int:year>')
def working_reports(year=2024):
    try:
        print(f"🔍 تقارير مضمونة: طلب بيانات سنة {year}")

        from sqlalchemy import extract
        from datetime import datetime, date

        # جلب اجتماعات السنة المحددة
        meetings = Meeting.query.filter(
            extract('year', Meeting.meeting_date) == year
        ).order_by(Meeting.meeting_date).all()

        print(f"📅 تم العثور على {len(meetings)} اجتماع في سنة {year}")

        # حساب الإحصائيات
        total_meetings = len(meetings)
        completed_meetings = 0
        upcoming_meetings = 0
        cancelled_meetings = 0
        today = date.today()

        for meeting in meetings:
            if meeting.is_cancelled:
                cancelled_meetings += 1
            elif meeting.meeting_date < today:
                completed_meetings += 1
            else:
                upcoming_meetings += 1

        print(f"📊 الإحصائيات: مجموع={total_meetings}, مكتملة={completed_meetings}, قادمة={upcoming_meetings}, ملغاة={cancelled_meetings}")

        return render_template('working_reports.html',
                             meetings=meetings,
                             selected_year=year,
                             total_meetings=total_meetings,
                             completed_meetings=completed_meetings,
                             upcoming_meetings=upcoming_meetings,
                             cancelled_meetings=cancelled_meetings,
                             today=today)

    except Exception as e:
        print(f"❌ خطأ في التقارير المضمونة: {str(e)}")
        return render_template('working_reports.html',
                             meetings=[],
                             selected_year=year,
                             total_meetings=0,
                             completed_meetings=0,
                             upcoming_meetings=0,
                             cancelled_meetings=0,
                             today=date.today())

# API لتحديث إحصائيات التقارير
@app.route('/api/reports/stats')
def get_reports_stats():
    try:
        print("🔍 API: طلب الإحصائيات العامة")

        # إحصائيات أساسية
        total_meetings = Meeting.query.count()
        print(f"📊 إجمالي الاجتماعات: {total_meetings}")

        # الاجتماعات القادمة (لم تحدث بعد)
        upcoming_meetings = Meeting.query.filter(
            Meeting.meeting_date >= datetime.now().date(),
            Meeting.is_cancelled == False,
            Meeting.is_postponed == False
        ).count()

        # الاجتماعات المكتملة (حدثت بالفعل)
        completed_meetings = Meeting.query.filter(
            Meeting.meeting_date < datetime.now().date(),
            Meeting.is_cancelled == False,
            Meeting.is_postponed == False
        ).count()

        # الاجتماعات الملغية
        cancelled_meetings = Meeting.query.filter_by(is_cancelled=True).count()

        # متوسط شهري (إجمالي الاجتماعات / عدد الشهور)
        first_meeting = Meeting.query.order_by(Meeting.meeting_date.asc()).first()
        if first_meeting:
            months_diff = (datetime.now().year - first_meeting.meeting_date.year) * 12 + \
                         (datetime.now().month - first_meeting.meeting_date.month) + 1
            monthly_average = round(total_meetings / max(months_diff, 1), 1)
        else:
            monthly_average = 0

        # أكثر الشهور نشاطاً
        from sqlalchemy import func, extract
        month_stats = db.session.query(
            extract('month', Meeting.meeting_date).label('month'),
            func.count(Meeting.id).label('count')
        ).group_by(extract('month', Meeting.meeting_date)).order_by(func.count(Meeting.id).desc()).first()

        if month_stats:
            month_names = {
                1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
                5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
                9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
            }
            most_active_month = month_names.get(int(month_stats.month), 'كانون الثاني')
            most_active_count = month_stats.count
        else:
            most_active_month = 'يناير'
            most_active_count = 0

        stats = {
            'total_meetings': total_meetings,
            'upcoming_meetings': upcoming_meetings,
            'completed_meetings': completed_meetings,
            'cancelled_meetings': cancelled_meetings,
            'monthly_average': monthly_average,
            'most_active_month': most_active_month,
            'most_active_count': most_active_count
        }

        print(f"📈 الإحصائيات: مجموع={total_meetings}, مكتملة={completed_meetings}, قادمة={upcoming_meetings}, ملغاة={cancelled_meetings}")

        return jsonify({
            'success': True,
            'total_meetings': total_meetings,
            'completed_meetings': completed_meetings,
            'upcoming_meetings': upcoming_meetings,
            'cancelled_meetings': cancelled_meetings,
            'avg_per_month': monthly_average,
            'most_active_month': most_active_month
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ خطأ في جلب الإحصائيات: {e}")
        print(f"📋 تفاصيل الخطأ: {error_details}")
        return jsonify({
            'success': False,
            'error': f'خطأ في الخادم: {str(e)}'
        }), 500

# API لجلب البيانات الشهرية
@app.route('/api/reports/monthly-data/<int:year>')
def get_monthly_data(year):
    try:
        print(f"🔍 API: طلب بيانات سنة {year}")
        from sqlalchemy import func, extract

        # جلب اجتماعات السنة من قاعدة البيانات
        year_meetings = Meeting.query.filter(extract('year', Meeting.meeting_date) == year).all()
        print(f"📅 اجتماعات سنة {year}: {len(year_meetings)}")

        for meeting in year_meetings:
            print(f"  - {meeting.title} ({meeting.meeting_date}) - ملغي: {meeting.is_cancelled}")

        # أسماء الأشهر العربية
        arabic_months = {
            1: 'كانون الثاني',
            2: 'شباط',
            3: 'آذار',
            4: 'نيسان',
            5: 'أيار',
            6: 'حزيران',
            7: 'تموز',
            8: 'آب',
            9: 'أيلول',
            10: 'تشرين الأول',
            11: 'تشرين الثاني',
            12: 'كانون الأول'
        }

        monthly_stats = []
        current_date = datetime.now()

        for month_num in range(1, 13):
            # جلب اجتماعات الشهر باستخدام SQLAlchemy
            month_meetings = Meeting.query.filter(
                extract('year', Meeting.meeting_date) == year,
                extract('month', Meeting.meeting_date) == month_num
            ).all()

            completed = 0
            upcoming = 0
            cancelled = 0

            for meeting in month_meetings:
                if meeting.is_cancelled:
                    cancelled += 1
                elif meeting.meeting_date < current_date.date():
                    # اجتماع مكتمل (تاريخه في الماضي وغير ملغي)
                    completed += 1
                else:
                    # اجتماع قادم (تاريخه في المستقبل وغير ملغي)
                    upcoming += 1

            # تحديد ما إذا كان الشهر قد انتهى
            month_ended = (year < current_date.year) or (year == current_date.year and month_num <= current_date.month)

            monthly_stats.append({
                'month': arabic_months[month_num],
                'month_number': month_num,
                'completed': completed,
                'upcoming': upcoming,
                'cancelled': cancelled,
                'total': completed + upcoming + cancelled,
                'ended': month_ended
            })

            # طباعة تفاصيل الشهر إذا كان يحتوي على بيانات
            if completed + upcoming + cancelled > 0:
                print(f"📅 {arabic_months[month_num]} {year}: مكتملة={completed}, قادمة={upcoming}, ملغاة={cancelled}")

        # حساب الإجماليات للسنة المحددة
        year_completed = sum(month['completed'] for month in monthly_stats)
        year_upcoming = sum(month['upcoming'] for month in monthly_stats)
        year_cancelled = sum(month['cancelled'] for month in monthly_stats)
        year_total = year_completed + year_upcoming + year_cancelled

        print(f"📊 إحصائيات سنة {year}: مكتملة={year_completed}, قادمة={year_upcoming}, ملغاة={year_cancelled}, المجموع={year_total}")

        return jsonify({
            'success': True,
            'year': year,
            'months': monthly_stats,
            'year_totals': {
                'completed': year_completed,
                'upcoming': year_upcoming,
                'cancelled': year_cancelled,
                'total': year_total
            }
        })

    except Exception as e:
        print(f"❌ خطأ في جلب البيانات الشهرية: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في الخادم: {str(e)}'
        }), 500

# API لجلب إحصائيات جهات الدعوة
@app.route('/api/reports/inviting-parties')
@login_required
def get_inviting_parties_stats():
    try:
        # جلب إحصائيات جهات الدعوة
        from sqlalchemy import func

        inviting_parties_stats = db.session.query(
            Meeting.inviting_party,
            func.count(Meeting.id).label('count')
        ).group_by(Meeting.inviting_party).order_by(func.count(Meeting.id).desc()).all()

        # تحويل النتائج إلى قائمة
        parties_data = []
        total_meetings = Meeting.query.count()

        for party, count in inviting_parties_stats:
            percentage = round((count / total_meetings) * 100) if total_meetings > 0 else 0
            parties_data.append({
                'name': party,
                'count': count,
                'percentage': percentage
            })

        return jsonify({
            'success': True,
            'data': parties_data,
            'total': total_meetings
        })

    except Exception as e:
        print(f"❌ خطأ في جلب إحصائيات جهات الدعوة: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# الإعدادات
@app.route('/settings')
@login_required
def settings():
    return render_template('settings.html')

# الملف الشخصي
@app.route('/profile')
@login_required
def profile():
    return render_template('profile.html', user=current_user)

# إعدادات الإشعارات
@app.route('/notifications_settings')
@login_required
def notifications_settings():
    return render_template('notifications_settings.html')

# ===== ROUTES التصدير والتقارير =====

# تصدير CSV
@app.route('/export_csv')
@login_required
def export_csv():
    try:
        meetings = Meeting.query.order_by(Meeting.meeting_date.asc()).all()

        import io
        output = io.StringIO()
        output.write('\ufeff')  # BOM للدعم العربي

        # العناوين
        output.write('الرقم,نوع الفعالية,التاريخ,الوقت,المكان,الموضوع,جهة الدعوة,رقم الكتاب,اللباس,الحالة\n')

        # البيانات
        for i, meeting in enumerate(meetings, 1):
            status = "ملغي" if meeting.is_cancelled else "مؤجل" if meeting.is_postponed else "نشط"

            row = [
                str(i),
                meeting.meeting_type or 'غير محدد',
                meeting.meeting_date.strftime('%Y/%m/%d') if meeting.meeting_date else '',
                meeting.meeting_time.strftime('%H:%M') if meeting.meeting_time else '',
                meeting.location or 'غير محدد',
                meeting.subject or 'بدون موضوع',
                meeting.inviting_party or 'غير محدد',
                meeting.book_number or 'غير محدد',
                meeting.dress_code or 'غير محدد',
                status
            ]

            output.write(','.join(f'"{field}"' for field in row) + '\n')

        response = make_response(output.getvalue())
        response.headers["Content-Disposition"] = f"attachment; filename=تقرير_الاجتماعات_{datetime.now().strftime('%Y-%m-%d_%H-%M')}.csv"
        response.headers["Content-Type"] = "text/csv; charset=utf-8"

        return response

    except Exception as e:
        flash(f'خطأ في تصدير CSV: {str(e)}', 'error')
        return redirect(url_for('reports'))

# تصدير PDF
@app.route('/export_pdf')
@login_required
def export_pdf():
    try:
        meetings = Meeting.query.order_by(Meeting.meeting_date.asc()).all()

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير الاجتماعات الشامل والمفصل</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    margin: 15px;
                    line-height: 1.4;
                    font-size: 11px;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #1B4332;
                    padding-bottom: 15px;
                }}
                .logo {{
                    color: #1B4332;
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 8px;
                }}
                .title {{
                    color: #1B4332;
                    font-size: 16px;
                    margin-bottom: 8px;
                }}
                .info {{
                    color: #666;
                    font-size: 12px;
                }}
                .meeting-card {{
                    border: 1px solid #ddd;
                    margin-bottom: 15px;
                    border-radius: 8px;
                    overflow: hidden;
                    page-break-inside: avoid;
                }}
                .meeting-header {{
                    background: linear-gradient(135deg, #1B4332, #2D5A3D);
                    color: white;
                    padding: 10px 15px;
                    font-weight: bold;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}
                .meeting-body {{
                    padding: 12px 15px;
                    background: #f9f9f9;
                }}
                .detail-grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 10px;
                    margin-bottom: 10px;
                }}
                .detail-item {{
                    background: white;
                    padding: 8px;
                    border-radius: 4px;
                    border-right: 3px solid #1B4332;
                }}
                .detail-label {{
                    font-weight: bold;
                    color: #1B4332;
                    font-size: 10px;
                    margin-bottom: 3px;
                }}
                .detail-value {{
                    color: #333;
                    font-size: 11px;
                }}
                .status-badge {{
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 10px;
                    font-weight: bold;
                }}
                .status-active {{ background: #d4edda; color: #155724; }}
                .status-cancelled {{ background: #f8d7da; color: #721c24; }}
                .status-postponed {{ background: #fff3cd; color: #856404; }}
                .notes-section {{
                    background: white;
                    padding: 8px;
                    border-radius: 4px;
                    margin-top: 8px;
                    border-right: 3px solid #ffc107;
                }}
                .attachments-section {{
                    background: white;
                    padding: 8px;
                    border-radius: 4px;
                    margin-top: 8px;
                    border-right: 3px solid #17a2b8;
                }}
                .footer {{
                    margin-top: 20px;
                    text-align: center;
                    font-size: 10px;
                    color: #666;
                    border-top: 1px solid #ddd;
                    padding-top: 15px;
                }}
                @media print {{
                    body {{ margin: 10px; }}
                    .meeting-card {{ page-break-inside: avoid; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="logo">القوات المسلحة الأردنية</div>
                <div class="title">تقرير الاجتماعات الشامل والمفصل</div>
                <div class="info">
                    مديرية الدائرة المالية<br>
                    تاريخ التقرير: {format_gregorian_date_now()}<br>
                    عدد الاجتماعات: {len(meetings)} اجتماع
                </div>
            </div>
        """

        # إضافة البيانات المفصلة لكل اجتماع
        for i, meeting in enumerate(meetings, 1):
            status = "ملغي" if meeting.is_cancelled else "مؤجل" if meeting.is_postponed else "نشط"
            status_class = "status-cancelled" if meeting.is_cancelled else "status-postponed" if meeting.is_postponed else "status-active"

            # جلب المرفقات
            attachments = MeetingAttachment.query.filter_by(meeting_id=meeting.id).all()

            html_content += f"""
            <div class="meeting-card">
                <div class="meeting-header">
                    <div>
                        <strong>اجتماع رقم {i}: {meeting.subject or 'بدون موضوع'}</strong>
                    </div>
                    <div>
                        <span class="status-badge {status_class}">{status}</span>
                    </div>
                </div>

                <div class="meeting-body">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">📅 التاريخ والوقت</div>
                            <div class="detail-value">
                                {meeting.meeting_date.strftime('%Y/%m/%d') if meeting.meeting_date else 'غير محدد'}<br>
                                الساعة: {military_time_filter(meeting.meeting_time) if meeting.meeting_time else 'غير محدد'}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">🏢 نوع الفعالية والمكان</div>
                            <div class="detail-value">
                                {meeting.meeting_type or 'غير محدد'}<br>
                                📍 {meeting.location or 'غير محدد'}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">🏛️ جهة الدعوة</div>
                            <div class="detail-value">
                                {meeting.inviting_party or 'غير محدد'}<br>
                                ⏰ الحضور قبل: {meeting.arrival_time_before or 15} دقيقة
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">📄 معلومات الكتاب</div>
                            <div class="detail-value">
                                رقم: {meeting.book_number or 'غير محدد'}<br>
                                تاريخ: {meeting.book_date.strftime('%Y/%m/%d') if meeting.book_date else 'غير محدد'}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">👔 زي الحضور</div>
                            <div class="detail-value">
                                {meeting.dress_code or 'غير محدد'}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">👤 معلومات النظام</div>
                            <div class="detail-value">
                                منشئ: {meeting.creator.username if meeting.creator else 'غير معروف'}<br>
                                إنشاء: {meeting.created_at.strftime('%Y/%m/%d %H:%M') if meeting.created_at else 'غير محدد'}
                            </div>
                        </div>
                    </div>

                    {f'''
                    <div class="notes-section">
                        <div class="detail-label">📝 ملاحظات إضافية</div>
                        <div class="detail-value">{meeting.notes}</div>
                    </div>
                    ''' if meeting.notes else ''}

                    {f'''
                    <div class="attachments-section">
                        <div class="detail-label">📎 المرفقات ({len(attachments)})</div>
                        <div class="detail-value">
                            {', '.join([f"• {att.original_filename} ({(att.file_size/1024):.1f} KB)" for att in attachments]) if attachments else 'لا توجد مرفقات'}
                        </div>
                    </div>
                    ''' if True else ''}
                </div>
            </div>
            """

        html_content += f"""
            <div class="footer">
                <p><strong>ملخص التقرير:</strong></p>
                <p>إجمالي الاجتماعات: {len(meetings)} |
                النشطة: {len([m for m in meetings if not m.is_cancelled and not m.is_postponed])} |
                الملغية: {len([m for m in meetings if m.is_cancelled])} |
                المؤجلة: {len([m for m in meetings if m.is_postponed])}</p>
                <p>تم إنشاء هذا التقرير تلقائياً من نظام إدارة الاجتماعات</p>
                <p>القوات المسلحة الأردنية - مديرية الدائرة المالية</p>
                <p style="font-size: 9px; color: #999;">تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """

        response = make_response(html_content)
        response.headers["Content-Disposition"] = f"attachment; filename=تقرير_الاجتماعات_{datetime.now().strftime('%Y-%m-%d_%H-%M')}.html"
        response.headers["Content-Type"] = "text/html; charset=utf-8"

        return response

    except Exception as e:
        flash(f'خطأ في تصدير PDF: {str(e)}', 'error')
        return redirect(url_for('reports'))

# تصدير Word
@app.route('/export_word')
@login_required
def export_word():
    try:
        meetings = Meeting.query.order_by(Meeting.meeting_date.asc()).all()

        # إنشاء محتوى RTF للـ Word
        rtf_content = r"""{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}
\f0\fs24
\qc\b تقرير الاجتماعات الشامل\b0\par
\qc القوات المسلحة الأردنية - مديرية الدائرة المالية\par
\qc تاريخ التقرير: """ + format_gregorian_date_now() + r"""\par
\qc عدد الاجتماعات: """ + str(len(meetings)) + r"""\par
\par
\trowd\trgaph108\trleft-108
\cellx1000\cellx2000\cellx3000\cellx4000\cellx5000\cellx6000\cellx7000\cellx8000\cellx9000\cellx10000
\b الرقم\cell نوع الفعالية\cell التاريخ\cell الوقت\cell المكان\cell الموضوع\cell جهة الدعوة\cell رقم الكتاب\cell اللباس\cell الحالة\cell\b0\row
"""

        # إضافة البيانات
        for i, meeting in enumerate(meetings, 1):
            status = "ملغي" if meeting.is_cancelled else "مؤجل" if meeting.is_postponed else "نشط"

            rtf_content += r"\trowd\trgaph108\trleft-108"
            rtf_content += r"\cellx1000\cellx2000\cellx3000\cellx4000\cellx5000\cellx6000\cellx7000\cellx8000\cellx9000\cellx10000"
            rtf_content += f"{i}\\cell "
            rtf_content += f"{meeting.meeting_type or 'غير محدد'}\\cell "
            rtf_content += f"{meeting.meeting_date.strftime('%Y/%m/%d') if meeting.meeting_date else ''}\\cell "
            rtf_content += f"{meeting.meeting_time.strftime('%H:%M') if meeting.meeting_time else ''}\\cell "
            rtf_content += f"{meeting.location or 'غير محدد'}\\cell "
            rtf_content += f"{meeting.subject or 'بدون موضوع'}\\cell "
            rtf_content += f"{meeting.inviting_party or 'غير محدد'}\\cell "
            rtf_content += f"{meeting.book_number or 'غير محدد'}\\cell "
            rtf_content += f"{meeting.dress_code or 'غير محدد'}\\cell "
            rtf_content += f"{status}\\cell\\row\n"

        rtf_content += r"""
\par
\qc تم إنشاء هذا التقرير تلقائياً من نظام إدارة الاجتماعات\par
\qc القوات المسلحة الأردنية - مديرية الدائرة المالية\par
}"""

        response = make_response(rtf_content)
        response.headers["Content-Disposition"] = f"attachment; filename=تقرير_الاجتماعات_{datetime.now().strftime('%Y-%m-%d_%H-%M')}.rtf"
        response.headers["Content-Type"] = "application/rtf; charset=utf-8"

        return response

    except Exception as e:
        flash(f'خطأ في تصدير Word: {str(e)}', 'error')
        return redirect(url_for('reports'))

# البحث الجديد مع التصدير
@app.route('/search_new', methods=['GET', 'POST'])
@login_required
def search_new():
    if request.method == 'POST':
        search_type = request.form.get('search_type')
        search_value = request.form.get('search_value', '').strip()
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')

        print(f"🔍 تم استلام طلب بحث جديد:")
        print(f"   - نوع البحث: '{search_type}'")
        print(f"   - قيمة البحث: '{search_value}'")
        print(f"   - تاريخ البداية: '{start_date}'")
        print(f"   - تاريخ النهاية: '{end_date}'")
        print(f"   - جميع بيانات النموذج: {dict(request.form)}")
        print(f"   - المستخدم الحالي: {current_user.username if current_user.is_authenticated else 'غير مسجل'}")
        print(f"   - عنوان IP: {request.remote_addr}")
        print(f"   - User Agent: {request.headers.get('User-Agent', 'غير محدد')[:50]}...")

        query = Meeting.query

        # تطبيق فلاتر البحث
        if search_type == 'date_range' and start_date and end_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Meeting.meeting_date.between(start_date_obj, end_date_obj))

        elif search_value:
            print(f"🔍 تطبيق فلتر البحث:")
            print(f"   - نوع البحث: {search_type}")
            print(f"   - قيمة البحث: '{search_value}'")

            if search_type == 'subject':
                query = query.filter(Meeting.subject.contains(search_value))
            elif search_type == 'inviting_party':
                # البحث المرن لجهة الدعوة - يدعم البحث الجزئي والكامل
                query = query.filter(
                    db.or_(
                        Meeting.inviting_party.contains(search_value),
                        Meeting.inviting_party.ilike(f'%{search_value}%'),
                        Meeting.inviting_party == search_value
                    )
                )
                print(f"   - البحث في جهة الدعوة باستخدام: contains, ilike, exact match")
            elif search_type == 'book_number':
                query = query.filter(Meeting.book_number.contains(search_value))
            elif search_type == 'location':
                query = query.filter(Meeting.location.contains(search_value))
            elif search_type == 'meeting_type':
                query = query.filter(Meeting.meeting_type.contains(search_value))

        results = query.order_by(Meeting.meeting_date.asc()).all()

        # تسجيل النتائج النهائية
        print(f"📊 النتائج النهائية:")
        print(f"   - نوع البحث: {search_type}")
        print(f"   - قيمة البحث: '{search_value}'")
        print(f"   - عدد النتائج المرسلة للعرض: {len(results)}")

        # طباعة تفاصيل النتائج للتحقق
        if search_type == 'inviting_party' and len(results) > 0:
            print(f"   - أول 3 نتائج:")
            for i, result in enumerate(results[:3]):
                print(f"     {i+1}. {result.subject} - جهة الدعوة: '{result.inviting_party}'")
        elif search_type == 'inviting_party' and len(results) == 0:
            print(f"   - لا توجد نتائج! التحقق من جهات الدعوة الموجودة:")
            all_parties = db.session.query(Meeting.inviting_party).distinct().all()
            for party in all_parties[:5]:
                print(f"     - '{party[0]}'")
                if party[0] and search_value.lower() in party[0].lower():
                    print(f"       ✅ يحتوي على '{search_value}'")
                else:
                    print(f"       ❌ لا يحتوي على '{search_value}'")



        return render_template('search_new.html',
                             results=results,
                             search_performed=True,
                             search_type=search_type,
                             search_value=search_value,
                             start_date=start_date,
                             end_date=end_date)

    return render_template('search_new.html', search_performed=False)

# تصدير نتائج البحث إلى PDF
@app.route('/export_search_pdf', methods=['POST'])
@login_required
def export_search_pdf():
    try:
        results_data = request.form.get('results_data')
        if not results_data:
            return jsonify({'success': False, 'message': 'لا توجد بيانات للتصدير'}), 400

        results = json.loads(results_data)

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير نتائج البحث الشامل</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    margin: 15px;
                    line-height: 1.4;
                    font-size: 11px;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #1B4332;
                    padding-bottom: 15px;
                }}
                .logo {{
                    color: #1B4332;
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 8px;
                }}
                .title {{
                    color: #1B4332;
                    font-size: 16px;
                    margin-bottom: 8px;
                }}
                .info {{
                    color: #666;
                    font-size: 12px;
                }}
                .meeting-card {{
                    border: 1px solid #ddd;
                    margin-bottom: 15px;
                    border-radius: 8px;
                    overflow: hidden;
                    page-break-inside: avoid;
                }}
                .meeting-header {{
                    background: linear-gradient(135deg, #1B4332, #2D5A3D);
                    color: white;
                    padding: 10px 15px;
                    font-weight: bold;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}
                .meeting-body {{
                    padding: 12px 15px;
                    background: #f9f9f9;
                }}
                .detail-grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 10px;
                    margin-bottom: 10px;
                }}
                .detail-item {{
                    background: white;
                    padding: 8px;
                    border-radius: 4px;
                    border-right: 3px solid #1B4332;
                }}
                .detail-label {{
                    font-weight: bold;
                    color: #1B4332;
                    font-size: 10px;
                    margin-bottom: 3px;
                }}
                .detail-value {{
                    color: #333;
                    font-size: 11px;
                }}
                .status-badge {{
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 10px;
                    font-weight: bold;
                }}
                .status-active {{ background: #d4edda; color: #155724; }}
                .status-cancelled {{ background: #f8d7da; color: #721c24; }}
                .status-postponed {{ background: #fff3cd; color: #856404; }}
                .footer {{
                    margin-top: 20px;
                    text-align: center;
                    font-size: 10px;
                    color: #666;
                    border-top: 1px solid #ddd;
                    padding-top: 15px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="logo">القوات المسلحة الأردنية</div>
                <div class="title">تقرير نتائج البحث الشامل</div>
                <div class="info">
                    مديرية الدائرة المالية<br>
                    تاريخ التقرير: {format_gregorian_date_now()}<br>
                    عدد النتائج: {len(results)} اجتماع
                </div>
            </div>
        """

        # عرض نتائج البحث بتفاصيل شاملة
        for i, result in enumerate(results, 1):
            status = "ملغي" if result.get('is_cancelled') else "مؤجل" if result.get('is_postponed') else "نشط"
            status_class = "status-cancelled" if result.get('is_cancelled') else "status-postponed" if result.get('is_postponed') else "status-active"

            html_content += f"""
            <div class="meeting-card">
                <div class="meeting-header">
                    <div>
                        <strong>نتيجة رقم {i}: {result.get('subject', 'بدون موضوع')}</strong>
                    </div>
                    <div>
                        <span class="status-badge {status_class}">{status}</span>
                    </div>
                </div>

                <div class="meeting-body">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">📅 التاريخ والوقت</div>
                            <div class="detail-value">
                                {result.get('meeting_date', 'غير محدد')}<br>
                                الساعة: {result.get('meeting_time', 'غير محدد')}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">🏢 نوع الفعالية والمكان</div>
                            <div class="detail-value">
                                {result.get('meeting_type', 'غير محدد')}<br>
                                📍 {result.get('location', 'غير محدد')}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">🏛️ جهة الدعوة</div>
                            <div class="detail-value">
                                {result.get('inviting_party', 'غير محدد')}<br>
                                ⏰ الحضور قبل: {result.get('arrival_time_before', 15)} دقيقة
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">📄 معلومات الكتاب</div>
                            <div class="detail-value">
                                رقم: {result.get('book_number', 'غير محدد')}<br>
                                تاريخ: {result.get('book_date', 'غير محدد')}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">👔 زي الحضور</div>
                            <div class="detail-value">
                                {result.get('dress_code', 'غير محدد')}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">👤 معلومات النظام</div>
                            <div class="detail-value">
                                منشئ: {result.get('creator', 'غير معروف')}<br>
                                إنشاء: {result.get('created_at', 'غير محدد')}
                            </div>
                        </div>
                    </div>

                    {f'''
                    <div class="notes-section">
                        <div class="detail-label">📝 ملاحظات إضافية</div>
                        <div class="detail-value">{result.get('notes', '')}</div>
                    </div>
                    ''' if result.get('notes') else ''}
                </div>
            </div>
            """

        html_content += f"""
            <div class="footer">
                <p><strong>ملخص نتائج البحث:</strong></p>
                <p>إجمالي النتائج: {len(results)} |
                النشطة: {len([r for r in results if not r.get('is_cancelled') and not r.get('is_postponed')])} |
                الملغية: {len([r for r in results if r.get('is_cancelled')])} |
                المؤجلة: {len([r for r in results if r.get('is_postponed')])}</p>
                <p>تم إنشاء هذا التقرير تلقائياً من نظام إدارة الاجتماعات</p>
                <p>القوات المسلحة الأردنية - مديرية الدائرة المالية</p>
                <p style="font-size: 9px; color: #999;">تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """

        response = make_response(html_content)
        response.headers["Content-Disposition"] = f"attachment; filename=نتائج_البحث_{datetime.now().strftime('%Y%m%d_%H%M')}.html"
        response.headers["Content-Type"] = "text/html; charset=utf-8"

        return response

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في التصدير: {str(e)}'}), 500

# تعديل اجتماع
@app.route('/edit_meeting/<int:meeting_id>', methods=['GET', 'POST'])
@login_required
def edit_meeting(meeting_id):
    meeting = Meeting.query.get_or_404(meeting_id)

    if request.method == 'POST':
        try:
            meeting.subject = request.form['subject']
            meeting.meeting_type = request.form['meeting_type']
            meeting.meeting_date = datetime.strptime(request.form['meeting_date'], '%Y-%m-%d').date()
            meeting.meeting_time = datetime.strptime(request.form['meeting_time'], '%H:%M').time()
            meeting.location = request.form['location']
            meeting.inviting_party = request.form['inviting_party']
            meeting.arrival_time_before = int(request.form.get('arrival_time_before', 15))
            meeting.book_number = request.form['book_number']
            meeting.book_date = datetime.strptime(request.form['book_date'], '%Y-%m-%d').date()
            meeting.dress_code = request.form['dress_code']
            meeting.notes = request.form.get('notes', '')
            meeting.updated_at = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث الاجتماع بنجاح', 'success')
            return redirect(url_for('edit_meetings'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تحديث الاجتماع: {str(e)}', 'error')

    return render_template('edit_meeting_form.html', meeting=meeting)

# حذف اجتماع
@app.route('/delete_meeting/<int:meeting_id>', methods=['POST'])
@login_required
def delete_meeting(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        db.session.delete(meeting)
        db.session.commit()
        flash('تم حذف الاجتماع بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف الاجتماع: {str(e)}', 'error')

    return redirect(url_for('edit_meetings'))

# تأجيل اجتماع
@app.route('/postpone_meeting/<int:meeting_id>', methods=['POST'])
@login_required
def postpone_meeting(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        meeting.is_postponed = True
        meeting.updated_at = datetime.utcnow()
        db.session.commit()
        flash('تم تأجيل الاجتماع بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في تأجيل الاجتماع: {str(e)}', 'error')

    return redirect(url_for('edit_meetings'))

# إلغاء اجتماع
@app.route('/cancel_meeting/<int:meeting_id>', methods=['POST'])
@login_required
def cancel_meeting(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        meeting.is_cancelled = True
        meeting.updated_at = datetime.utcnow()
        db.session.commit()
        flash('تم إلغاء الاجتماع بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في إلغاء الاجتماع: {str(e)}', 'error')

    return redirect(url_for('edit_meetings'))

# إعادة تفعيل اجتماع
@app.route('/reactivate_meeting/<int:meeting_id>', methods=['POST'])
@login_required
def reactivate_meeting(meeting_id):
    try:
        meeting = Meeting.query.get_or_404(meeting_id)
        meeting.is_cancelled = False
        meeting.is_postponed = False
        meeting.updated_at = datetime.utcnow()
        db.session.commit()
        flash('تم إعادة تفعيل الاجتماع بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في إعادة تفعيل الاجتماع: {str(e)}', 'error')

    return redirect(url_for('edit_meetings'))

# إنشاء قاعدة البيانات والبيانات الافتراضية
def create_default_data():
    with app.app_context():
        try:
            db.create_all()

            # إنشاء مستخدم افتراضي
            if not User.query.filter_by(username='admin').first():
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    is_admin=True
                )
                admin.set_password('admin')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي: admin/admin")

            # إضافة بعض الاجتماعات التجريبية إذا لم تكن موجودة
            if Meeting.query.count() == 0:
                # اجتماع واحد في 2024
                meeting_2024 = Meeting(
                    title='اجتماع مجلس الإدارة - مارس 2024',
                    subject='اجتماع مجلس الإدارة الشهري',
                    meeting_type='اجتماع إداري',
                    meeting_date=date(2024, 3, 15),
                    meeting_time=time(10, 30),
                    location='قاعة الاجتماعات الرئيسية',
                    inviting_party='مجلس الإدارة',
                    book_number='001/2024/م.د',
                    book_date=date(2024, 3, 10),
                    dress_code='الزي الرسمي',
                    notes='اجتماع دوري لمناقشة الأمور الإدارية',
                    is_cancelled=False,
                    is_postponed=False,
                    creator_id=1
                )
                db.session.add(meeting_2024)

                # 10 اجتماعات في 2025
                meetings_2025_data = [
                    ('اجتماع يناير 2025', date(2025, 1, 10), False),
                    ('اجتماع فبراير 2025', date(2025, 2, 15), False),
                    ('اجتماع مارس 2025', date(2025, 3, 20), True),  # ملغي
                    ('اجتماع أبريل 2025', date(2025, 4, 12), False),
                    ('اجتماع مايو 2025', date(2025, 5, 18), False),
                    ('اجتماع يونيو 2025', date(2025, 6, 25), False),
                    ('اجتماع يوليو 2025', date(2025, 7, 8), True),   # ملغي
                    ('اجتماع أغسطس 2025', date(2025, 8, 14), False),
                    ('اجتماع سبتمبر 2025', date(2025, 9, 22), False),
                    ('اجتماع أكتوبر 2025', date(2025, 10, 30), False)
                ]

                for i, (title, meeting_date, is_cancelled) in enumerate(meetings_2025_data, 1):
                    meeting = Meeting(
                        title=title,
                        subject=f'اجتماع شهري رقم {i}',
                        meeting_type='اجتماع إداري',
                        meeting_date=meeting_date,
                        meeting_time=time(14, 0),
                        location='قاعة المؤتمرات',
                        inviting_party='الإدارة العامة',
                        book_number=f'{i:03d}/2025/م.د',
                        book_date=meeting_date - timedelta(days=5),
                        dress_code='الزي الرسمي',
                        notes=f'اجتماع شهري لسنة 2025 - الشهر {meeting_date.month}',
                        is_cancelled=is_cancelled,
                        is_postponed=False,
                        creator_id=1
                    )
                    db.session.add(meeting)

                db.session.commit()
                print("✅ تم إضافة اجتماعات تجريبية: 1 في 2024 و 10 في 2025")

            print("✅ تم إنشاء قاعدة البيانات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

if __name__ == '__main__':
    create_default_data()

    print("🚀 تشغيل نظام إدارة الاجتماعات الكامل...")
    print("📍 الرابط: http://127.0.0.1:5000")
    print("🔑 المستخدم: admin | كلمة المرور: admin")
    print("🟢 شاشة الدخول الخضراء متاحة")
    print("👁️ أزرار العين متاحة في صفحة جميع الاجتماعات")
    print("📋 جميع الشاشات متاحة")
    print("=" * 60)

    app.run(host='127.0.0.1', port=5000, debug=True, use_reloader=False)
