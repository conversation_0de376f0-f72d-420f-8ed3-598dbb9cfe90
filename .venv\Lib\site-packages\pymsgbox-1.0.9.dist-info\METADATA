Metadata-Version: 2.4
Name: PyMsgBox
Version: 1.0.9
Summary: A simple, cross-platform, pure Python module for JavaScript-like message boxes.
Home-page: https://github.com/asweigart/pymsgbox
Author: <PERSON>
Author-email: <EMAIL>
License: GPLv3+
Classifier: License :: OSI Approved :: GNU General Public License v3 or later (GPLv3+)
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Description-Content-Type: text/markdown
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: summary

PyMsgBox
========

 A simple, cross-platform, pure Python module for JavaScript-like message boxes.

To import, run:

    >>> from pymsgbox import *`

 There are four functions in PyMsgBox, which follow JavaScript's message box naming conventions:

    >>> alert(text='', title='', button='OK')`

    Displays a simple message box with text and a single OK button. Returns the text of the button clicked on.

    >>> confirm(text='', title='', buttons=['OK', 'Cancel'])`

    Displays a message box with OK and Cancel buttons. Number and text of buttons can be customized. Returns the text of the button clicked on.

    >>> prompt(text='', title='' , defaultValue='')`

    Displays a message box with text input, and OK & Cancel buttons. Returns the text entered, or None if Cancel was clicked.

    >>> password(text='', title='', defaultValue='', mask='*')`

    Displays a message box with text input, and OK & Cancel buttons. Typed characters appear as *. Returns the text entered, or None if Cancel was clicked.

On Linux Python 2, you need to first install Tkinter by running: sudo apt-get install python-tk

Modified BSD License

Derived from Stephen Raymond Ferg's EasyGui http://easygui.sourceforge.net/
