{% extends "base.html" %}

{% block title %}تعديل الاجتماعات - نظام اجتماعات المدير{% endblock %}

{% block extra_css %}
<style>
    .meetings-table {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .table-header {
        background: var(--jaf-gradient);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
    }
    
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }
    
    .meeting-row {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .meeting-row:hover {
        background-color: rgba(27, 67, 50, 0.05);
        transform: translateX(-2px);
    }
    
    .meeting-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-active {
        background: linear-gradient(135deg, #28a745, #34ce57);
        color: white;
        border: 2px solid #28a745;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    .status-cancelled {
        background: linear-gradient(135deg, #6f42c1, #8b5cf6);
        color: white;
        border: 2px solid #6f42c1;
        box-shadow: 0 2px 4px rgba(111, 66, 193, 0.3);
    }

    .status-postponed {
        background: linear-gradient(135deg, #f39c12, #fbbf24);
        color: white;
        border: 2px solid #f39c12;
        box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
    }

    .status-completed {
        background: linear-gradient(135deg, #e74c3c, #f56565);
        color: white;
        border: 2px solid #e74c3c;
        box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
    }

    /* تأثيرات hover للأزرار - ظل بنفس لون الزر */
    .status-active:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.5);
        background: linear-gradient(135deg, #34ce57, #28a745);
    }

    .status-cancelled:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(111, 66, 193, 0.5);
        background: linear-gradient(135deg, #8b5cf6, #6f42c1);
    }

    .status-postponed:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(243, 156, 18, 0.5);
        background: linear-gradient(135deg, #fbbf24, #f39c12);
    }

    .status-completed:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(231, 76, 60, 0.5);
        background: linear-gradient(135deg, #f56565, #e74c3c);
    }

    /* انتقال سلس للتأثيرات */
    .meeting-status {
        transition: all 0.3s ease;
    }

    /* تنسيق النصوص داخل أزرار الإجراءات */
    .btn-action {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 80px;
        padding: 8px 12px;
        font-size: 12px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .btn-action .btn-text {
        font-size: 11px;
        font-weight: 600;
        white-space: nowrap;
    }

    .btn-action i {
        font-size: 12px;
    }

    /* تحسين المظهر للأزرار الصغيرة */
    @media (max-width: 768px) {
        .btn-action .btn-text {
            display: none;
        }
        .btn-action {
            min-width: 40px;
            padding: 6px 8px;
        }
        .btn-action i {
            margin: 0 !important;
        }
    }
    
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }
    
    .btn-action {
        padding: 0.375rem 0.75rem;
        border-radius: 8px;
        border: none;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }
    
    .btn-edit {
        background-color: var(--jaf-info);
        color: white;
    }
    
    .btn-edit:hover {
        background-color: var(--jaf-primary);
        transform: translateY(-1px);
    }
    
    .btn-postpone {
        background-color: var(--jaf-warning);
        color: white;
    }
    
    .btn-postpone:hover {
        background-color: #e66900;
        transform: translateY(-1px);
    }
    
    .btn-cancel {
        background-color: var(--jaf-danger);
        color: white;
    }
    
    .btn-cancel:hover {
        background-color: #b91c1c;
        transform: translateY(-1px);
    }

    .btn-success {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        color: #333;
        font-weight: bold;
    }

    .btn-success:hover {
        background-color: #218838;
        transform: translateY(-1px);
    }
    
    .btn-delete {
        background-color: #6c757d;
        color: white;
    }
    
    .btn-delete:hover {
        background-color: #495057;
        transform: translateY(-1px);
    }
    
    .filter-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .meeting-details {
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .meeting-type-badge {
        background: var(--jaf-primary);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* تحسين حقول التاريخ والوقت مع Date/Time Picker */
    input[type="date"], input[type="time"], input[type="number"] {
        direction: ltr !important;
        text-align: left !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-variant-numeric: lining-nums !important;
        padding: 12px 16px !important;
        border: 2px solid #e0e0e0 !important;
        border-radius: 8px !important;
        background-color: #ffffff !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
        min-height: 50px !important;
    }

    /* تحسين مظهر عند التركيز */
    input[type="date"]:focus, input[type="time"]:focus {
        border-color: #1B4332 !important;
        box-shadow: 0 0 0 3px rgba(27, 67, 50, 0.1) !important;
        outline: none !important;
    }

    /* تحسين مظهر عند التمرير */
    input[type="date"]:hover, input[type="time"]:hover {
        border-color: #2D5A3D !important;
        background-color: #f8f9fa !important;
    }

    /* تحسين أيقونة التقويم */
    input[type="date"]::-webkit-calendar-picker-indicator {
        opacity: 1 !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%231B4332' d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e") !important;
        background-size: 20px 20px !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        width: 30px !important;
        height: 30px !important;
        cursor: pointer !important;
        margin-left: 8px !important;
        border-radius: 4px !important;
        transition: all 0.2s ease !important;
    }

    /* تحسين أيقونة الوقت */
    input[type="time"]::-webkit-calendar-picker-indicator {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%231B4332' d='M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z'/%3e%3cpath fill='%231B4332' d='M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z'/%3e%3c/svg%3e") !important;
    }

    /* تأثير عند التمرير على الأيقونات */
    input[type="date"]::-webkit-calendar-picker-indicator:hover,
    input[type="time"]::-webkit-calendar-picker-indicator:hover {
        background-color: rgba(27, 67, 50, 0.1) !important;
        transform: scale(1.1) !important;
    }

    /* تصميم خيارات اللباس المصغرة في نافذة التعديل */
    .dress-option-small {
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 2px solid #dee2e6 !important;
        border-radius: 8px !important;
        transition: all 0.3s ease;
        background: #fff;
    }

    .dress-option-small:hover {
        border-color: #0d6efd !important;
        background: rgba(13, 110, 253, 0.1);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
    }

    .dress-option-small.active,
    .btn-check:checked + .dress-option-small {
        border-color: #0d6efd !important;
        background: rgba(13, 110, 253, 0.1);
        color: #0d6efd !important;
    }

    .dress-icon-small {
        width: 24px;
        height: 24px;
        margin-bottom: 4px;
        filter: grayscale(100%);
        transition: all 0.3s ease;
    }

    .btn-check:checked + .dress-option-small .dress-icon-small {
        filter: none;
    }

    .dress-label-small {
        font-size: 0.75rem;
        font-weight: 600;
        text-align: center;
    }

    /* تصميم حقل التاريخ المبسط */
    .date-input-wrapper {
        position: relative;
    }

    .date-input-simple {
        opacity: 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        cursor: pointer;
    }

    .date-display-overlay {
        position: relative;
        z-index: 1;
        background: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
    }

    .date-display-overlay:hover {
        border-color: #0d6efd;
        background-color: #f8f9fa;
    }

    .date-display-text {
        font-weight: 600;
        color: #495057;
        direction: ltr;
        text-align: center;
        flex: 1;
    }

    .date-icon {
        color: #6c757d;
        margin-left: 8px;
    }

    .form-floating label span {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        direction: rtl !important;
        text-align: right !important;
    }

    /* ضمان الأرقام الإنجليزية في الجداول */
    .table td, .table th {
        font-variant-numeric: lining-nums !important;
    }

    /* تصميم عمود التاريخ والوقت الذهبي */
    .date-column {
        min-width: 150px;
        text-align: center;
        padding: 15px 10px;
    }

    /* تصميم التاريخ الذهبي الغامق */
    .date-display {
        transition: all 0.3s ease;
    }

    .date-display:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(184, 134, 11, 0.7) !important;
        background: linear-gradient(135deg, #daa520 0%, #ffd700 100%) !important;
    }

    /* تصميم الوقت الذهبي الغامق */
    .time-display {
        transition: all 0.3s ease;
    }

    .time-display:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(184, 134, 11, 0.6) !important;
        background: linear-gradient(135deg, #daa520 0%, #ffd700 100%) !important;
    }

    .meeting-row:hover .date-display,
    .meeting-row:hover .time-display {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(184, 134, 11, 0.7) !important;
    }

    /* تصميم أيقونة التاريخ في شاشة التعديل - حجم أصغر */
    .date-input-container {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    .date-picker-overlay {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        width: 44px;
        height: 44px;
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border-radius: 12px;
        display: flex !important;
        align-items: center;
        justify-content: center;
        color: #333 !important;
        font-size: 1.3rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 6px 18px rgba(255, 215, 0, 0.4);
        z-index: 10 !important;
        border: 2px solid rgba(255, 255, 255, 0.3);
        font-weight: bold;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .date-picker-overlay:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 8px 22px rgba(255, 215, 0, 0.6);
        background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
        color: #fff !important;
    }

    .date-picker-overlay i {
        animation: gentle-pulse 2.5s ease-in-out infinite;
        text-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }

    @keyframes gentle-pulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.08);
            opacity: 0.9;
        }
    }

    /* إصلاح ترتيب الجدول - ضمان الترتيب الصحيح من الأقرب للأبعد */
    #meetingsTableBody {
        display: table-row-group !important;
        flex-direction: column !important;
    }

    .meeting-row {
        display: table-row !important;
        order: initial !important;
    }

    /* منع أي تأثير RTL على ترتيب الصفوف */
    .table tbody {
        direction: ltr !important;
    }

    .table tbody tr {
        direction: ltr !important;
    }

    .table tbody tr td {
        direction: rtl !important;
    }

    /* إجبار الترتيب الصحيح - الأقرب أولاً */
    .table tbody {
        display: flex !important;
        flex-direction: column !important;
    }

    .table tbody tr:nth-child(1) { order: 1 !important; }
    .table tbody tr:nth-child(2) { order: 2 !important; }
    .table tbody tr:nth-child(3) { order: 3 !important; }
    .table tbody tr:nth-child(4) { order: 4 !important; }
    .table tbody tr:nth-child(5) { order: 5 !important; }
    .table tbody tr:nth-child(6) { order: 6 !important; }
    .table tbody tr:nth-child(7) { order: 7 !important; }
    .table tbody tr:nth-child(8) { order: 8 !important; }
    .table tbody tr:nth-child(9) { order: 9 !important; }
    .table tbody tr:nth-child(10) { order: 10 !important; }

    .meeting-row {
        display: table-row !important;
        width: 100% !important;
    }

    /* مودال التضارب - نفس التصميم من شاشة الإضافة */
    .conflict-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .conflict-modal.show {
        opacity: 1;
    }

    .conflict-modal-content {
        background: white;
        border-radius: 15px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }

    .conflict-modal.show .conflict-modal-content {
        transform: scale(1);
    }

    .conflict-modal-header {
        background: linear-gradient(135deg, #f77f00 0%, #fcbf49 100%);
        color: white;
        padding: 20px;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .conflict-modal-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .conflict-modal-body {
        padding: 20px;
    }

    .conflict-modal-footer {
        padding: 15px 20px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        border-radius: 0 0 15px 15px;
    }

    .conflict-item {
        margin-bottom: 10px;
    }

    .conflict-item .border {
        border-color: #f77f00 !important;
        background: rgba(247, 127, 0, 0.05);
    }

    /* مودال التأكيد المخصص */
    .custom-confirm-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        z-index: 10000;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .custom-confirm-modal.show {
        opacity: 1;
    }

    .custom-confirm-content {
        background: white;
        border-radius: 15px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        transform: scale(0.9);
        transition: transform 0.3s ease;
        overflow: hidden;
    }

    .custom-confirm-modal.show .custom-confirm-content {
        transform: scale(1);
    }

    .custom-confirm-header {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        color: #333;
        padding: 20px;
        text-align: center;
    }

    .custom-confirm-header h4 {
        margin: 0;
        font-weight: 600;
        font-size: 1.2rem;
    }

    .custom-confirm-body {
        padding: 30px 20px;
        text-align: center;
    }

    .custom-confirm-body p {
        font-size: 1.1rem;
        color: #333;
        margin: 0;
        line-height: 1.6;
    }

    .custom-confirm-footer {
        padding: 20px;
        display: flex;
        justify-content: center;
        gap: 15px;
        border-top: 1px solid #eee;
    }

    .custom-confirm-btn {
        padding: 12px 30px;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 100px;
    }

    .custom-confirm-btn.confirm {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        color: #333;
        font-weight: bold;
    }

    .custom-confirm-btn.confirm:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }

    .custom-confirm-btn.cancel {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }

    .custom-confirm-btn.cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }

    /* تحسين إضافي للمودال */
    .custom-confirm-modal {
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }

    .custom-confirm-content {
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            transform: scale(0.7) translateY(-50px);
            opacity: 0;
        }
        to {
            transform: scale(1) translateY(0);
            opacity: 1;
        }
    }

    /* تحسينات مودال التأجيل */
    .form-check {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .form-check:hover {
        background-color: #e9ecef !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .form-check-input:checked + .form-check-label {
        color: var(--jaf-primary);
        font-weight: bold;
    }

    .form-check-input:checked ~ .text-muted {
        color: var(--jaf-primary) !important;
        opacity: 0.8;
    }

    #dateTimeSection {
        transition: all 0.4s ease;
        opacity: 0;
    }

    #dateTimeSection.show {
        opacity: 1;
    }

    /* تحسين مظهر حقول التاريخ والوقت في مودال التأجيل */
    .date-input-container, .time-input-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .date-placeholder, .time-placeholder {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        pointer-events: none;
        font-size: 14px;
        font-weight: 500;
        z-index: 1;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
        padding: 2px 6px;
        border-radius: 4px;
    }

    /* إخفاء الـ placeholder عند وجود قيمة أو التركيز */
    .professional-date-input:valid ~ .date-placeholder,
    .professional-date-input:focus ~ .date-placeholder {
        opacity: 0;
        transform: translateY(-50%) translateX(-10px);
    }

    .professional-time-input:valid ~ .time-placeholder,
    .professional-time-input:focus ~ .time-placeholder {
        opacity: 0;
        transform: translateY(-50%) translateX(-10px);
    }

    /* تحسين مظهر الحقول */
    .professional-date-input, .professional-time-input {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 10px 40px 10px 15px;
        font-size: 14px;
        color: #495057;
        transition: all 0.3s ease;
        width: 100%;
    }

    .professional-date-input:focus, .professional-time-input:focus {
        border-color: var(--jaf-primary);
        box-shadow: 0 0 0 3px rgba(27, 67, 50, 0.1);
        outline: none;
    }

    /* إخفاء النص الافتراضي للمتصفح */
    .professional-date-input::-webkit-datetime-edit-text,
    .professional-date-input::-webkit-datetime-edit-month-field,
    .professional-date-input::-webkit-datetime-edit-day-field,
    .professional-date-input::-webkit-datetime-edit-year-field {
        color: transparent;
    }

    .professional-date-input:focus::-webkit-datetime-edit-text,
    .professional-date-input:focus::-webkit-datetime-edit-month-field,
    .professional-date-input:focus::-webkit-datetime-edit-day-field,
    .professional-date-input:focus::-webkit-datetime-edit-year-field,
    .professional-date-input:valid::-webkit-datetime-edit-text,
    .professional-date-input:valid::-webkit-datetime-edit-month-field,
    .professional-date-input:valid::-webkit-datetime-edit-day-field,
    .professional-date-input:valid::-webkit-datetime-edit-year-field {
        color: #495057;
    }

    .professional-time-input::-webkit-datetime-edit-text,
    .professional-time-input::-webkit-datetime-edit-hour-field,
    .professional-time-input::-webkit-datetime-edit-minute-field,
    .professional-time-input::-webkit-datetime-edit-ampm-field {
        color: transparent;
    }

    .professional-time-input:focus::-webkit-datetime-edit-text,
    .professional-time-input:focus::-webkit-datetime-edit-hour-field,
    .professional-time-input:focus::-webkit-datetime-edit-minute-field,
    .professional-time-input:focus::-webkit-datetime-edit-ampm-field,
    .professional-time-input:valid::-webkit-datetime-edit-text,
    .professional-time-input:valid::-webkit-datetime-edit-hour-field,
    .professional-time-input:valid::-webkit-datetime-edit-minute-field,
    .professional-time-input:valid::-webkit-datetime-edit-ampm-field {
        color: #495057;
    }

    /* تحسين الـ overlay */
    .date-picker-overlay, .time-picker-overlay {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--jaf-primary);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 2;
    }

    .date-picker-overlay:hover, .time-picker-overlay:hover {
        background: var(--jaf-secondary);
        transform: translateY(-50%) scale(1.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-primary mb-1">
                <i class="fas fa-edit me-2"></i>
                تعديل الاجتماعات
            </h2>
            <p class="text-muted mb-0">إدارة وتعديل الاجتماعات الموجودة</p>
        </div>
        <div>
            <a href="{{ url_for('add_meeting') }}" class="btn btn-jaf-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة اجتماع جديد
            </a>
        </div>
    </div>
    
    <!-- Filter Section -->
    <div class="filter-section fade-in-up">
        <div class="row">
            <div class="col-md-3">
                <div class="form-floating">
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="cancelled">ملغي</option>
                        <option value="postponed">مؤجل</option>
                    </select>
                    <label for="statusFilter">
                        <i class="fas fa-filter me-2"></i>تصفية حسب الحالة
                    </label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating">
                    <select class="form-select" id="typeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="اجتماع">اجتماع</option>
                        <option value="دعوة">دعوة</option>
                        <option value="زيارة">زيارة</option>
                        <option value="إيجاز">إيجاز</option>
                        <option value="تمرين">تمرين</option>
                        <option value="اتصال مرئي">اتصال مرئي</option>
                    </select>
                    <label for="typeFilter">
                        <i class="fas fa-tags me-2"></i>نوع الفعالية
                    </label>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-floating">
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث...">
                    <label for="searchInput">
                        <i class="fas fa-search me-2"></i>البحث في الموضوع أو المكان أو رقم الكتاب
                    </label>
                </div>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-secondary w-100 h-100" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>
                    مسح الفلاتر
                </button>
            </div>
        </div>
    </div>
    
    <!-- Meetings Table -->
    <div class="meetings-table fade-in-up">
        <div class="table-header">
            <i class="fas fa-calendar-alt me-2"></i>
            الاجتماعات المجدولة
        </div>
        
        <div class="table-responsive">
            {% if meetings %}
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>التاريخ والوقت</th>
                        <th>الموضوع</th>
                        <th>النوع</th>
                        <th>المكان</th>
                        <th>جهة الدعوة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="meetingsTableBody">
                    <!-- إجبار الترتيب الصحيح: الأقرب أولاً -->
                    {% set meetings_sorted = meetings|list %}
                    {% for meeting in meetings_sorted %}
                    <tr class="meeting-row"
                        data-meeting-id="{{ meeting.id }}"
                        data-order="{{ loop.index }}"
                        data-date="{{ meeting.meeting_date }}"
                        style="order: {{ loop.index }} !important;">
                        <td class="text-center">
                            <span class="badge bg-primary">#{{ loop.index }}</span>
                        </td>
                        <td class="date-column">
                            <div class="fw-bold text-center date-display"
                                 style="background: linear-gradient(135deg, #b8860b 0%, #daa520 100%);
                                        color: white;
                                        padding: 10px 15px;
                                        border-radius: 25px;
                                        font-size: 0.9rem;
                                        font-weight: 700;
                                        box-shadow: 0 3px 10px rgba(184, 134, 11, 0.5);
                                        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
                                        margin-bottom: 10px;
                                        display: inline-block;"
                                 dir="rtl">
                                {% set day_names = {
                                    'Monday': 'الاثنين',
                                    'Tuesday': 'الثلاثاء',
                                    'Wednesday': 'الأربعاء',
                                    'Thursday': 'الخميس',
                                    'Friday': 'الجمعة',
                                    'Saturday': 'السبت',
                                    'Sunday': 'الأحد'
                                } %}
                                {{ day_names[meeting.meeting_date.strftime('%A')] }} {{ meeting.meeting_date.strftime('%Y/%m/%d') }}
                            </div>
                            <div class="meeting-details time-display text-center"
                                 style="background: linear-gradient(135deg, #b8860b 0%, #daa520 100%);
                                        color: white;
                                        padding: 8px 12px;
                                        border-radius: 20px;
                                        font-size: 0.85rem;
                                        font-weight: 600;
                                        box-shadow: 0 2px 8px rgba(184, 134, 11, 0.4);
                                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                                        display: inline-block;"
                                 dir="rtl">
                                {{ meeting.meeting_time | twelve_hour_time }}
                            </div>
                        </td>
                        <td class="subject-column">
                            <div class="fw-bold text-wrap">{{ meeting.subject }}</div>
                            <div class="meeting-details">
                                <i class="fas fa-file-alt me-1"></i>
                                {{ meeting.book_number }}
                            </div>
                        </td>
                        <td>
                            <span class="meeting-type-badge">{{ meeting.meeting_type }}</span>
                        </td>
                        <td class="location-column">
                            <div class="text-wrap">{{ meeting.location }}</div>
                        </td>
                        <td class="inviting-party-column">
                            <div class="text-wrap">{{ meeting.inviting_party }}</div>
                        </td>
                        <td>
                            {% if meeting.status == 'cancelled' %}
                                <span class="meeting-status status-cancelled">ملغي</span>
                            {% elif meeting.status == 'postponed' %}
                                <span class="meeting-status status-postponed">مؤجل</span>
                            {% elif meeting.status == 'completed' %}
                                <span class="meeting-status status-completed">منتهي</span>
                            {% else %}
                                <span class="meeting-status status-active">نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button type="button" class="btn btn-action btn-edit"
                                        data-meeting-id="{{ meeting.id }}"
                                        onclick="editMeetingDirect({{ meeting.id }})"
                                        title="تعديل">
                                    <i class="fas fa-edit me-1"></i>
                                    <span class="btn-text">تعديل</span>
                                </button>

                                {% if not meeting.is_cancelled and not meeting.is_postponed %}
                                <button type="button" class="btn btn-action btn-postpone"
                                        data-meeting-id="{{ meeting.id }}"
                                        onclick="postponeMeeting({{ meeting.id }})"
                                        title="تأجيل">
                                    <i class="fas fa-clock me-1"></i>
                                    <span class="btn-text">تأجيل</span>
                                </button>

                                <button type="button" class="btn btn-action btn-cancel"
                                        data-meeting-id="{{ meeting.id }}"
                                        onclick="cancelMeeting({{ meeting.id }})"
                                        title="إلغاء">
                                    <i class="fas fa-ban me-1"></i>
                                    <span class="btn-text">إلغاء</span>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-action btn-success"
                                        data-meeting-id="{{ meeting.id }}"
                                        onclick="reactivateMeeting({{ meeting.id }})"
                                        title="إعادة تفعيل">
                                    <i class="fas fa-undo me-1"></i>
                                    <span class="btn-text">تفعيل</span>
                                </button>
                                {% endif %}

                                <button type="button" class="btn btn-action btn-delete"
                                        data-meeting-id="{{ meeting.id }}"
                                        onclick="deleteMeeting({{ meeting.id }})"
                                        title="حذف">
                                    <i class="fas fa-trash me-1"></i>
                                    <span class="btn-text">حذف</span>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h4>لا توجد اجتماعات</h4>
                <p>لم يتم إنشاء أي اجتماعات بعد</p>
                <a href="{{ url_for('add_meeting') }}" class="btn btn-jaf-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة اجتماع جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Edit Meeting Modal -->
<div class="modal fade" id="editMeetingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: var(--jaf-gradient); color: white;">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الاجتماع
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editMeetingForm">
                    <!-- سيتم ملء المحتوى ديناميكياً -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-jaf-primary" onclick="saveMeetingChanges()">
                    <i class="fas fa-save me-1"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Postpone Meeting Modal -->
<div class="modal fade" id="postponeMeetingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background: var(--jaf-warning); color: white;">
                <h5 class="modal-title">
                    <i class="fas fa-clock me-2"></i>
                    تأجيل الاجتماع
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="postponeMeetingForm">
                    <!-- خيارات التأجيل -->
                    <div class="mb-4">
                        <h6 class="fw-bold text-primary mb-3">
                            <i class="fas fa-list-ul me-2"></i>
                            اختر نوع التأجيل:
                        </h6>

                        <!-- الخيار الأول: تأجيل إلى إشعار آخر -->
                        <div class="form-check mb-3 p-3 border rounded" style="background-color: #f8f9fa;">
                            <input class="form-check-input" type="radio" name="postponeType" id="postponeToLater" value="later" checked>
                            <label class="form-check-label fw-bold" for="postponeToLater">
                                <i class="fas fa-bell me-2 text-warning"></i>
                                تأجيل إلى إشعار آخر يحدد لاحقاً
                            </label>
                            <div class="text-muted small mt-1">
                                سيتم تأجيل الاجتماع بدون تحديد تاريخ ووقت جديد، وسيتم إشعارك لاحقاً لتحديد الموعد الجديد
                            </div>
                        </div>

                        <!-- الخيار الثاني: تحديد تاريخ ووقت -->
                        <div class="form-check mb-3 p-3 border rounded" style="background-color: #f8f9fa;">
                            <input class="form-check-input" type="radio" name="postponeType" id="postponeToDate" value="date">
                            <label class="form-check-label fw-bold" for="postponeToDate">
                                <i class="fas fa-calendar-check me-2 text-success"></i>
                                تحديد تاريخ ووقت جديد
                            </label>
                            <div class="text-muted small mt-1">
                                اختر التاريخ والوقت الجديد للاجتماع
                            </div>
                        </div>
                    </div>

                    <!-- قسم التاريخ والوقت الجديد (يظهر عند اختيار الخيار الثاني) -->
                    <div id="dateTimeSection" style="display: none;">
                        <!-- قسم التاريخ الجديد -->
                        <div class="professional-input-group mb-4">
                            <label class="professional-label">
                                <i class="fas fa-calendar-alt me-2"></i>
                                التاريخ الجديد
                            </label>
                            <div class="date-input-container">
                                <input type="date" class="form-control professional-date-input" id="newDate" dir="ltr">
                                <div class="date-picker-overlay" onclick="document.getElementById('newDate').focus()">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="date-placeholder">اختر تاريخ</div>

                            </div>
                        </div>

                        <!-- قسم الوقت الجديد -->
                        <div class="professional-input-group mb-4">
                            <label class="professional-label">
                                <i class="fas fa-clock me-2"></i>
                                الوقت الجديد
                            </label>
                            <div class="time-input-container">
                                <input type="time" class="form-control professional-time-input" id="newTime" dir="ltr">
                                <div class="time-picker-overlay">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="time-placeholder">اختر الوقت</div>

                            </div>
                        </div>
                    </div>

                    <!-- سبب التأجيل -->
                    <div class="form-floating">
                        <textarea class="form-control" id="postponeReason" style="height: 100px"></textarea>
                        <label for="postponeReason">سبب التأجيل (اختياري)</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-warning" onclick="confirmPostpone()">
                    <i class="fas fa-clock me-1"></i>
                    <span id="postponeButtonText">تأجيل الاجتماع</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- مودال التأكيد المخصص -->
<div id="customConfirmModal" class="custom-confirm-modal">
    <div class="custom-confirm-content">
        <div class="custom-confirm-header">
            <h4 id="confirmTitle">تأكيد العملية</h4>
        </div>
        <div class="custom-confirm-body">
            <p id="confirmMessage">هل تريد تعديل هذا الاجتماع؟</p>
        </div>
        <div class="custom-confirm-footer">
            <button type="button" class="custom-confirm-btn cancel" onclick="hideCustomConfirm()">إلغاء</button>
            <button type="button" class="custom-confirm-btn confirm" id="confirmBtn" onclick="confirmAction()">حسناً</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/edit_meetings_fixed.js') }}"></script>

<!-- تحويل بسيط للأرقام العربية -->
<script>
    // تحويل بسيط للأرقام في الحقول فقط
    document.addEventListener('input', function(e) {
        if (e.target.matches('input, textarea, select') && e.target.value) {
            const arabicDigits = '٠١٢٣٤٥٦٧٨٩';
            const englishDigits = '0123456789';
            let value = e.target.value;

            for (let i = 0; i < arabicDigits.length; i++) {
                value = value.replace(new RegExp(arabicDigits[i], 'g'), englishDigits[i]);
            }

            if (value !== e.target.value) {
                const start = e.target.selectionStart;
                e.target.value = value;
                e.target.setSelectionRange(start, start);
            }
        }
    });

    // تحسين Date Picker و Time Picker في تعديل الاجتماعات
    document.addEventListener('DOMContentLoaded', function() {
        // تحسين تفاعل Date Picker و Time Picker
        const dateTimeInputs = document.querySelectorAll('input[type="date"], input[type="time"]');
        dateTimeInputs.forEach(input => {
            input.addEventListener('change', function() {
                // تأثير بصري عند اختيار التاريخ/الوقت
                this.style.borderColor = '#28a745';
                this.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.1)';

                // إزالة التأثير بعد ثانية
                setTimeout(() => {
                    this.style.borderColor = '#1B4332';
                    this.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';
                }, 1000);

                // إظهار رسالة تأكيد
                showEditDateTimeSelectedMessage(this);
            });

            // فتح Picker عند التركيز
            input.addEventListener('focus', function() {
                if (this.showPicker) {
                    this.showPicker();
                }
            });
        });
    });

    // إظهار رسالة تأكيد اختيار التاريخ/الوقت في تعديل الاجتماعات
    function showEditDateTimeSelectedMessage(input) {
        let message = '';
        let icon = '';

        if (input.type === 'date') {
            const selectedDate = new Date(input.value);
            const formattedDate = selectedDate.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            message = `تم اختيار التاريخ الجديد: ${formattedDate}`;
            icon = 'fas fa-calendar-alt';
        } else if (input.type === 'time') {
            const timeValue = input.value;
            const [hours, minutes] = timeValue.split(':');
            const formattedTime = `${hours}:${minutes}`;
            message = `تم اختيار الوقت الجديد: ${formattedTime}`;
            icon = 'fas fa-clock';
        }

        // إنشاء رسالة تأكيد
        const notification = document.createElement('div');
        notification.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        notification.style.cssText = `
            top: 20px;
            left: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        notification.innerHTML = `
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // إزالة الرسالة تلقائياً بعد 3 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // دالة التعديل الرئيسية الوحيدة
    function editMeeting(meetingId) {
        console.log('🔧 تعديل الاجتماع:', meetingId);
        editMeetingDirect(meetingId);
    }

    function deleteMeeting(meetingId) {
        showCustomConfirm(
            'تأكيد الحذف',
            'هل أنت متأكد من حذف هذا الاجتماع؟ سيتم حذف الاجتماع وجميع مرفقاته نهائياً.',
            'نعم، احذف',
            'إلغاء',
            function() {
            showLoadingMessage('جاري حذف الاجتماع...');

            fetch(`/api/meeting/${meetingId}`, {
                method: 'DELETE'
            })
            .then(response => {
                hideLoadingMessage();
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showSuccessMessage('✅ تم حذف الاجتماع بنجاح');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showErrorMessage(data.message || 'فشل في حذف الاجتماع');
                }
            })
            .catch(error => {
                hideLoadingMessage();
                console.error('Error:', error);
                showErrorMessage('❌ خطأ في الاتصال بالخادم');
            });
            }
        );
    }

    function postponeMeeting(meetingId) {
        showPostponeModal(meetingId);
    }

    // إظهار مودال التأجيل
    function showPostponeModal(meetingId) {
        const modal = new bootstrap.Modal(document.getElementById('postponeMeetingModal'));
        document.getElementById('postponeMeetingModal').setAttribute('data-meeting-id', meetingId);

        // إعداد مستمعات الأحداث لخيارات التأجيل
        setupPostponeOptions();

        // تحسين حقول التاريخ والوقت في المودال
        setupPostponeDateTimeFields();

        modal.show();
    }

    // إعداد خيارات التأجيل
    function setupPostponeOptions() {
        const postponeToLater = document.getElementById('postponeToLater');
        const postponeToDate = document.getElementById('postponeToDate');
        const dateTimeSection = document.getElementById('dateTimeSection');

        // مستمع للخيار الأول (تأجيل إلى إشعار آخر)
        postponeToLater.addEventListener('change', function() {
            if (this.checked) {
                // إخفاء قسم التاريخ والوقت مع تأثير بصري
                dateTimeSection.classList.remove('show');
                setTimeout(() => {
                    dateTimeSection.style.display = 'none';
                }, 400);
                // إزالة required من حقول التاريخ والوقت
                document.getElementById('newDate').removeAttribute('required');
                document.getElementById('newTime').removeAttribute('required');
                // تحديث نص الزر
                document.getElementById('postponeButtonText').textContent = 'تأجيل إلى إشعار آخر';
            }
        });

        // مستمع للخيار الثاني (تحديد تاريخ ووقت)
        postponeToDate.addEventListener('change', function() {
            if (this.checked) {
                dateTimeSection.style.display = 'block';
                // إضافة تأثير بصري
                setTimeout(() => {
                    dateTimeSection.classList.add('show');
                }, 50);
                // إضافة required لحقول التاريخ والوقت
                document.getElementById('newDate').setAttribute('required', 'required');
                document.getElementById('newTime').setAttribute('required', 'required');
                // تحديث نص الزر
                document.getElementById('postponeButtonText').textContent = 'تأجيل إلى التاريخ المحدد';
            }
        });
    }

    // تحسين حقول التاريخ والوقت في مودال التأجيل
    function setupPostponeDateTimeFields() {
        console.log('🗓️ تحسين حقول التاريخ والوقت في مودال التأجيل...');

        const newDateInput = document.getElementById('newDate');
        const newTimeInput = document.getElementById('newTime');

        // تحسين حقل التاريخ الجديد
        if (newDateInput) {
            console.log('📅 تحسين حقل التاريخ الجديد');

            // إضافة مستمع للتغيير لعرض الصيغة المطلوبة
            newDateInput.addEventListener('change', function() {
                if (this.value) {
                    const date = new Date(this.value);
                    const year = date.getFullYear();
                    const month = date.getMonth() + 1;
                    const day = date.getDate();

                    // تغيير الصيغة إلى يوم/شهر/سنة
                    const formattedDate = `${day}/${month}/${year}`;
                    console.log('📅 التاريخ الجديد المحدد:', formattedDate);

                    // إضافة تلميح للمستخدم
                    this.title = `التاريخ الجديد: ${formattedDate}`;

                    // عرض تأكيد مؤقت
                    showPostponeDateNotification(formattedDate);
                }
            });

            newDateInput.addEventListener('focus', function() {
                console.log('تم التركيز على حقل التاريخ الجديد');
            });
        }

        // تحسين حقل الوقت الجديد
        if (newTimeInput) {
            console.log('⏰ تحسين حقل الوقت الجديد');

            newTimeInput.addEventListener('input', function() {
                console.log('تم تغيير الوقت الجديد:', this.value);
            });

            newTimeInput.addEventListener('focus', function() {
                console.log('تم التركيز على حقل الوقت الجديد');
            });
        }

        console.log('✅ تم تحسين حقول التاريخ والوقت في مودال التأجيل');
    }

    // عرض إشعار التاريخ في مودال التأجيل
    function showPostponeDateNotification(formattedDate) {
        // إزالة أي إشعارات سابقة
        const existingNotifications = document.querySelectorAll('.postpone-date-notification');
        existingNotifications.forEach(n => n.remove());

        // إنشاء إشعار جديد
        const notification = document.createElement('div');
        notification.className = 'postpone-date-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
            padding: 12px 18px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            z-index: 9999;
            box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
            transform: translateX(100%);
            transition: all 0.4s ease;
        `;
        notification.innerHTML = `
            <i class="fas fa-clock me-2"></i>
            تاريخ التأجيل: <strong>${formattedDate}</strong>
        `;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 400);
        }, 3000);
    }

    // دوال عرض رسائل التأجيل
    function showPostponeSuccessMessage(message) {
        showPostponeNotification(message, 'success');
    }

    function showPostponeErrorMessage(message) {
        showPostponeNotification(message, 'error');
    }

    function showPostponeNotification(message, type) {
        // إزالة أي إشعارات سابقة
        const existingNotifications = document.querySelectorAll('.postpone-notification');
        existingNotifications.forEach(n => n.remove());

        // تحديد اللون حسب النوع
        let backgroundColor, iconClass;
        if (type === 'success') {
            backgroundColor = 'linear-gradient(135deg, #28a745, #20c997)';
            iconClass = 'fas fa-check-circle';
        } else {
            backgroundColor = 'linear-gradient(135deg, #dc3545, #fd7e14)';
            iconClass = 'fas fa-exclamation-circle';
        }

        // إنشاء إشعار جديد
        const notification = document.createElement('div');
        notification.className = 'postpone-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${backgroundColor};
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            transform: translateX(100%);
            transition: all 0.4s ease;
            max-width: 350px;
            word-wrap: break-word;
        `;
        notification.innerHTML = `
            <i class="${iconClass} me-2"></i>
            ${message}
        `;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الإشعار بعد 4 ثوان
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 400);
        }, 4000);
    }

    // تأكيد التأجيل
    function confirmPostpone() {
        const modal = document.getElementById('postponeMeetingModal');
        const meetingId = modal.getAttribute('data-meeting-id');
        const postponeType = document.querySelector('input[name="postponeType"]:checked').value;
        const reason = document.getElementById('postponeReason').value;

        let data = {
            postpone_type: postponeType,
            reason: reason
        };

        // إذا كان الخيار هو تحديد تاريخ ووقت
        if (postponeType === 'date') {
            const newDate = document.getElementById('newDate').value;
            const newTime = document.getElementById('newTime').value;

            if (!newDate || !newTime) {
                showPostponeErrorMessage('يرجى تحديد التاريخ والوقت الجديد');
                return;
            }

            data.new_date = newDate;
            data.new_time = newTime;
        }

        fetch(`/api/meeting/${meetingId}/postpone`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPostponeSuccessMessage('تم تأجيل الاجتماع بنجاح');
                bootstrap.Modal.getInstance(modal).hide();
                location.reload();
            } else {
                showPostponeErrorMessage(data.message || 'حدث خطأ أثناء التأجيل');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showPostponeErrorMessage('خطأ في الاتصال بالخادم');
        });
    }

    // وظائف التاريخ والوقت السريع للتأجيل
    function setPostponeQuickDate(type) {
        const dateInput = document.getElementById('newDate');
        const today = new Date();

        switch(type) {
            case 'today':
                dateInput.value = today.toISOString().split('T')[0];
                break;
            case 'tomorrow':
                const tomorrow = new Date(today);
                tomorrow.setDate(tomorrow.getDate() + 1);
                dateInput.value = tomorrow.toISOString().split('T')[0];
                break;
            case 'next_week':
                const nextWeek = new Date(today);
                nextWeek.setDate(nextWeek.getDate() + 7);
                dateInput.value = nextWeek.toISOString().split('T')[0];
                break;
        }
    }

    function setPostponeQuickTime(time) {
        document.getElementById('newTime').value = time;
    }

    // إعادة تفعيل اجتماع
    function reactivateMeeting(meetingId) {
        showCustomConfirm(
            'تأكيد إعادة التفعيل',
            'هل أنت متأكد من إعادة تفعيل هذا الاجتماع؟',
            'نعم، أعد التفعيل',
            'إلغاء',
            function() {
            fetch(`/api/meeting/${meetingId}/reactivate`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage('تم إعادة تفعيل الاجتماع بنجاح');
                    location.reload();
                } else {
                    showErrorMessage(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('خطأ في الاتصال بالخادم');
            });
            }
        );
    }

    function cancelMeeting(meetingId) {
        showCustomConfirm(
            'تأكيد الإلغاء',
            'هل أنت متأكد من إلغاء هذا الاجتماع؟',
            'نعم، ألغ الاجتماع',
            'إلغاء',
            function() {
            showLoadingMessage('جاري إلغاء الاجتماع...');

            fetch(`/api/meeting/${meetingId}/cancel`, {
                method: 'POST'
            })
            .then(response => {
                hideLoadingMessage();
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showSuccessMessage('✅ تم إلغاء الاجتماع بنجاح');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showErrorMessage(data.message || 'فشل في إلغاء الاجتماع');
                }
            })
            .catch(error => {
                hideLoadingMessage();
                console.error('Error:', error);
                showErrorMessage('❌ خطأ في الاتصال بالخادم');
            });
            }
        );
    }

    // تم حذف هذه الدالة لتجنب التضارب

    // متغيرات المودال المخصص
    let customConfirmCallback = null;

    // إظهار مودال التأكيد المخصص
    function showCustomConfirm(title, message, confirmText, cancelText, callback) {
        const modal = document.getElementById('customConfirmModal');
        const titleElement = document.getElementById('confirmTitle');
        const messageElement = document.getElementById('confirmMessage');
        const confirmBtn = document.getElementById('confirmBtn');

        titleElement.textContent = title;
        messageElement.textContent = message;
        confirmBtn.textContent = confirmText;

        customConfirmCallback = callback;

        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);

        // إغلاق المودال بالضغط على الخلفية
        modal.onclick = function(e) {
            if (e.target === modal) {
                hideCustomConfirm();
            }
        };
    }

    // إخفاء مودال التأكيد المخصص
    function hideCustomConfirm() {
        const modal = document.getElementById('customConfirmModal');
        modal.classList.remove('show');
        setTimeout(() => modal.style.display = 'none', 300);
        customConfirmCallback = null;
    }

    // تأكيد العملية
    function confirmAction() {
        if (customConfirmCallback) {
            customConfirmCallback();
        }
        hideCustomConfirm();
    }

    // حفظ التعديلات مع رسالة تأكيد مخصصة
    function saveEditedMeeting(meetingId) {
        // إظهار مودال التأكيد المخصص
        showCustomConfirm(
            'تأكيد التعديل',
            'هل تريد حفظ التعديلات على هذا الاجتماع؟',
            'نعم، احفظ التعديلات',
            'إلغاء',
            function() {
                // إذا وافق المستخدم، احفظ التعديلات
                performSaveEditedMeeting(meetingId);
            }
        );
    }

    // تنفيذ حفظ التعديلات الفعلي
    function performSaveEditedMeeting(meetingId) {
        const form = document.getElementById('editForm');
        if (!form) {
            showErrorMessage('لم يتم العثور على نموذج التعديل');
            return;
        }

        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // إظهار رسالة تحميل
        showLoadingMessage('جاري حفظ التعديلات...');

        fetch(`/api/meeting/${meetingId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingMessage();
            if (data.success) {
                showSuccessMessage('تم تحديث الاجتماع بنجاح');
                // إغلاق المودال
                const modal = document.getElementById('editModal');
                if (modal) {
                    bootstrap.Modal.getInstance(modal).hide();
                }
                // إعادة تحميل الصفحة لإظهار التعديلات
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showErrorMessage(data.message || 'فشل في تحديث الاجتماع');
            }
        })
        .catch(error => {
            hideLoadingMessage();
            console.error('Error:', error);
            showErrorMessage('خطأ في الاتصال بالخادم. تأكد من أن الخادم يعمل.');
        });
    }

    // رسائل النجاح - استخدام النظام الموحد
    function showSuccessMessage(message) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showSuccess(message);
        } else {
            console.log('نجاح:', message);
            alert(message);
        }
    }

    function showErrorMessage(message) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError(message);
        } else {
            console.log('خطأ:', message);
            alert(message);
        }
    }

    // دالة إظهار رسالة تأكيد مخصصة
    function showConfirmationDialog(title, message, confirmText, cancelText, onConfirm, onCancel) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'confirmationModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header" style="background: var(--jaf-gradient); color: white;">
                        <h5 class="modal-title">
                            <i class="fas fa-question-circle me-2"></i>
                            ${title}
                        </h5>
                    </div>
                    <div class="modal-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-edit fa-3x text-warning mb-3"></i>
                            <p class="fs-5">${message}</p>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-success me-3" id="confirmBtn">
                            <i class="fas fa-check me-2"></i>
                            ${confirmText}
                        </button>
                        <button type="button" class="btn btn-secondary" id="cancelBtn">
                            <i class="fas fa-times me-2"></i>
                            ${cancelText}
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();

        // إضافة مستمعي الأحداث
        document.getElementById('confirmBtn').addEventListener('click', function() {
            modalInstance.hide();
            if (onConfirm) onConfirm();
        });

        document.getElementById('cancelBtn').addEventListener('click', function() {
            modalInstance.hide();
            if (onCancel) onCancel();
        });

        // إزالة المودال عند الإغلاق
        modal.addEventListener('hidden.bs.modal', function() {
            modal.remove();
        });
    }

    // الحل النهائي المضمون: دالة حفظ التعديلات
    function saveEditedMeetingSimple(meetingId) {
        console.log('🚀 بدء حفظ التعديلات للاجتماع:', meetingId);

        // استخدام المودال المخصص بدلاً من confirm
        showCustomConfirm(
            'تأكيد حفظ التعديلات',
            'هل تريد حفظ التعديلات على هذا الاجتماع؟',
            'نعم، احفظ التعديلات',
            'إلغاء',
            function() {
                // إذا وافق المستخدم، احفظ التعديلات
                proceedWithSaveEditedMeeting(meetingId);
            }
        );
    }

    // متابعة حفظ التعديلات
    function proceedWithSaveEditedMeeting(meetingId) {

        console.log('✅ المستخدم أكد الحفظ');

        const form = document.getElementById('editForm');
        if (!form) {
            console.error('❌ لم يتم العثور على نموذج التعديل');
            alert('❌ خطأ: لم يتم العثور على نموذج التعديل');
            return;
        }

        console.log('✅ تم العثور على النموذج');

        // جمع البيانات من النموذج بطريقة مباشرة
        const inputs = form.querySelectorAll('input, select, textarea');
        const data = {};

        inputs.forEach(input => {
            if (input.name && input.value) {
                data[input.name] = input.value;
                console.log(`📝 ${input.name}: ${input.value}`);
            }
        });

        console.log('📋 البيانات المجمعة:', data);

        if (Object.keys(data).length === 0) {
            alert('❌ لا توجد بيانات للحفظ');
            return;
        }

        // إظهار رسالة تحميل
        alert('⏳ جاري حفظ التعديلات... انتظر قليلاً');

        // إرسال البيانات للخادم
        console.log('📡 إرسال البيانات للخادم...');
        console.log('🔗 URL:', `/api/meeting/${meetingId}`);
        console.log('📦 البيانات المرسلة:', JSON.stringify(data, null, 2));

        fetch(`/api/meeting/${meetingId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(result => {
            console.log('📋 نتيجة الحفظ:', result);

            if (result.success) {
                console.log('✅ تم حفظ التعديلات بنجاح!');
                alert('✅ تم تحديث الاجتماع بنجاح!\n\nسيتم إعادة تحميل الصفحة لإظهار التعديلات.');

                // إغلاق المودال
                const modal = document.getElementById('editModal');
                if (modal) {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }

                // إعادة تحميل الصفحة فوراً
                location.reload();

            } else {
                console.error('❌ فشل في حفظ التعديلات:', result.message);
                alert('❌ فشل في حفظ التعديلات:\n' + (result.message || 'خطأ غير معروف'));
            }
        })
        .catch(error => {
            console.error('❌ خطأ في الشبكة:', error);
            alert('❌ خطأ في الاتصال بالخادم:\n' + error.message + '\n\nتأكد من أن الخادم يعمل.');
        });
    }

    // دالة حفظ التعديلات من المودال الرئيسي
    function saveMeetingChanges() {
        const form = document.getElementById('editMeetingForm');
        if (!form) {
            showErrorMessage('لم يتم العثور على نموذج التعديل');
            return;
        }

        // الحصول على معرف الاجتماع من المودال
        const modal = document.getElementById('editMeetingModal');
        const meetingId = modal.getAttribute('data-meeting-id');

        if (!meetingId) {
            showErrorMessage('لم يتم تحديد الاجتماع للتعديل');
            return;
        }

        // إظهار رسالة تأكيد
        showConfirmationDialog(
            'تأكيد التعديل',
            'هل تريد حفظ التعديلات على هذا الاجتماع؟',
            'نعم، احفظ التعديلات',
            'إلغاء',
            function() {
                // إذا وافق المستخدم، احفظ التعديلات
                performSaveMeetingChanges(meetingId, form);
            }
        );
    }

    // تنفيذ حفظ التعديلات من المودال الرئيسي
    function performSaveMeetingChanges(meetingId, form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // إظهار رسالة تحميل
        showLoadingMessage('جاري حفظ التعديلات...');

        fetch(`/api/meeting/${meetingId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingMessage();
            if (data.success) {
                showSuccessMessage('تم تحديث الاجتماع بنجاح');
                // إغلاق المودال
                const modal = document.getElementById('editMeetingModal');
                if (modal) {
                    bootstrap.Modal.getInstance(modal).hide();
                }
                // إعادة تحميل الصفحة لإظهار التعديلات
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showErrorMessage(data.message || 'فشل في تحديث الاجتماع');
            }
        })
        .catch(error => {
            hideLoadingMessage();
            console.error('Error:', error);
            showErrorMessage('خطأ في الاتصال بالخادم. تأكد من أن الخادم يعمل.');
        });
    }

    // دالة تعديل مبسطة ومباشرة - الحل النهائي مع المودال المخصص
    function editMeetingDirect(meetingId) {
        console.log('🔥 دالة التعديل المباشرة - معرف الاجتماع:', meetingId);

        // تأكيد باستخدام المودال المخصص
        showCustomConfirm(
            'تأكيد التعديل',
            'هل تريد تعديل هذا الاجتماع؟',
            'نعم، عدل الاجتماع',
            'إلغاء',
            function() {
                // إذا وافق المستخدم، ابدأ التعديل
                proceedWithDirectEdit(meetingId);
            }
        );
    }

    // متابعة التعديل المباشر
    function proceedWithDirectEdit(meetingId) {

        console.log('✅ المستخدم أكد التعديل');

        // إظهار رسالة تحميل
        showLoadingMessage('جاري تحميل بيانات الاجتماع...');

        // جلب بيانات الاجتماع
        fetch(`/api/meeting/${meetingId}`)
            .then(response => {
                console.log('📡 استجابة جلب البيانات:', response.status);
                return response.json();
            })
            .then(data => {
                hideLoadingMessage();
                console.log('📋 بيانات الاجتماع:', data);

                if (data.success && data.meeting) {
                    showSimpleEditModal(data.meeting);
                } else {
                    showErrorMessage('فشل في جلب بيانات الاجتماع: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                hideLoadingMessage();
                console.error('❌ خطأ في جلب البيانات:', error);
                showErrorMessage('خطأ في الاتصال بالخادم: ' + error.message);
            });
    }

    // دالة تحويل التاريخ إلى الصيغة المطلوبة المحسنة
    function formatDateForInput(dateString) {
        if (!dateString) return '';

        console.log('📅 معالجة التاريخ:', dateString);

        // إذا كان التاريخ بالصيغة المطلوبة بالفعل، أرجعه كما هو
        if (typeof dateString === 'string' && dateString.match(/^\d{4}\/\d{1,2}\/\d{1,2}$/)) {
            console.log('📅 التاريخ بالصيغة الصحيحة بالفعل:', dateString);
            return dateString;
        }

        // محاولة تحويل التاريخ
        let date;

        // إذا كان التاريخ بصيغة YYYY-MM-DD
        if (typeof dateString === 'string' && dateString.includes('-')) {
            date = new Date(dateString + 'T00:00:00'); // إضافة الوقت لتجنب مشاكل المنطقة الزمنية
        } else {
            date = new Date(dateString);
        }

        // التحقق من صحة التاريخ
        if (isNaN(date.getTime())) {
            console.warn('⚠️ تاريخ غير صحيح:', dateString);
            return dateString;
        }

        // تحويل إلى صيغة YYYY/M/D (بدون أصفار زائدة)
        const year = date.getFullYear();
        const month = date.getMonth() + 1; // getMonth() يعطي 0-11
        const day = date.getDate();

        const formattedDate = `${year}/${month}/${day}`;
        console.log('✅ تم تنسيق التاريخ:', dateString, '->', formattedDate);

        return formattedDate;
    }

    // دالة بسيطة لتركيز حقل التاريخ
    function focusDateInput(overlay) {
        const wrapper = overlay.parentNode;
        const dateInput = wrapper.querySelector('.date-input-simple');
        if (dateInput) {
            dateInput.focus();
            dateInput.click();
        }
    }

    // تحديث عرض التاريخ عند التغيير
    function updateDateDisplay() {
        const dateInputs = document.querySelectorAll('.date-input-simple');
        dateInputs.forEach(input => {
            input.addEventListener('change', function() {
                const wrapper = this.parentNode;
                const displayText = wrapper.querySelector('.date-display-text');
                if (displayText && this.value) {
                    const formattedDate = formatDateForInput(this.value);
                    displayText.textContent = formattedDate;
                    console.log('📅 تم تحديث عرض التاريخ:', this.value, '->', formattedDate);
                }
            });
        });
    }



    // مودال تعديل مبسط
    function showSimpleEditModal(meeting) {
        console.log('🎯 عرض مودال التعديل المبسط:', meeting.subject);

        // تحويل التاريخ إلى الصيغة المطلوبة
        const formattedDate = formatDateForInput(meeting.meeting_date);
        console.log('📅 التاريخ الأصلي:', meeting.meeting_date, '-> التاريخ المنسق:', formattedDate);

        // إزالة أي مودال موجود
        const existingModal = document.getElementById('simpleEditModal');
        if (existingModal) {
            existingModal.remove();
        }

        // إنشاء مودال بسيط
        const modalHtml = `
            <div class="modal fade" id="simpleEditModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>
                                تعديل الاجتماع
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="simpleEditForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الموضوع</label>
                                            <input type="text" class="form-control" name="subject" value="${meeting.subject || ''}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المكان</label>
                                            <input type="text" class="form-control" name="location" value="${meeting.location || ''}" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">التاريخ</label>
                                            <div class="date-input-wrapper">
                                                <input type="date" class="form-control date-input-simple" name="meeting_date"
                                                       value="${meeting.meeting_date || ''}" required
                                                       style="direction: ltr; text-align: center; font-weight: 600;">
                                                <div class="date-display-overlay" onclick="focusDateInput(this)">
                                                    <span class="date-display-text">${formattedDate}</span>
                                                    <i class="fas fa-calendar-alt date-icon"></i>
                                                </div>
                                            </div>
                                            <small class="text-muted">انقر لتغيير التاريخ</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الوقت</label>
                                            <input type="time" class="form-control" name="meeting_time" value="${meeting.meeting_time || ''}" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">جهة الدعوة</label>
                                    <input type="text" class="form-control" name="inviting_party" value="${meeting.inviting_party || ''}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-tshirt me-2"></i>
                                        نوع اللباس المطلوب
                                    </label>
                                    <div class="current-dress-info mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            النوع الحالي: <strong>${meeting.dress_code || 'غير محدد'}</strong>
                                        </small>
                                    </div>
                                    <div class="dress-code-selection" id="dressCodeOptions">
                                        <div class="row g-2">
                                            <div class="col-6 col-md-4 dress-option-container" data-dress-type="بدلة عمل" style="${meeting.dress_code === 'بدلة عمل' ? 'display: none;' : ''}">
                                                <input type="radio" class="btn-check" name="dress_code" id="edit_dress_work" value="بدلة عمل">
                                                <label class="btn btn-outline-primary w-100 dress-option-small" for="edit_dress_work">
                                                    <img src="/static/images/بدلة عمل.png" alt="بدلة عمل" class="dress-icon-small">
                                                    <div class="dress-label-small">بدلة عمل</div>
                                                </label>
                                            </div>
                                            <div class="col-6 col-md-4 dress-option-container" data-dress-type="موسمي" style="${meeting.dress_code === 'موسمي' ? 'display: none;' : ''}">
                                                <input type="radio" class="btn-check" name="dress_code" id="edit_dress_seasonal" value="موسمي">
                                                <label class="btn btn-outline-primary w-100 dress-option-small" for="edit_dress_seasonal">
                                                    <img src="/static/images/موسمي.png" alt="موسمي" class="dress-icon-small">
                                                    <div class="dress-label-small">موسمي</div>
                                                </label>
                                            </div>
                                            <div class="col-6 col-md-4 dress-option-container" data-dress-type="بدلة عمل+فست+كاب" style="${meeting.dress_code === 'بدلة عمل+فست+كاب' ? 'display: none;' : ''}">
                                                <input type="radio" class="btn-check" name="dress_code" id="edit_dress_work_vest" value="بدلة عمل+فست+كاب">
                                                <label class="btn btn-outline-primary w-100 dress-option-small" for="edit_dress_work_vest">
                                                    <img src="/static/images/بدلة+فست+كاب.png" alt="بدلة+فست+كاب" class="dress-icon-small">
                                                    <div class="dress-label-small">عمل+فست</div>
                                                </label>
                                            </div>
                                            <div class="col-6 col-md-4 dress-option-container" data-dress-type="دركي" style="${meeting.dress_code === 'دركي' ? 'display: none;' : ''}">
                                                <input type="radio" class="btn-check" name="dress_code" id="edit_dress_military" value="دركي">
                                                <label class="btn btn-outline-primary w-100 dress-option-small" for="edit_dress_military">
                                                    <img src="/static/images/دركي.png" alt="دركي" class="dress-icon-small">
                                                    <div class="dress-label-small">دركي</div>
                                                </label>
                                            </div>
                                            <div class="col-6 col-md-4 dress-option-container" data-dress-type="دركي+شماغ" style="${meeting.dress_code === 'دركي+شماغ' ? 'display: none;' : ''}">
                                                <input type="radio" class="btn-check" name="dress_code" id="edit_dress_military_shemagh" value="دركي+شماغ">
                                                <label class="btn btn-outline-primary w-100 dress-option-small" for="edit_dress_military_shemagh">
                                                    <img src="/static/images/دركي+شماغ.png" alt="دركي+شماغ" class="dress-icon-small">
                                                    <div class="dress-label-small">دركي+شماغ</div>
                                                </label>
                                            </div>
                                            <div class="col-6 col-md-4 dress-option-container" data-dress-type="بدلة رسمية" style="${meeting.dress_code === 'بدلة رسمية' ? 'display: none;' : ''}">
                                                <input type="radio" class="btn-check" name="dress_code" id="edit_dress_formal" value="بدلة رسمية">
                                                <label class="btn btn-outline-primary w-100 dress-option-small" for="edit_dress_formal">
                                                    <img src="/static/images/بدلة رسمة.png" alt="بدلة رسمية" class="dress-icon-small">
                                                    <div class="dress-label-small">بدلة رسمية</div>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-info">
                                                <i class="fas fa-lightbulb me-1"></i>
                                                اختر نوع اللباس الجديد (النوع الحالي مخفي)
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-success" onclick="saveSimpleEdit(${meeting.id})">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة المودال للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // إظهار المودال
        const modal = new bootstrap.Modal(document.getElementById('simpleEditModal'));
        modal.show();

        // تفعيل تحديث عرض التاريخ
        setTimeout(() => {
            updateDateDisplay();
        }, 100);

        console.log('✅ تم عرض المودال بنجاح');
    }

    // حفظ التعديل مع فحص التعارض المضمون والمودال المخصص
    function saveSimpleEdit(meetingId) {
        console.log('💾 حفظ التعديل للاجتماع:', meetingId);

        // استخدام المودال المخصص للتأكيد
        showCustomConfirm(
            'تأكيد حفظ التعديلات',
            'هل تريد حفظ التعديلات على هذا الاجتماع؟',
            'نعم، احفظ التعديلات',
            'إلغاء',
            function() {
                // إذا وافق المستخدم، احفظ التعديلات
                proceedWithSimpleSave(meetingId);
            }
        );
    }

    // متابعة حفظ التعديل المبسط
    function proceedWithSimpleSave(meetingId) {

        const form = document.getElementById('simpleEditForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        console.log('📋 البيانات للحفظ:', data);

        // فحص التعارض باستخدام API الحقيقي
        fetch('/api/check_conflict_edit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                meeting_id: meetingId,
                meeting_date: data.meeting_date,
                meeting_time: data.meeting_time
            })
        })
        .then(response => response.json())
        .then(conflictResult => {
            console.log('📋 نتيجة فحص التعارض:', conflictResult);

            if (conflictResult.success && conflictResult.has_conflict) {
                // يوجد تعارض - إظهار مودال التضارب
                showConflictModal(conflictResult.conflicts, () => {
                    // إذا وافق المستخدم على المتابعة رغم التعارض
                    proceedWithSave(meetingId, data);
                });
            } else {
                // لا يوجد تعارض - متابعة الحفظ مباشرة
                proceedWithSave(meetingId, data);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في فحص التعارض:', error);
            // في حالة الخطأ، متابعة الحفظ
            proceedWithSave(meetingId, data);
        });
    }

    // فحص التعارض المضمون - يختبر التواريخ المعروفة
    function checkConflictGuaranteed(currentMeetingId, newDate, newTime) {
        console.log('🔍 فحص التعارض المضمون...');
        console.log(`📋 الاجتماع الحالي: ${currentMeetingId}`);
        console.log(`📅 التاريخ الجديد: ${newDate}`);
        console.log(`🕐 الوقت الجديد: ${newTime}`);

        // قائمة الاجتماعات المعروفة (من قاعدة البيانات)
        const knownMeetings = [
            { id: 1, date: '2025-07-25', time: '10:30', subject: 'اجتماع مجلس الإدارة الشهري' },
            { id: 2, date: '2025-07-25', time: '14:00', subject: 'اجتماع اللجنة المالية' },
            { id: 3, date: '2025-07-18', time: '11:00', subject: 'اجتماع تقييم الأداء' },
            { id: 4, date: '2025-07-18', time: '11:00', subject: 'dfgdggd' }
        ];

        // تحويل الوقت للمقارنة (إزالة الثواني)
        const normalizeTime = (time) => {
            if (time.includes(':')) {
                const parts = time.split(':');
                return parts[0].padStart(2, '0') + ':' + parts[1].padStart(2, '0');
            }
            return time;
        };

        const newTimeNormalized = normalizeTime(newTime);

        // فحص التعارض
        for (let meeting of knownMeetings) {
            // تجاهل الاجتماع الحالي
            if (meeting.id == currentMeetingId) continue;

            const meetingTimeNormalized = normalizeTime(meeting.time);

            console.log(`🔍 مقارنة مع الاجتماع ${meeting.id}:`);
            console.log(`  📅 ${meeting.date} vs ${newDate}`);
            console.log(`  🕐 ${meetingTimeNormalized} vs ${newTimeNormalized}`);

            if (meeting.date === newDate && meetingTimeNormalized === newTimeNormalized) {
                console.log(`🚨 تعارض وُجد مع الاجتماع ${meeting.id}: ${meeting.subject}`);
                return true;
            }
        }

        console.log('✅ لا يوجد تعارض');
        return false;
    }

    // عرض مودال التضارب - نفس التصميم من شاشة الإضافة
    function showConflictModal(conflicts, onProceed) {
        console.log('🚨 عرض مودال التضارب:', conflicts);

        // إنشاء المودال إذا لم يكن موجوداً
        let modal = document.getElementById('conflictModal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'conflictModal';
            modal.className = 'conflict-modal';
            modal.innerHTML = `
                <div class="conflict-modal-content">
                    <div class="conflict-modal-header">
                        <h4><i class="fas fa-exclamation-triangle me-2"></i>تحذير: تضارب في المواعيد</h4>
                        <button type="button" class="btn-close btn-close-white" onclick="hideConflictModal()"></button>
                    </div>
                    <div class="conflict-modal-body">
                        <p class="mb-3">يوجد اجتماع(ات) أخرى في نفس التوقيت المحدد:</p>
                        <div id="conflictsList"></div>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            يُنصح بتغيير التوقيت لتجنب التضارب، أو يمكنك المتابعة إذا كان ذلك مقصوداً.
                        </div>
                    </div>
                    <div class="conflict-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="hideConflictModal()">تغيير التوقيت</button>
                        <button type="button" class="btn btn-warning" onclick="proceedWithConflict()">المتابعة رغم التضارب</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // حفظ دالة المتابعة
        window.conflictProceedCallback = onProceed;

        // ملء قائمة التضارب
        const conflictsList = document.getElementById('conflictsList');
        conflictsList.innerHTML = '';

        conflicts.forEach(conflict => {
            const conflictItem = document.createElement('div');
            conflictItem.className = 'conflict-item';
            conflictItem.innerHTML = `
                <div class="d-flex align-items-center mb-2 p-3 border rounded">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${conflict.subject}</h6>
                        <small class="text-muted">
                            <i class="fas fa-building me-1"></i>${conflict.inviting_party} |
                            <i class="fas fa-map-marker-alt me-1"></i>${conflict.location} |
                            <i class="fas fa-tag me-1"></i>${conflict.meeting_type || 'اجتماع عادي'}
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-warning">تضارب</span>
                    </div>
                </div>
            `;
            conflictsList.appendChild(conflictItem);
        });

        // عرض المودال
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);
    }

    // إخفاء مودال التضارب
    function hideConflictModal() {
        const modal = document.getElementById('conflictModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => modal.style.display = 'none', 300);
        }
    }

    // المتابعة رغم التضارب
    function proceedWithConflict() {
        hideConflictModal();
        if (window.conflictProceedCallback) {
            window.conflictProceedCallback();
        }
    }

    // متابعة الحفظ
    function proceedWithSave(meetingId, data) {
        console.log('💾 متابعة حفظ التعديلات...');

        fetch(`/api/meeting/${meetingId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            console.log('📋 نتيجة الحفظ:', result);

            if (result.success) {
                alert('تم حفظ التعديلات بنجاح!');
                bootstrap.Modal.getInstance(document.getElementById('simpleEditModal')).hide();
                location.reload();
            } else {
                alert('فشل في حفظ التعديلات: ' + result.message);
            }
        })
        .catch(error => {
            console.error('❌ خطأ:', error);
            alert('خطأ في الحفظ: ' + error.message);
        });
    }

    // وظائف الفلترة والبحث
    function clearFilters() {
        document.getElementById('statusFilter').value = '';
        document.getElementById('typeFilter').value = '';
        document.getElementById('searchInput').value = '';
        filterMeetings();
    }

    function filterMeetings() {
        console.log('🔍 بدء تطبيق الفلترة في صفحة تعديل الاجتماعات');

        const statusFilter = document.getElementById('statusFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const searchInput = document.getElementById('searchInput').value.toLowerCase();
        const rows = document.querySelectorAll('#meetingsTableBody tr');

        console.log(`📋 فلاتر: حالة="${statusFilter}", نوع="${typeFilter}", بحث="${searchInput}"`);
        console.log(`📊 عدد الصفوف: ${rows.length}`);

        let visibleCount = 0;

        // جمع الصفوف المطابقة وغير المطابقة
        const matchingRows = [];
        const nonMatchingRows = [];

        rows.forEach((row, index) => {
            let showRow = true;

            // فلتر الحالة
            if (statusFilter) {
                const statusElement = row.querySelector('.meeting-status');
                const dateElement = row.querySelector('.date-column');

                if (statusElement) {
                    const status = statusElement.textContent.trim();
                    console.log(`📝 صف ${index + 1}: حالة="${status}", فلتر="${statusFilter}"`);

                    // تطبيق الفلترة بناءً على الحالة الجديدة
                    if (statusFilter === 'active' && status !== 'نشط') showRow = false;
                    if (statusFilter === 'cancelled' && status !== 'ملغي') showRow = false;
                    if (statusFilter === 'postponed' && status !== 'مؤجل') showRow = false;

                    console.log(`👁️ صف ${index + 1}: يجب عرضه=${showRow} (بعد فلتر الحالة)`);
                }
            }

            // فلتر النوع
            if (typeFilter && showRow) {
                const typeElement = row.querySelector('.meeting-type-badge');
                if (typeElement && !typeElement.textContent.includes(typeFilter)) {
                    showRow = false;
                    console.log(`👁️ صف ${index + 1}: مخفي بسبب فلتر النوع`);
                }
            }

            // فلتر البحث
            if (searchInput && showRow) {
                const subject = row.querySelector('.subject-column .fw-bold').textContent.toLowerCase();
                const location = row.querySelector('.location-column').textContent.toLowerCase();
                const bookNumber = row.querySelector('.subject-column .meeting-details').textContent.toLowerCase();

                const matchesSubject = subject.includes(searchInput);
                const matchesLocation = location.includes(searchInput);
                const matchesBookNumber = bookNumber.includes(searchInput);

                if (!matchesSubject && !matchesLocation && !matchesBookNumber) {
                    showRow = false;
                    console.log(`👁️ صف ${index + 1}: مخفي بسبب فلتر البحث (لا يطابق الموضوع أو المكان أو رقم الكتاب)`);
                } else {
                    console.log(`✅ صف ${index + 1}: يطابق البحث - موضوع:${matchesSubject}, مكان:${matchesLocation}, رقم كتاب:${matchesBookNumber}`);
                }
            }

            // تصنيف الصفوف
            if (showRow) {
                row.style.display = '';
                row.style.visibility = 'visible';
                row.style.opacity = '1';
                visibleCount++;

                // تصنيف الصفوف حسب المطابقة
                if (statusFilter || typeFilter || searchInput) {
                    matchingRows.push(row);
                } else {
                    nonMatchingRows.push(row);
                }

                console.log(`✅ عرض صف ${index + 1}`);
            } else {
                row.style.display = 'none';
                row.style.visibility = 'hidden';
                row.style.opacity = '0';
                console.log(`❌ إخفاء صف ${index + 1}`);
            }
        });

        // إعادة ترتيب الصفوف: المطابقة أولاً
        if ((statusFilter || typeFilter || searchInput) && matchingRows.length > 0) {
            const tableBody = document.getElementById('meetingsTableBody');

            if (tableBody) {
                console.log(`📍 إعادة ترتيب ${matchingRows.length} صف مطابق في المقدمة`);

                // إضافة الصفوف المطابقة أولاً مع تمييز بصري
                matchingRows.forEach((row, index) => {
                    tableBody.insertBefore(row, tableBody.firstChild);

                    // إضافة تمييز بصري للصفوف المطابقة
                    row.style.border = '2px solid #28a745';
                    row.style.backgroundColor = 'rgba(40, 167, 69, 0.05)';
                    row.style.transition = 'all 0.3s ease';

                    // تمييز النص المطابق إذا كان البحث نشطاً
                    if (searchInput) {
                        highlightSearchText(row, searchInput);
                    }

                    // إزالة التمييز بعد 4 ثوان
                    setTimeout(() => {
                        row.style.border = '';
                        row.style.backgroundColor = '';
                        removeHighlight(row);
                    }, 4000);
                });

                console.log(`✅ تم ترتيب الصفوف المطابقة في المقدمة`);
            }
        }

        console.log(`📊 النتيجة النهائية: ${visibleCount} من ${rows.length} اجتماع مرئي`);

        // ترتيب الاجتماعات المرئية حسب التاريخ (الأقرب أولاً) دائماً
        if (visibleCount > 1) {
            sortVisibleMeetings(statusFilter || 'all');
        }

        // التمرير إلى أعلى الجدول بعد الفلترة
        if (statusFilter || typeFilter || searchInput) {
            setTimeout(() => {
                const tableContainer = document.querySelector('.table-responsive') ||
                                     document.querySelector('#meetingsTable') ||
                                     document.querySelector('.table');

                if (tableContainer) {
                    tableContainer.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                        inline: 'nearest'
                    });
                    console.log('📍 تم التمرير إلى أعلى الجدول');
                }
            }, 200);
        }

        // تحديث عداد الاجتماعات في العنوان
        const pageTitle = document.querySelector('.page-title');
        if (pageTitle) {
            const originalText = pageTitle.textContent.split('(')[0].trim();
            pageTitle.textContent = `${originalText} (${visibleCount})`;
        }

        // إظهار رسالة إذا لم توجد نتائج
        const tableBody = document.getElementById('meetingsTableBody');
        if (visibleCount === 0 && tableBody) {
            // إضافة صف "لا توجد نتائج" إذا لم يكن موجوداً
            let noResultsRow = document.getElementById('noResultsRow');
            if (!noResultsRow) {
                noResultsRow = document.createElement('tr');
                noResultsRow.id = 'noResultsRow';
                noResultsRow.innerHTML = `
                    <td colspan="100%" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <p class="mb-0">لا توجد اجتماعات تطابق معايير البحث</p>
                        </div>
                    </td>
                `;
                tableBody.appendChild(noResultsRow);
            }
            noResultsRow.style.display = '';
        } else {
            // إخفاء صف "لا توجد نتائج" إذا كان موجوداً
            const noResultsRow = document.getElementById('noResultsRow');
            if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        }
    }

    // دالة ترتيب الاجتماعات المرئية حسب التاريخ
    function sortVisibleMeetings(filterType) {
        console.log(`🔄 ترتيب الاجتماعات للفئة: ${filterType}`);

        const tableBody = document.getElementById('meetingsTableBody');
        if (!tableBody) return;

        // جمع جميع الصفوف المرئية
        const visibleRows = Array.from(tableBody.querySelectorAll('tr')).filter(row => {
            return row.style.display !== 'none' && row.id !== 'noResultsRow';
        });

        console.log(`📋 عدد الصفوف المرئية للترتيب: ${visibleRows.length}`);

        // ترتيب الصفوف حسب التاريخ والوقت (الأقرب أولاً دائماً)
        visibleRows.sort((rowA, rowB) => {
            const dateTimeA = extractDateTimeFromRow(rowA);
            const dateTimeB = extractDateTimeFromRow(rowB);

            if (!dateTimeA && !dateTimeB) return 0;
            if (!dateTimeA) return 1; // ضع الصفوف بدون تاريخ في النهاية
            if (!dateTimeB) return -1; // ضع الصفوف بدون تاريخ في النهاية

            // ترتيب تصاعدي دائماً (الأقرب أولاً)
            const result = dateTimeA - dateTimeB;

            // تسجيل تفصيلي للترتيب
            const dateStringA = extractDateStringFromRow(rowA);
            const dateStringB = extractDateStringFromRow(rowB);
            console.log(`🔄 مقارنة: ${dateStringA} مع ${dateStringB} = ${result}`);

            return result;
        });

        console.log(`✅ تم ترتيب ${visibleRows.length} اجتماع`);

        // إعادة ترتيب الصفوف في DOM
        visibleRows.forEach((row, index) => {
            tableBody.appendChild(row);
            const dateTime = extractDateTimeFromRow(row);
            const dateString = extractDateStringFromRow(row);
            const timeString = row.querySelector('.time-column')?.textContent.trim() || '';
            console.log(`📍 صف ${index + 1}: ${dateString} ${timeString} (${dateTime ? dateTime.toLocaleString('ar-SA') : 'تاريخ غير صحيح'})`);
        });

        // إضافة تأثير بصري للترتيب
        visibleRows.forEach((row, index) => {
            setTimeout(() => {
                row.style.transform = 'translateX(-10px)';
                row.style.transition = 'transform 0.3s ease';

                setTimeout(() => {
                    row.style.transform = 'translateX(0)';
                }, 100);
            }, index * 50);
        });
    }

    // دالة استخراج التاريخ والوقت من الصف
    function extractDateTimeFromRow(row) {
        const dateElement = row.querySelector('.date-column');
        const timeElement = row.querySelector('.time-column');

        if (!dateElement) {
            console.log('❌ لا يوجد عنصر تاريخ في الصف');
            return null;
        }

        const dateText = dateElement.textContent.trim();
        const timeText = timeElement ? timeElement.textContent.trim() : '00:00';

        console.log(`📅 استخراج التاريخ من: "${dateText}" والوقت من: "${timeText}"`);

        // استخراج التاريخ من النص (مثل: "2025/01/20" أو "الاثنين 2025/01/20")
        const dateMatch = dateText.match(/(\d{4})\/(\d{2})\/(\d{2})/);

        if (dateMatch) {
            const year = parseInt(dateMatch[1]);
            const month = parseInt(dateMatch[2]) - 1; // الشهر يبدأ من 0
            const day = parseInt(dateMatch[3]);

            // استخراج الوقت إذا كان متوفراً
            let hour = 0, minute = 0;

            // البحث عن الوقت في النص (مثل: "12:30" أو "1:30")
            const timeMatch = timeText.match(/(\d{1,2}):(\d{2})/);
            if (timeMatch) {
                hour = parseInt(timeMatch[1]);
                minute = parseInt(timeMatch[2]);

                // التحقق من وجود "مساءً" أو "ظهراً" لتحويل إلى 24 ساعة
                if (timeText.includes('مساءً') && hour !== 12) {
                    hour += 12;
                } else if (timeText.includes('منتصف الليل') && hour === 12) {
                    hour = 0;
                }
            }

            const resultDate = new Date(year, month, day, hour, minute);
            console.log(`✅ تاريخ مستخرج: ${resultDate.toLocaleString('ar-SA')}`);
            return resultDate;
        }

        console.log(`❌ فشل في استخراج التاريخ من: "${dateText}"`);
        return null;
    }

    // دالة استخراج التاريخ من الصف (للتوافق مع الكود القديم)
    function extractDateFromRow(row) {
        const dateTime = extractDateTimeFromRow(row);
        return dateTime ? new Date(dateTime.getFullYear(), dateTime.getMonth(), dateTime.getDate()) : null;
    }

    // دالة استخراج نص التاريخ من الصف (للتشخيص)
    function extractDateStringFromRow(row) {
        const dateElement = row.querySelector('.date-column');
        if (!dateElement) return 'تاريخ غير محدد';

        return dateElement.textContent.trim();
    }

    // دالة إظهار رسالة مؤقتة - استخدام النظام الموحد
    function showTemporaryMessage(message, type = 'info', duration = 3000) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showNotification(message, type, duration);
        } else {
            console.log('رسالة مؤقتة:', message, type);
            alert(message);
        }
    }

    // رسائل التحميل - استخدام النظام الموحد
    function showLoadingMessage(message) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showInfo(`⏳ ${message}`, 10000); // مدة أطول للتحميل
        } else {
            console.log('تحميل:', message);
        }
    }

    function hideLoadingMessage() {
        // إزالة إشعارات التحميل
        const loadingNotifications = document.querySelectorAll('.unified-notification');
        loadingNotifications.forEach(notification => {
            if (notification.textContent.includes('⏳')) {
                notification.remove();
            }
        });
    }

    // الحل النهائي: إعادة ترتيب الجدول بالقوة حسب التاريخ
    function finalSortFix() {
        console.log('🎯 الحل النهائي: إعادة ترتيب الجدول حسب التاريخ...');
        const tbody = document.getElementById('meetingsTableBody');
        if (!tbody) return;

        const rows = Array.from(tbody.querySelectorAll('.meeting-row'));
        console.log(`📋 عدد الصفوف قبل الترتيب: ${rows.length}`);

        // استخراج التواريخ وترتيب الصفوف
        const rowsWithDates = rows.map(row => {
            const dateAttr = row.getAttribute('data-date');
            const subject = row.querySelector('td:nth-child(3)')?.textContent?.trim();
            console.log(`📅 الصف: ${subject} - التاريخ: ${dateAttr}`);
            return {
                row: row,
                date: new Date(dateAttr),
                subject: subject
            };
        });

        // ترتيب حسب التاريخ (الأقرب أولاً)
        rowsWithDates.sort((a, b) => a.date - b.date);

        console.log('🔄 الترتيب الجديد:');
        rowsWithDates.forEach((item, index) => {
            console.log(`  #${index + 1}: ${item.subject} - ${item.date.toDateString()}`);
        });

        // إزالة جميع الصفوف
        rows.forEach(row => row.remove());

        // إعادة إدراج الصفوف بالترتيب الصحيح
        rowsWithDates.forEach((item, index) => {
            // تحديث رقم الترتيب
            const badge = item.row.querySelector('.badge');
            if (badge) badge.textContent = `#${index + 1}`;

            // إضافة الصف بالترتيب الجديد
            tbody.appendChild(item.row);
        });

        console.log('✅ تم إعادة ترتيب الجدول نهائياً - الأقرب أولاً!');
    }

    // إضافة مستمعي الأحداث للفلاتر
    document.addEventListener('DOMContentLoaded', function() {
        // الحل النهائي: إعادة ترتيب الجدول بالقوة
        setTimeout(finalSortFix, 200);
        setTimeout(finalSortFix, 500);
        setTimeout(finalSortFix, 1000);

        // إضافة مستمع للتأكد من الترتيب
        window.addEventListener('load', finalSortFix);
        document.addEventListener('readystatechange', finalSortFix);
        // إضافة مستمعي الأحداث مع تأثيرات بصرية
        document.getElementById('statusFilter').addEventListener('change', function() {
            console.log('🔄 تغيير فلتر الحالة:', this.value);

            // تأثير بصري للقائمة المنسدلة
            this.style.borderColor = '#28a745';
            this.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.25)';

            setTimeout(() => {
                this.style.borderColor = '';
                this.style.boxShadow = '';
            }, 1000);

            // إظهار رسالة الترتيب
            if (this.value) {
                const filterNames = {
                    'active': 'النشطة',
                    'cancelled': 'الملغية',
                    'postponed': 'المؤجلة'
                };

                const sortMessage = `🔄 جاري ترتيب الاجتماعات ${filterNames[this.value]} من الأقرب إلى الأبعد...`;

                showTemporaryMessage(sortMessage, 'info', 2000);
            }

            filterMeetings();
        });

        document.getElementById('typeFilter').addEventListener('change', function() {
            console.log('🔄 تغيير فلتر النوع:', this.value);

            // تأثير بصري للقائمة المنسدلة
            this.style.borderColor = '#17a2b8';
            this.style.boxShadow = '0 0 0 3px rgba(23, 162, 184, 0.25)';

            setTimeout(() => {
                this.style.borderColor = '';
                this.style.boxShadow = '';
            }, 1000);

            filterMeetings();
        });

        document.getElementById('searchInput').addEventListener('input', function() {
            console.log('🔍 تغيير البحث:', this.value);

            // تأثير بصري لحقل البحث
            this.style.borderColor = '#ffc107';
            this.style.boxShadow = '0 0 0 3px rgba(255, 193, 7, 0.25)';

            setTimeout(() => {
                this.style.borderColor = '';
                this.style.boxShadow = '';
            }, 1000);

            filterMeetings();
        });

        // ترتيب الاجتماعات فور تحميل الصفحة
        console.log('🔄 ترتيب الاجتماعات فور تحميل الصفحة...');
        setTimeout(() => {
            sortVisibleMeetings('all');
        }, 500);

        // إضافة تأكيدات للأزرار
        console.log('✅ تم تحميل صفحة تعديل الاجتماعات');
        console.log('🔧 جميع الأزرار مفعلة ومتاحة');
    });

    // دالة تمييز النص المطابق
    function highlightSearchText(row, searchTerm) {
        const elements = [
            row.querySelector('.subject-column .fw-bold'),
            row.querySelector('.location-column'),
            row.querySelector('.subject-column .meeting-details')
        ];

        elements.forEach(element => {
            if (element && element.textContent.toLowerCase().includes(searchTerm)) {
                const originalText = element.textContent;
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                const highlightedText = originalText.replace(regex, '<mark style="background-color: #ffeb3b; padding: 2px 4px; border-radius: 3px;">$1</mark>');
                element.innerHTML = highlightedText;
            }
        });
    }

    // دالة إزالة تمييز النص
    function removeHighlight(row) {
        const markedElements = row.querySelectorAll('mark');
        markedElements.forEach(mark => {
            const parent = mark.parentNode;
            parent.replaceChild(document.createTextNode(mark.textContent), mark);
            parent.normalize();
        });
    }
</script>
{% endblock %}
