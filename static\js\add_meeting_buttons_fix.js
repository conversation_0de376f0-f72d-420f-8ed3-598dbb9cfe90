/**
 * إصلاح شامل لجميع أزرار صفحة إدخال الاجتماع
 * Complete Fix for Add Meeting Page Buttons
 */

console.log('🔧 تحميل إصلاحات أزرار صفحة إدخال الاجتماع...');

// متغيرات عامة
let isFormSubmitting = false;
let selectedDressCode = null;

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء إصلاح أزرار صفحة إدخال الاجتماع...');
    
    // انتظار قليل للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        initializeAllButtons();
        initializeDressCodeSelection();
        initializeDateTimePickers();
        initializeFormSubmission();
        initializeFileUpload();
        initializeInvitingParties();
        
        console.log('✅ تم إصلاح جميع أزرار صفحة إدخال الاجتماع');
    }, 500);
});

// تهيئة جميع الأزرار
function initializeAllButtons() {
    console.log('🔘 إصلاح جميع الأزرار...');
    
    // إزالة جميع onclick القديمة
    const allButtons = document.querySelectorAll('button, [onclick]');
    allButtons.forEach(element => {
        if (element.hasAttribute('onclick')) {
            element.removeAttribute('onclick');
        }
    });
    
    // زر إعادة التعيين
    const resetBtn = document.querySelector('.modern-reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', handleResetForm);
        console.log('✅ تم ربط زر إعادة التعيين');
    }
    
    // زر إزالة الملف
    const removeFileBtn = document.getElementById('removeFile');
    if (removeFileBtn) {
        removeFileBtn.addEventListener('click', handleRemoveFile);
        console.log('✅ تم ربط زر إزالة الملف');
    }
    
    console.log('✅ تم إصلاح جميع الأزرار');
}

// تهيئة اختيار نوع اللباس
function initializeDressCodeSelection() {
    console.log('👔 إصلاح اختيار نوع اللباس...');
    
    const dressCodeOptions = document.querySelectorAll('.dress-code-option');
    const hiddenInput = document.getElementById('dress_code');
    
    dressCodeOptions.forEach(option => {
        // إزالة onclick القديم
        option.removeAttribute('onclick');
        
        // إضافة مستمع جديد
        option.addEventListener('click', function() {
            const value = this.getAttribute('data-value');
            console.log('👔 تم اختيار نوع اللباس:', value);
            
            // إزالة التحديد من جميع الخيارات
            dressCodeOptions.forEach(opt => opt.classList.remove('selected'));
            
            // تحديد الخيار الحالي
            this.classList.add('selected');
            
            // تحديث القيمة المخفية
            if (hiddenInput) {
                hiddenInput.value = value;
            }
            
            selectedDressCode = value;
            
            // تأثير بصري
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            console.log('✅ تم تحديث نوع اللباس:', value);
        });
    });
    
    console.log('✅ تم إصلاح اختيار نوع اللباس');
}

// تهيئة منتقيات التاريخ والوقت
function initializeDateTimePickers() {
    console.log('📅 إصلاح منتقيات التاريخ والوقت...');
    
    // منتقي تاريخ الاجتماع
    const meetingDateOverlay = document.querySelector('.modern-date-overlay');
    if (meetingDateOverlay) {
        meetingDateOverlay.removeAttribute('onclick');
        meetingDateOverlay.addEventListener('click', function() {
            const dateInput = document.getElementById('meeting_date');
            if (dateInput) {
                dateInput.focus();
                if (dateInput.showPicker) {
                    dateInput.showPicker();
                }
            }
        });
    }
    
    // منتقي وقت الاجتماع
    const meetingTimeOverlay = document.querySelector('.modern-time-overlay');
    if (meetingTimeOverlay) {
        meetingTimeOverlay.removeAttribute('onclick');
        meetingTimeOverlay.addEventListener('click', function() {
            const timeInput = document.getElementById('meeting_time');
            if (timeInput) {
                timeInput.focus();
                if (timeInput.showPicker) {
                    timeInput.showPicker();
                }
            }
        });
    }
    
    // منتقي تاريخ الكتاب
    const bookDateOverlay = document.querySelector('.modern-date-overlay:last-of-type');
    if (bookDateOverlay) {
        bookDateOverlay.removeAttribute('onclick');
        bookDateOverlay.addEventListener('click', function() {
            const bookDateInput = document.getElementById('book_date');
            if (bookDateInput) {
                bookDateInput.focus();
                if (bookDateInput.showPicker) {
                    bookDateInput.showPicker();
                }
            }
        });
    }
    
    console.log('✅ تم إصلاح منتقيات التاريخ والوقت');
}

// تهيئة إرسال النموذج
function initializeFormSubmission() {
    console.log('📝 إصلاح إرسال النموذج...');
    
    const form = document.getElementById('meetingForm');
    const submitBtn = document.querySelector('.modern-submit-btn');
    
    if (form && submitBtn) {
        // إزالة أي مستمعات سابقة
        form.removeEventListener('submit', handleFormSubmit);
        
        // إضافة مستمع جديد
        form.addEventListener('submit', handleFormSubmit);
        
        console.log('✅ تم ربط إرسال النموذج');
    }
}

// معالجة إرسال النموذج
function handleFormSubmit(event) {
    event.preventDefault();
    
    if (isFormSubmitting) {
        console.log('⏳ النموذج قيد الإرسال بالفعل...');
        return false;
    }
    
    console.log('📤 بدء إرسال النموذج...');
    
    // التحقق من صحة البيانات
    if (!validateForm()) {
        return false;
    }
    
    isFormSubmitting = true;
    
    const form = event.target;
    const submitBtn = form.querySelector('.modern-submit-btn');
    
    // تحديث زر الإرسال
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    }
    
    // إرسال البيانات
    const formData = new FormData(form);
    
    fetch(form.action || window.location.pathname, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('✅ تم إرسال النموذج بنجاح:', data);
        
        // إظهار رسالة نجاح
        showSuccessMessage('تم حفظ الاجتماع بنجاح! 🎉');
        
        // إعادة تعيين النموذج
        setTimeout(() => {
            form.reset();
            resetFormState();
        }, 2000);
    })
    .catch(error => {
        console.error('❌ خطأ في إرسال النموذج:', error);
        showErrorMessage('حدث خطأ أثناء حفظ الاجتماع. يرجى المحاولة مرة أخرى.');
    })
    .finally(() => {
        isFormSubmitting = false;
        
        // إعادة تفعيل زر الإرسال
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الاجتماع';
        }
    });
    
    return false;
}

// التحقق من صحة النموذج
function validateForm() {
    console.log('🔍 التحقق من صحة النموذج...');
    
    // التحقق من نوع اللباس
    if (!selectedDressCode) {
        showErrorMessage('يرجى اختيار نوع اللباس');
        return false;
    }
    
    // التحقق من الحقول المطلوبة
    const requiredFields = [
        'meeting_title',
        'meeting_date',
        'meeting_time',
        'inviting_party',
        'book_date'
    ];
    
    for (let fieldId of requiredFields) {
        const field = document.getElementById(fieldId);
        if (!field || !field.value.trim()) {
            showErrorMessage(`يرجى ملء حقل ${getFieldLabel(fieldId)}`);
            field?.focus();
            return false;
        }
    }
    
    console.log('✅ النموذج صحيح');
    return true;
}

// الحصول على تسمية الحقل
function getFieldLabel(fieldId) {
    const labels = {
        'meeting_title': 'عنوان الاجتماع',
        'meeting_date': 'تاريخ الاجتماع',
        'meeting_time': 'وقت الاجتماع',
        'inviting_party': 'جهة الدعوة',
        'book_date': 'تاريخ الكتاب'
    };
    return labels[fieldId] || fieldId;
}

// معالجة إعادة تعيين النموذج
function handleResetForm() {
    console.log('🔄 إعادة تعيين النموذج...');
    
    if (confirm('هل تريد مسح جميع البيانات المدخلة؟')) {
        const form = document.getElementById('meetingForm');
        if (form) {
            form.reset();
            resetFormState();
            showSuccessMessage('تم مسح جميع البيانات');
        }
    }
}

// إعادة تعيين حالة النموذج
function resetFormState() {
    // إعادة تعيين نوع اللباس
    selectedDressCode = null;
    document.querySelectorAll('.dress-code-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // مسح الحقل المخفي
    const hiddenInput = document.getElementById('dress_code');
    if (hiddenInput) {
        hiddenInput.value = '';
    }
    
    console.log('✅ تم إعادة تعيين حالة النموذج');
}

// معالجة إزالة الملف
function handleRemoveFile() {
    console.log('🗑️ إزالة الملف...');
    
    const fileInput = document.getElementById('meeting_file');
    const fileInfo = document.getElementById('fileInfo');
    
    if (fileInput) {
        fileInput.value = '';
    }
    
    if (fileInfo) {
        fileInfo.style.display = 'none';
    }
    
    showSuccessMessage('تم إزالة الملف');
}

// تهيئة رفع الملفات
function initializeFileUpload() {
    const fileInput = document.getElementById('meeting_file');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                if (fileName) {
                    fileName.textContent = file.name;
                }
                if (fileInfo) {
                    fileInfo.style.display = 'block';
                }
                console.log('📎 تم اختيار ملف:', file.name);
            }
        });
    }
}

// تهيئة قائمة جهات الدعوة
function initializeInvitingParties() {
    const input = document.getElementById('inviting_party');
    if (input) {
        input.removeAttribute('onclick');
        input.removeAttribute('onfocus');
        input.removeAttribute('oninput');
        
        input.addEventListener('click', showInvitingPartiesList);
        input.addEventListener('focus', showInvitingPartiesList);
        input.addEventListener('input', function() {
            filterInvitingPartiesList(this.value);
        });
    }
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    console.log('✅', message);
    
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 9999;
        font-weight: bold;
        max-width: 400px;
    `;
    notification.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// إظهار رسالة خطأ
function showErrorMessage(message) {
    console.error('❌', message);
    
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #dc3545;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 9999;
        font-weight: bold;
        max-width: 400px;
    `;
    notification.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 5 ثوان
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// دوال مساعدة إضافية

// إظهار قائمة جهات الدعوة
function showInvitingPartiesList() {
    console.log('📋 إظهار قائمة جهات الدعوة...');

    const dropdown = document.getElementById('inviting_party_dropdown');
    if (!dropdown) return;

    // قائمة جهات الدعوة
    const invitingPartiesList = [
        'رئاسة الأركان المشتركة',
        'القيادة العامة للقوات المسلحة',
        'مديرية الأمن العام',
        'مديرية المخابرات العامة',
        'وزارة الدفاع',
        'وزارة الداخلية',
        'الحرس الملكي',
        'القوات الخاصة',
        'سلاح الجو الملكي',
        'القوات البحرية الملكية',
        'قوات الدرك'
    ];

    // مسح المحتوى السابق
    dropdown.innerHTML = '';

    // إضافة العناصر
    invitingPartiesList.forEach(party => {
        const item = document.createElement('div');
        item.className = 'dropdown-item';
        item.style.cursor = 'pointer';
        item.textContent = party;

        item.addEventListener('click', function() {
            document.getElementById('inviting_party').value = this.textContent;
            dropdown.style.display = 'none';
        });

        dropdown.appendChild(item);
    });

    // إضافة خيار "أخرى"
    const otherItem = document.createElement('div');
    otherItem.className = 'dropdown-item';
    otherItem.style.cursor = 'pointer';
    otherItem.style.borderTop = '2px solid #e9ecef';
    otherItem.style.fontWeight = 'bold';
    otherItem.style.color = '#007bff';
    otherItem.innerHTML = '<i class="fas fa-edit me-2"></i>أخرى (كتابة يدوية)';

    otherItem.addEventListener('click', function() {
        document.getElementById('inviting_party').value = '';
        document.getElementById('inviting_party').focus();
        dropdown.style.display = 'none';
    });

    dropdown.appendChild(otherItem);
    dropdown.style.display = 'block';
}

// فلترة قائمة جهات الدعوة
function filterInvitingPartiesList(searchTerm) {
    console.log('🔍 فلترة جهات الدعوة:', searchTerm);

    if (!searchTerm || searchTerm.length < 2) {
        showInvitingPartiesList();
        return;
    }

    const dropdown = document.getElementById('inviting_party_dropdown');
    if (!dropdown) return;

    const invitingPartiesList = [
        'رئاسة الأركان المشتركة',
        'القيادة العامة للقوات المسلحة',
        'مديرية الأمن العام',
        'مديرية المخابرات العامة',
        'وزارة الدفاع',
        'وزارة الداخلية',
        'الحرس الملكي',
        'القوات الخاصة',
        'سلاح الجو الملكي',
        'القوات البحرية الملكية',
        'قوات الدرك'
    ];

    // فلترة القائمة
    const filtered = invitingPartiesList.filter(party =>
        party.includes(searchTerm)
    );

    // مسح المحتوى السابق
    dropdown.innerHTML = '';

    // إضافة النتائج المفلترة
    filtered.forEach(party => {
        const item = document.createElement('div');
        item.className = 'dropdown-item';
        item.style.cursor = 'pointer';
        item.textContent = party;

        item.addEventListener('click', function() {
            document.getElementById('inviting_party').value = this.textContent;
            dropdown.style.display = 'none';
        });

        dropdown.appendChild(item);
    });

    // إضافة خيار "أخرى" دائماً
    const otherItem = document.createElement('div');
    otherItem.className = 'dropdown-item';
    otherItem.style.cursor = 'pointer';
    otherItem.style.borderTop = '2px solid #e9ecef';
    otherItem.style.fontWeight = 'bold';
    otherItem.style.color = '#007bff';
    otherItem.innerHTML = '<i class="fas fa-edit me-2"></i>أخرى (كتابة يدوية)';

    otherItem.addEventListener('click', function() {
        document.getElementById('inviting_party').value = '';
        document.getElementById('inviting_party').focus();
        dropdown.style.display = 'none';
    });

    dropdown.appendChild(otherItem);
    dropdown.style.display = 'block';
}

// إخفاء القائمة عند النقر خارجها
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('inviting_party_dropdown');
    const input = document.getElementById('inviting_party');

    if (dropdown && input &&
        !dropdown.contains(event.target) &&
        !input.contains(event.target)) {
        dropdown.style.display = 'none';
    }
});

console.log('✅ تم تحميل إصلاحات أزرار صفحة إدخال الاجتماع');
