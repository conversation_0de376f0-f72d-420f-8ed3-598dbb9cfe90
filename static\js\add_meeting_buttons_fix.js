/**
 * إصلاح شامل لجميع أزرار صفحة إدخال الاجتماع
 * Complete Fix for Add Meeting Page Buttons
 */

console.log('🔧 تحميل إصلاحات أزرار صفحة إدخال الاجتماع...');

// متغيرات عامة
let isFormSubmitting = false;
let selectedDressCode = null;

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء إصلاح أزرار صفحة إدخال الاجتماع...');
    
    // انتظار قليل للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        initializeAllButtons();
        initializeDressCodeSelection();
        initializeDateTimePickers();
        initializeFormSubmission();
        initializeFileUpload();
        initializeInvitingParties();
        initializeCommunicationType();
        
        console.log('✅ تم إصلاح جميع أزرار صفحة إدخال الاجتماع');
    }, 500);
});

// تهيئة جميع الأزرار
function initializeAllButtons() {
    console.log('🔘 إصلاح جميع الأزرار...');

    // إزالة جميع onclick القديمة
    const allElements = document.querySelectorAll('*[onclick]');
    allElements.forEach(element => {
        const oldOnclick = element.getAttribute('onclick');
        console.log('🗑️ إزالة onclick من:', element.tagName, oldOnclick);
        element.removeAttribute('onclick');
    });

    // زر إعادة التعيين
    const resetBtn = document.querySelector('.modern-reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', handleResetForm);
        console.log('✅ تم ربط زر إعادة التعيين');
    }

    // زر إزالة الملف
    const removeFileBtn = document.getElementById('removeFile');
    if (removeFileBtn) {
        removeFileBtn.addEventListener('click', handleRemoveFile);
        console.log('✅ تم ربط زر إزالة الملف');
    }

    // إصلاح جميع الأزرار الأخرى
    fixAllOtherButtons();

    console.log('✅ تم إصلاح جميع الأزرار');
}

// إصلاح جميع الأزرار الأخرى
function fixAllOtherButtons() {
    console.log('🔧 إصلاح الأزرار الإضافية...');

    // أزرار التاريخ السريع
    const quickDateButtons = document.querySelectorAll('.quick-date-btn');
    quickDateButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const dateType = this.textContent.trim();
            setQuickDate(dateType);
        });
    });

    // أزرار الوقت السريع
    const quickTimeButtons = document.querySelectorAll('.quick-time-btn');
    quickTimeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const timeValue = this.textContent.trim();
            setQuickTime(timeValue);
        });
    });

    // جميع الأزرار العامة
    const allButtons = document.querySelectorAll('button:not(.modern-submit-btn):not(.modern-reset-btn):not(#removeFile)');
    allButtons.forEach(btn => {
        if (!btn.hasAttribute('data-bs-dismiss') && !btn.hasAttribute('data-bs-toggle')) {
            btn.addEventListener('click', function(e) {
                console.log('🔘 نقر زر:', this.textContent.trim());

                // منع السلوك الافتراضي
                e.preventDefault();
                e.stopPropagation();

                // تأثير بصري
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);

                return false;
            });
        }
    });

    console.log('✅ تم إصلاح الأزرار الإضافية');
}

// تعيين تاريخ سريع
function setQuickDate(dateType) {
    console.log('📅 تعيين تاريخ سريع:', dateType);

    const dateInput = document.getElementById('meeting_date');
    if (!dateInput) return;

    const today = new Date();
    let targetDate = new Date(today);

    switch(dateType) {
        case 'اليوم':
            // targetDate = today
            break;
        case 'غداً':
            targetDate.setDate(today.getDate() + 1);
            break;
        case 'بعد غد':
            targetDate.setDate(today.getDate() + 2);
            break;
        case 'الأسبوع القادم':
            targetDate.setDate(today.getDate() + 7);
            break;
        default:
            console.log('نوع تاريخ غير معروف:', dateType);
            return;
    }

    // تنسيق التاريخ
    const formattedDate = targetDate.toISOString().split('T')[0];
    dateInput.value = formattedDate;

    // تأثير بصري
    dateInput.style.borderColor = '#28a745';
    setTimeout(() => {
        dateInput.style.borderColor = '#1B4332';
    }, 1000);

    showSuccessMessage(`تم تعيين التاريخ: ${dateType}`);
}

// تعيين وقت سريع
function setQuickTime(timeValue) {
    console.log('🕐 تعيين وقت سريع:', timeValue);

    const timeInput = document.getElementById('meeting_time');
    if (!timeInput) return;

    // تحويل الوقت إلى تنسيق 24 ساعة
    let formattedTime = timeValue;

    if (timeValue.includes('ص')) {
        formattedTime = timeValue.replace('ص', '').trim();
        if (formattedTime === '12:00') {
            formattedTime = '00:00';
        }
    } else if (timeValue.includes('م')) {
        formattedTime = timeValue.replace('م', '').trim();
        const [hours, minutes] = formattedTime.split(':');
        if (hours !== '12') {
            formattedTime = `${parseInt(hours) + 12}:${minutes}`;
        }
    }

    timeInput.value = formattedTime;

    // تأثير بصري
    timeInput.style.borderColor = '#28a745';
    setTimeout(() => {
        timeInput.style.borderColor = '#1B4332';
    }, 1000);

    showSuccessMessage(`تم تعيين الوقت: ${timeValue}`);
}

// تهيئة اختيار نوع اللباس
function initializeDressCodeSelection() {
    console.log('👔 إصلاح اختيار نوع اللباس...');
    
    const dressCodeOptions = document.querySelectorAll('.dress-code-option');
    const hiddenInput = document.getElementById('dress_code');
    
    dressCodeOptions.forEach(option => {
        // إزالة onclick القديم
        option.removeAttribute('onclick');
        
        // إضافة مستمع جديد
        option.addEventListener('click', function() {
            const value = this.getAttribute('data-value');
            console.log('👔 تم اختيار نوع اللباس:', value);
            
            // إزالة التحديد من جميع الخيارات
            dressCodeOptions.forEach(opt => opt.classList.remove('selected'));
            
            // تحديد الخيار الحالي
            this.classList.add('selected');
            
            // تحديث القيمة المخفية
            if (hiddenInput) {
                hiddenInput.value = value;
            }
            
            selectedDressCode = value;
            
            // تأثير بصري
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            console.log('✅ تم تحديث نوع اللباس:', value);
        });
    });
    
    console.log('✅ تم إصلاح اختيار نوع اللباس');
}

// تهيئة اختيار نوع الاتصال
function initializeCommunicationType() {
    console.log('📞 إصلاح اختيار نوع الاتصال...');

    const communicationOptions = document.querySelectorAll('.communication-option');
    const hiddenInput = document.getElementById('communication_type');
    let selectedCommunicationType = null;

    communicationOptions.forEach(option => {
        // إزالة onclick القديم
        option.removeAttribute('onclick');

        // إضافة مستمع جديد
        option.addEventListener('click', function() {
            const value = this.getAttribute('data-value');
            console.log('📞 تم اختيار نوع الاتصال:', value);

            // إزالة التحديد من جميع الخيارات
            communicationOptions.forEach(opt => opt.classList.remove('selected'));

            // تحديد الخيار الحالي
            this.classList.add('selected');

            // تحديث القيمة المخفية
            if (hiddenInput) {
                hiddenInput.value = value;
            }

            selectedCommunicationType = value;

            // تأثير بصري
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1.05)';
            }, 150);

            console.log('✅ تم تحديث نوع الاتصال:', value);

            // رسالة نجاح
            const labels = {
                'phone': 'مكالمة هاتفية',
                'verbal': 'شفهي',
                'email': 'إيميل'
            };
            showSuccessMessage(`تم اختيار: ${labels[value]}`);
        });
    });

    // تعيين الاختيار الافتراضي (شفهي)
    if (communicationOptions.length > 0) {
        communicationOptions[0].click();
    }

    console.log('✅ تم إصلاح اختيار نوع الاتصال');
}

// تنسيق عرض التاريخ (يوم-شهر-سنة)
function formatDateDisplay(dateInput) {
    if (!dateInput.value) return;

    try {
        // الحصول على التاريخ من القيمة (YYYY-MM-DD)
        const dateValue = dateInput.value;
        const [year, month, day] = dateValue.split('-');

        // تحويل إلى تنسيق يوم-شهر-سنة للعرض
        const formattedDate = `${day}-${month}-${year}`;

        // إضافة تلميح للمستخدم
        dateInput.title = `التاريخ: ${formattedDate}`;

        console.log('📅 تم تنسيق التاريخ:', {
            original: dateValue,
            formatted: formattedDate
        });

        // إظهار إشعار مؤقت
        showDateFormatNotification(formattedDate);

    } catch (error) {
        console.error('❌ خطأ في تنسيق التاريخ:', error);
    }
}

// إظهار إشعار تنسيق التاريخ
function showDateFormatNotification(formattedDate) {
    // إنشاء إشعار مؤقت
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(40, 167, 69, 0.95);
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: bold;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        backdrop-filter: blur(10px);
        animation: fadeInOut 2s ease-in-out;
    `;

    notification.innerHTML = `
        <i class="fas fa-calendar-check me-2"></i>
        التاريخ: ${formattedDate}
    `;

    // إضافة CSS للحركة إذا لم يكن موجوداً
    if (!document.getElementById('date-notification-styles')) {
        const style = document.createElement('style');
        style.id = 'date-notification-styles';
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // إزالة الإشعار بعد انتهاء الحركة
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 2000);
}

// تهيئة منتقيات التاريخ والوقت
function initializeDateTimePickers() {
    console.log('📅 إصلاح منتقيات التاريخ والوقت...');

    // منتقي تاريخ الاجتماع
    const meetingDateInput = document.getElementById('meeting_date');
    const meetingDateOverlays = document.querySelectorAll('.modern-date-overlay');

    if (meetingDateInput && meetingDateOverlays.length > 0) {
        // إزالة onclick القديمة
        meetingDateOverlays[0].removeAttribute('onclick');

        // إضافة مستمع جديد
        meetingDateOverlays[0].addEventListener('click', function() {
            console.log('📅 فتح منتقي تاريخ الاجتماع');
            meetingDateInput.focus();
            if (meetingDateInput.showPicker) {
                meetingDateInput.showPicker();
            }
        });

        // مستمع تغيير تاريخ الاجتماع (بدون قيود)
        meetingDateInput.addEventListener('change', function() {
            console.log('📅 تغيير تاريخ الاجتماع:', this.value);

            // تحويل التاريخ إلى تنسيق يوم-شهر-سنة
            formatDateDisplay(this);

            // تأثير بصري فقط
            this.style.borderColor = '#28a745';
            setTimeout(() => {
                this.style.borderColor = '#1B4332';
            }, 1000);
        });
    }

    // منتقي وقت الاجتماع
    const meetingTimeInput = document.getElementById('meeting_time');
    const meetingTimeOverlay = document.querySelector('.modern-time-overlay');

    if (meetingTimeInput && meetingTimeOverlay) {
        meetingTimeOverlay.removeAttribute('onclick');
        meetingTimeOverlay.addEventListener('click', function() {
            console.log('🕐 فتح منتقي وقت الاجتماع');
            meetingTimeInput.focus();
            if (meetingTimeInput.showPicker) {
                meetingTimeInput.showPicker();
            }
        });

        meetingTimeInput.addEventListener('change', function() {
            console.log('🕐 تغيير وقت الاجتماع:', this.value);

            // تأثير بصري فقط
            this.style.borderColor = '#28a745';
            setTimeout(() => {
                this.style.borderColor = '#1B4332';
            }, 1000);
        });
    }

    // منتقي تاريخ الكتاب (محسن)
    const bookDateInput = document.getElementById('book_date');
    const bookDateOverlays = document.querySelectorAll('.modern-date-overlay');

    if (bookDateInput && bookDateOverlays.length > 1) {
        // إزالة onclick القديمة
        bookDateOverlays[1].removeAttribute('onclick');

        // تحسين مظهر منتقي تاريخ الكتاب
        bookDateOverlays[1].style.cursor = 'pointer';
        bookDateOverlays[1].style.transition = 'all 0.3s ease';

        // إضافة مستمع جديد
        bookDateOverlays[1].addEventListener('click', function() {
            console.log('📅 فتح منتقي تاريخ الكتاب');
            bookDateInput.focus();
            if (bookDateInput.showPicker) {
                bookDateInput.showPicker();
            }
        });

        // تأثيرات التمرير
        bookDateOverlays[1].addEventListener('mouseenter', function() {
            this.style.background = 'rgba(0, 123, 255, 0.1)';
            this.style.transform = 'scale(1.1)';
        });

        bookDateOverlays[1].addEventListener('mouseleave', function() {
            this.style.background = 'var(--jaf-secondary)';
            this.style.transform = 'scale(1)';
        });

        // مستمع تغيير تاريخ الكتاب (بدون قيود)
        bookDateInput.addEventListener('change', function() {
            console.log('📅 تغيير تاريخ الكتاب:', this.value);

            // تحويل التاريخ إلى تنسيق يوم-شهر-سنة
            formatDateDisplay(this);

            // تأثير بصري فقط
            this.style.borderColor = '#28a745';
            setTimeout(() => {
                this.style.borderColor = '#1B4332';
            }, 1000);
        });
    }

    // تعيين تاريخ الكتاب الافتراضي (اليوم)
    if (bookDateInput && !bookDateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        bookDateInput.value = today;
        console.log('📅 تم تعيين تاريخ الكتاب الافتراضي:', today);
    }

    console.log('✅ تم إصلاح منتقيات التاريخ والوقت');
}

// التحقق من صحة تاريخ الكتاب
function validateBookDate() {
    console.log('🔍 التحقق من صحة تاريخ الكتاب...');

    const bookDateInput = document.getElementById('book_date');
    const meetingDateInput = document.getElementById('meeting_date');

    if (!bookDateInput || !meetingDateInput) {
        console.log('❌ لم يتم العثور على حقول التاريخ');
        return false;
    }

    // إزالة التأثيرات السابقة
    bookDateInput.style.borderColor = '#1B4332';
    bookDateInput.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';

    // إزالة رسائل الخطأ السابقة
    const existingError = document.querySelector('.book-date-error');
    if (existingError) {
        existingError.remove();
    }

    // التحقق فقط إذا تم ملء كلا التاريخين
    if (bookDateInput.value && meetingDateInput.value) {
        console.log('📅 مقارنة التواريخ:', {
            bookDate: bookDateInput.value,
            meetingDate: meetingDateInput.value
        });

        // مقارنة التواريخ كنصوص (YYYY-MM-DD) لتجنب مشاكل المنطقة الزمنية
        if (bookDateInput.value > meetingDateInput.value) {
            // تاريخ الكتاب أكبر من تاريخ الاجتماع - خطأ
            bookDateInput.style.borderColor = '#dc3545';
            bookDateInput.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';

            // إضافة رسالة خطأ
            const errorMessage = document.createElement('div');
            errorMessage.className = 'book-date-error alert alert-danger mt-2';
            errorMessage.style.fontSize = '14px';
            errorMessage.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>خطأ:</strong> يجب أن يكون تاريخ الكتاب مساوياً أو أقل من تاريخ الاجتماع
                <br>
                <small>تاريخ الكتاب: ${bookDateInput.value} | تاريخ الاجتماع: ${meetingDateInput.value}</small>
            `;

            // إدراج رسالة الخطأ بعد حاوي التاريخ
            const container = bookDateInput.closest('.modern-date-input-container') || bookDateInput.parentNode;
            container.parentNode.insertBefore(errorMessage, container.nextSibling);

            showErrorMessage('تاريخ الكتاب يجب أن يكون مساوياً أو أقل من تاريخ الاجتماع');

            console.log('❌ تاريخ الكتاب غير صحيح');
            return false;
        } else {
            // تاريخ الكتاب صحيح
            bookDateInput.style.borderColor = '#28a745';
            bookDateInput.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.1)';

            setTimeout(() => {
                bookDateInput.style.borderColor = '#1B4332';
                bookDateInput.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';
            }, 2000);

            console.log('✅ تاريخ الكتاب صحيح');
            return true;
        }
    }

    return true;
}

// التحقق من صحة تاريخ الاجتماع
function validateMeetingDate() {
    console.log('🔍 التحقق من صحة تاريخ الاجتماع...');

    const meetingDateInput = document.getElementById('meeting_date');
    if (!meetingDateInput || !meetingDateInput.value) {
        return true; // إذا لم يتم اختيار تاريخ بعد
    }

    const meetingDate = new Date(meetingDateInput.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // إزالة الوقت للمقارنة بالتاريخ فقط

    // إزالة التأثيرات السابقة
    meetingDateInput.style.borderColor = '#1B4332';
    meetingDateInput.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';

    // إزالة رسائل الخطأ السابقة
    const existingError = document.querySelector('.meeting-date-error');
    if (existingError) {
        existingError.remove();
    }

    if (meetingDate < today) {
        // تاريخ الاجتماع في الماضي - خطأ
        meetingDateInput.style.borderColor = '#dc3545';
        meetingDateInput.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';

        // إضافة رسالة خطأ
        const errorMessage = document.createElement('div');
        errorMessage.className = 'meeting-date-error alert alert-danger mt-2';
        errorMessage.style.fontSize = '14px';
        errorMessage.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>خطأ:</strong> لا يمكن اختيار تاريخ في الماضي
            <br>
            <small>التاريخ المختار: ${meetingDate.toLocaleDateString('ar-SA')} | اليوم: ${today.toLocaleDateString('ar-SA')}</small>
        `;

        meetingDateInput.parentNode.appendChild(errorMessage);

        showErrorMessage('تاريخ الاجتماع يجب أن يكون اليوم أو في المستقبل');

        console.log('❌ تاريخ الاجتماع في الماضي');
        return false;
    }

    console.log('✅ تاريخ الاجتماع صحيح');
    return true;
}

// التحقق من صحة وقت الاجتماع
function validateMeetingTime() {
    console.log('🔍 التحقق من صحة وقت الاجتماع...');

    const meetingDateInput = document.getElementById('meeting_date');
    const meetingTimeInput = document.getElementById('meeting_time');

    if (!meetingDateInput || !meetingTimeInput || !meetingDateInput.value || !meetingTimeInput.value) {
        return true; // إذا لم يتم اختيار التاريخ أو الوقت بعد
    }

    const meetingDate = new Date(meetingDateInput.value);
    const today = new Date();

    // إزالة التأثيرات السابقة
    meetingTimeInput.style.borderColor = '#1B4332';
    meetingTimeInput.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';

    // إزالة رسائل الخطأ السابقة
    const existingError = document.querySelector('.meeting-time-error');
    if (existingError) {
        existingError.remove();
    }

    // التحقق فقط إذا كان تاريخ الاجتماع هو اليوم
    if (meetingDate.toDateString() === today.toDateString()) {
        const [hours, minutes] = meetingTimeInput.value.split(':');
        const meetingDateTime = new Date(meetingDate);
        meetingDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

        const now = new Date();

        if (meetingDateTime <= now) {
            // وقت الاجتماع قد انتهى - خطأ
            meetingTimeInput.style.borderColor = '#dc3545';
            meetingTimeInput.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';

            // إضافة رسالة خطأ
            const errorMessage = document.createElement('div');
            errorMessage.className = 'meeting-time-error alert alert-danger mt-2';
            errorMessage.style.fontSize = '14px';
            errorMessage.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>خطأ:</strong> لا يمكن اختيار وقت قد انتهى
                <br>
                <small>الوقت المختار: ${meetingTimeInput.value} | الوقت الحالي: ${now.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}</small>
            `;

            meetingTimeInput.parentNode.appendChild(errorMessage);

            showErrorMessage('وقت الاجتماع يجب أن يكون في المستقبل');

            console.log('❌ وقت الاجتماع قد انتهى');
            return false;
        }
    }

    console.log('✅ وقت الاجتماع صحيح');
    return true;
}

// تحديث قيود تاريخ الكتاب
function updateBookDateConstraints() {
    console.log('🔄 تحديث قيود تاريخ الكتاب...');

    const bookDateInput = document.getElementById('book_date');
    const meetingDateInput = document.getElementById('meeting_date');

    if (!bookDateInput || !meetingDateInput) {
        return;
    }

    if (meetingDateInput.value) {
        // تعيين الحد الأقصى لتاريخ الكتاب = تاريخ الاجتماع
        bookDateInput.setAttribute('max', meetingDateInput.value);
        console.log('📅 تم تعيين الحد الأقصى لتاريخ الكتاب:', meetingDateInput.value);

        // إذا كان تاريخ الكتاب الحالي أكبر من تاريخ الاجتماع، قم بتعديله
        if (bookDateInput.value && bookDateInput.value > meetingDateInput.value) {
            bookDateInput.value = meetingDateInput.value;
            showSuccessMessage(`تم تعديل تاريخ الكتاب ليكون مساوياً لتاريخ الاجتماع`);
        }

        // التحقق من صحة تاريخ الكتاب
        validateBookDate();
    } else {
        // إزالة الحد الأقصى إذا لم يتم اختيار تاريخ الاجتماع
        bookDateInput.removeAttribute('max');
    }
}

// تهيئة إرسال النموذج
function initializeFormSubmission() {
    console.log('📝 إصلاح إرسال النموذج...');
    
    const form = document.getElementById('meetingForm');
    const submitBtn = document.querySelector('.modern-submit-btn');
    
    if (form && submitBtn) {
        // إزالة أي مستمعات سابقة
        form.removeEventListener('submit', handleFormSubmit);
        
        // إضافة مستمع جديد
        form.addEventListener('submit', handleFormSubmit);
        
        console.log('✅ تم ربط إرسال النموذج');
    }
}

// معالجة إرسال النموذج
function handleFormSubmit(event) {
    event.preventDefault();
    
    if (isFormSubmitting) {
        console.log('⏳ النموذج قيد الإرسال بالفعل...');
        return false;
    }
    
    console.log('📤 بدء إرسال النموذج...');
    
    // التحقق من صحة البيانات
    if (!validateForm()) {
        return false;
    }
    
    isFormSubmitting = true;
    
    const form = event.target;
    const submitBtn = form.querySelector('.modern-submit-btn');
    
    // تحديث زر الإرسال
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    }
    
    // إرسال البيانات
    const formData = new FormData(form);
    
    fetch(form.action || window.location.pathname, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('✅ تم إرسال النموذج بنجاح:', data);
        
        // إظهار رسالة نجاح
        showSuccessMessage('تم حفظ الاجتماع بنجاح! 🎉');
        
        // إعادة تعيين النموذج
        setTimeout(() => {
            form.reset();
            resetFormState();
        }, 2000);
    })
    .catch(error => {
        console.error('❌ خطأ في إرسال النموذج:', error);
        showErrorMessage('حدث خطأ أثناء حفظ الاجتماع. يرجى المحاولة مرة أخرى.');
    })
    .finally(() => {
        isFormSubmitting = false;
        
        // إعادة تفعيل زر الإرسال
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الاجتماع';
        }
    });
    
    return false;
}

// التحقق من صحة النموذج
function validateForm() {
    console.log('🔍 التحقق من صحة النموذج...');

    // التحقق من نوع اللباس
    if (!selectedDressCode) {
        showErrorMessage('يرجى اختيار نوع اللباس');

        // التمرير إلى قسم نوع اللباس
        const dressCodeSection = document.querySelector('.dress-code-grid');
        if (dressCodeSection) {
            dressCodeSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        return false;
    }

    // التحقق من الحقول المطلوبة
    const requiredFields = [
        'subject',
        'meeting_date',
        'meeting_time',
        'inviting_party',
        'book_date',
        'location',
        'meeting_type',
        'book_number',
        'communication_type'
    ];

    for (let fieldId of requiredFields) {
        const field = document.getElementById(fieldId);
        if (!field || !field.value.trim()) {
            showErrorMessage(`يرجى ملء حقل ${getFieldLabel(fieldId)}`);
            field?.focus();
            field?.scrollIntoView({ behavior: 'smooth', block: 'center' });
            return false;
        }
    }

    // تم إزالة التحققات من التواريخ حسب الطلب

    console.log('✅ النموذج صحيح');
    return true;
}

// الحصول على تسمية الحقل
function getFieldLabel(fieldId) {
    const labels = {
        'subject': 'موضوع الاجتماع',
        'meeting_date': 'تاريخ الاجتماع',
        'meeting_time': 'وقت الاجتماع',
        'inviting_party': 'جهة الدعوة',
        'book_date': 'تاريخ الكتاب',
        'location': 'مكان الاجتماع',
        'meeting_type': 'نوع الفعالية',
        'book_number': 'رقم الكتاب',
        'communication_type': 'نوع الاتصال'
    };
    return labels[fieldId] || fieldId;
}

// معالجة إعادة تعيين النموذج
function handleResetForm() {
    console.log('🔄 إعادة تعيين النموذج...');
    
    if (confirm('هل تريد مسح جميع البيانات المدخلة؟')) {
        const form = document.getElementById('meetingForm');
        if (form) {
            form.reset();
            resetFormState();
            showSuccessMessage('تم مسح جميع البيانات');
        }
    }
}

// إعادة تعيين حالة النموذج
function resetFormState() {
    // إعادة تعيين نوع اللباس
    selectedDressCode = null;
    document.querySelectorAll('.dress-code-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // مسح الحقل المخفي
    const hiddenInput = document.getElementById('dress_code');
    if (hiddenInput) {
        hiddenInput.value = '';
    }
    
    console.log('✅ تم إعادة تعيين حالة النموذج');
}

// معالجة إزالة الملف
function handleRemoveFile() {
    console.log('🗑️ إزالة الملف...');

    // البحث عن حقل الملف بطرق متعددة
    const fileInput = document.getElementById('meeting_file') ||
                     document.getElementById('attachments') ||
                     document.querySelector('input[type="file"]');

    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');

    if (fileInput) {
        fileInput.value = '';
        console.log('✅ تم مسح قيمة حقل الملف');
    }

    if (fileInfo) {
        fileInfo.style.display = 'none';
        console.log('✅ تم إخفاء معلومات الملف');
    }

    if (fileName) {
        fileName.textContent = '';
        console.log('✅ تم مسح اسم الملف');
    }

    // إعادة تعيين منطقة رفع الملفات
    const fileUploadArea = document.getElementById('fileUploadArea');
    if (fileUploadArea) {
        const icon = fileUploadArea.querySelector('i');
        const title = fileUploadArea.querySelector('h6') || fileUploadArea.querySelector('h5');
        const subtitle = fileUploadArea.querySelector('small') || fileUploadArea.querySelector('p');

        if (icon) {
            icon.className = 'fas fa-cloud-upload-alt';
            icon.style.color = '#6c757d';
        }

        if (title) {
            title.textContent = 'اسحب الملفات أو انقر';
            title.style.color = '';
        }

        if (subtitle) {
            subtitle.textContent = 'PDF, Word, والصور';
            subtitle.style.color = '#6c757d';
        }

        fileUploadArea.style.borderColor = '#dee2e6';
        fileUploadArea.style.backgroundColor = '#fafafa';

        console.log('✅ تم إعادة تعيين منطقة رفع الملفات');
    }

    showSuccessMessage('تم إزالة الملف');
}

// تهيئة رفع الملفات
function initializeFileUpload() {
    console.log('📎 تهيئة رفع الملفات...');

    // البحث عن حقل الملف بطرق متعددة
    let fileInput = document.getElementById('meeting_file') ||
                   document.getElementById('attachments') ||
                   document.querySelector('input[type="file"]');

    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileLabel = document.querySelector('label[for="meeting_file"]') ||
                     document.querySelector('label[for="attachments"]') ||
                     document.querySelector('.file-upload-area') ||
                     document.getElementById('fileUploadArea');

    console.log('🔍 عناصر رفع الملفات:', {
        fileInput: fileInput ? fileInput.id : 'غير موجود',
        fileInfo: fileInfo ? 'موجود' : 'غير موجود',
        fileName: fileName ? 'موجود' : 'غير موجود',
        fileLabel: fileLabel ? fileLabel.tagName : 'غير موجود'
    });

    if (fileInput) {
        console.log('✅ تم العثور على حقل الملف:', fileInput.id);

        // إضافة تأثيرات بصرية للتسمية أو منطقة الرفع
        if (fileLabel) {
            console.log('✅ تم العثور على منطقة رفع الملفات:', fileLabel.id || fileLabel.className);

            fileLabel.style.cursor = 'pointer';
            fileLabel.style.transition = 'all 0.3s ease';

            // إزالة أي مستمعات سابقة
            fileLabel.removeEventListener('click', handleFileAreaClick);

            // إضافة مستمع النقر لفتح منتقي الملفات
            fileLabel.addEventListener('click', handleFileAreaClick);

            fileLabel.addEventListener('mouseenter', function() {
                this.style.background = 'rgba(0, 123, 255, 0.1)';
                this.style.borderColor = '#007bff';
                this.style.transform = 'translateY(-2px)';
            });

            fileLabel.addEventListener('mouseleave', function() {
                this.style.background = 'rgba(248, 249, 250, 0.8)';
                this.style.borderColor = '#1B4332';
                this.style.transform = 'translateY(0)';
            });
        } else {
            console.log('❌ لم يتم العثور على منطقة رفع الملفات');
        }

        // دالة معالجة النقر على منطقة الملفات
        function handleFileAreaClick(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('📁 النقر على منطقة رفع الملفات - فتح منتقي الملفات');
            fileInput.click();
        }

        // إزالة مستمع سابق إن وجد
        fileInput.removeEventListener('change', handleFileChange);

        // إضافة مستمع جديد
        fileInput.addEventListener('change', handleFileChange);

        // دالة معالجة تغيير الملف
        function handleFileChange() {
            console.log('📁 تغيير في اختيار الملف...');

            if (this.files && this.files[0]) {
                const file = this.files[0];
                console.log('📎 تم اختيار ملف:', file.name, 'الحجم:', file.size, 'النوع:', file.type);

                // التحقق من نوع الملف
                const allowedTypes = [
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'image/jpeg',
                    'image/png',
                    'image/gif'
                ];

                if (!allowedTypes.includes(file.type)) {
                    showErrorMessage('نوع الملف غير مدعوم. يرجى اختيار ملف PDF أو Word أو صورة.');
                    this.value = '';
                    return;
                }

                // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
                const maxSize = 5 * 1024 * 1024; // 5MB
                if (file.size > maxSize) {
                    showErrorMessage('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت.');
                    this.value = '';
                    return;
                }

                // عرض معلومات الملف
                if (fileName) {
                    fileName.innerHTML = `
                        <i class="fas fa-file-${getFileIcon(file.type)} me-2"></i>
                        <strong>${file.name}</strong>
                        <br>
                        <small class="text-muted">الحجم: ${formatFileSize(file.size)}</small>
                    `;
                }

                if (fileInfo) {
                    fileInfo.style.display = 'block';
                    fileInfo.style.opacity = '0';
                    fileInfo.style.transform = 'translateY(10px)';
                    fileInfo.style.transition = 'all 0.3s ease';

                    setTimeout(() => {
                        fileInfo.style.opacity = '1';
                        fileInfo.style.transform = 'translateY(0)';
                    }, 100);
                }

                showSuccessMessage(`تم اختيار الملف: ${file.name}`);

            } else {
                // إخفاء معلومات الملف إذا لم يتم اختيار ملف
                if (fileInfo) {
                    fileInfo.style.display = 'none';
                }
            }
        });

        console.log('✅ تم تهيئة رفع الملفات');
    } else {
        console.error('❌ لم يتم العثور على حقل رفع الملف');
    }
}

// الحصول على أيقونة الملف حسب النوع
function getFileIcon(fileType) {
    if (fileType.includes('pdf')) return 'pdf';
    if (fileType.includes('word') || fileType.includes('document')) return 'word';
    if (fileType.includes('image')) return 'image';
    return 'alt';
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تهيئة قائمة جهات الدعوة
function initializeInvitingParties() {
    const input = document.getElementById('inviting_party');
    if (input) {
        input.removeAttribute('onclick');
        input.removeAttribute('onfocus');
        input.removeAttribute('oninput');

        // جعل الحقل للقراءة فقط
        input.readOnly = true;
        input.style.cursor = 'pointer';
        input.placeholder = 'اضغط لاختيار جهة الدعوة...';

        input.addEventListener('click', showInvitingPartiesModal);
        input.addEventListener('focus', showInvitingPartiesModal);

        // إنشاء المودال
        createInvitingPartiesModal();
    }
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    console.log('✅', message);

    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        z-index: 9999;
        font-weight: bold;
        max-width: 400px;
        font-family: Arial, sans-serif;
        font-size: 14px;
        direction: rtl;
        text-align: right;
        border: 2px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        animation: slideInLeft 0.3s ease-out;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-check-circle" style="font-size: 18px; color: #fff;"></i>
            <span style="flex: 1;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; margin: 0;">×</button>
        </div>
    `;

    // إضافة CSS للحركة
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInLeft {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutLeft {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(-100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 4 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOutLeft 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 4000);
}

// إظهار رسالة خطأ
function showErrorMessage(message) {
    console.error('❌', message);

    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        z-index: 9999;
        font-weight: bold;
        max-width: 400px;
        font-family: Arial, sans-serif;
        font-size: 14px;
        direction: rtl;
        text-align: right;
        border: 2px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        animation: slideInLeft 0.3s ease-out;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 18px; color: #fff;"></i>
            <span style="flex: 1;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; margin: 0;">×</button>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 6 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOutLeft 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 6000);
}

// دوال مساعدة إضافية

// إنشاء مودال جهات الدعوة
function createInvitingPartiesModal() {
    // التحقق من وجود المودال مسبقاً
    if (document.getElementById('invitingPartiesModal')) {
        return;
    }

    const modalHTML = `
        <div class="modal fade" id="invitingPartiesModal" tabindex="-1" aria-labelledby="invitingPartiesModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div class="modal-header" style="background: linear-gradient(135deg, #1b4332, #2d5a3d); color: white; border-radius: 15px 15px 0 0;">
                        <h5 class="modal-title" id="invitingPartiesModalLabel">
                            <i class="fas fa-building me-2"></i>
                            اختيار جهة الدعوة
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" style="padding: 2rem;">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="invitingPartySearch" placeholder="🔍 ابحث عن جهة الدعوة..." style="border-radius: 25px; padding: 12px 20px; border: 2px solid #e9ecef;">
                        </div>
                        <div id="invitingPartiesList" style="max-height: 400px; overflow-y: auto;">
                            <!-- سيتم ملء القائمة هنا -->
                        </div>
                    </div>
                    <div class="modal-footer" style="border: none; padding: 1rem 2rem 2rem;">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="border-radius: 25px; padding: 0.5rem 1.5rem;">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" id="addCustomParty" style="border-radius: 25px; padding: 0.5rem 1.5rem; background: linear-gradient(135deg, #007bff, #0056b3); border: none;">
                            <i class="fas fa-plus me-1"></i>
                            إضافة جهة جديدة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة المودال إلى الصفحة
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // تهيئة أحداث المودال
    initializeModalEvents();
}

// تهيئة أحداث المودال
function initializeModalEvents() {
    const searchInput = document.getElementById('invitingPartySearch');
    const addCustomBtn = document.getElementById('addCustomParty');

    // البحث في القائمة
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterModalParties(this.value);
        });
    }

    // زر إضافة جهة جديدة
    if (addCustomBtn) {
        addCustomBtn.addEventListener('click', function() {
            const searchValue = searchInput.value.trim();
            if (searchValue) {
                selectInvitingParty(searchValue);
            } else {
                showCustomPartyInput();
            }
        });
    }

    // ملء القائمة عند فتح المودال
    const modal = document.getElementById('invitingPartiesModal');
    if (modal) {
        modal.addEventListener('shown.bs.modal', function() {
            populateInvitingPartiesList();
            searchInput.focus();
        });

        modal.addEventListener('hidden.bs.modal', function() {
            searchInput.value = '';
            populateInvitingPartiesList();
        });
    }
}

// إظهار مودال جهات الدعوة
function showInvitingPartiesModal() {
    console.log('📋 إظهار مودال جهات الدعوة...');

    const modal = new bootstrap.Modal(document.getElementById('invitingPartiesModal'));
    modal.show();
}

// ملء قائمة جهات الدعوة من ملف ABCD.txt
function populateInvitingPartiesList() {
    const listContainer = document.getElementById('invitingPartiesList');
    if (!listContainer) return;

    // قائمة جهات الدعوة من ملف ABCD.txt
    const invitingPartiesList = [
        'عطوفة رئيس هيئة الاركان المشتركة',
        'المساعد للعمليات والتدريب',
        'المساعد للادارة والقوى البشرية',
        'مكتب المساعد للتخطيط والتنظيم والموارد الدفاعية',
        'وكيل قوة القوات المسلحة',
        'قيادة المنطقة العسكرية الوسطى',
        'قيادة المنطقة العسكرية الشمالية',
        'قيادة المنطقة العسكرية الشرقية',
        'قيادة المنطقة العسكرية الجنوبية',
        'قيادة قوات الملك عبدالله الثاني الخاصة الملكية',
        'قيادة سلاح المدفعية الملكي',
        'قيادة سلاح الجو الملكي',
        'قيادة القوة البحرية والزوارق الملكية',
        'قيادة الحرس الملكي الخاص',
        'قيادة لواء الملك حسين بن علي',
        'قيادة لواء الشيخ محمد بن زيد آل نهيان /التدخل السريع',
        'مديرية العمليات الحربية',
        'مديرية الاستخبارات العسكرية',
        'مديرية التدريب العسكري',
        'مديرية سلاح المشاه والدروع',
        'مكتب المفتش العام للقوات المسلحة الاردنية',
        'مديرية الدفاع الجوي الميداني الملكي',
        'مديرية سلاح الهندسة الملكي',
        'قيادة الشرطة العسكرية الملكية',
        'مديرية التخطيط والتنظيم',
        'مديرية التزويد اللوجستي',
        'مديرية الحرب الالكترونية',
        'مديرية شؤون الافراد',
        'مديرية شؤون الضباط',
        'مديرية التموين والنقل الملكي',
        'مديرية الخدمات الطبية الملكية',
        'مديرية سلاح الصيانة الملكي',
        'مديرية الامن العسكري',
        'مديرية ديوان القيادة العامة',
        'مديرية الاعلام العسكري',
        'مديرية أمن الحدود',
        'مديرية المشتريات الدفاعية',
        'مركز التخطيط المشترك (J5)',
        'مديرية الامن السيبراني وتكنولوجيا المعلومات',
        'مديرية مؤسسة الاسكان والاشغال العسكرية',
        'مديرية الطائرات المسيرة',
        'مديرية أمن وحماية المطارات',
        'كلية القيادة والاركان الملكية الاردنية',
        'كلية الدفاع الوطني الملكية الاردنية',
        'مكتب نائب الرئيس للشؤون العسكرية /جامعة مؤتة',
        'مديرية التربية والتعليم والثقافة العسكرية',
        'مديرية الدائرة المالية',
        'مديرية القضاء العسكري',
        'مديرية المؤسسة الاستهلاكية العسكرية',
        'مديرية افتاء القوات المسلحة الاردنية',
        'مديرية المساحة العسكرية',
        'مديرية الاتحاد الرياضي العسكري الاردني',
        'دائرة الموارد الدفاعية وادارة الاستثمار',
        'هيئة الاتصالات الخاصة',
        'الكلية العسكرية الملكية',
        'مديرية شؤون المرأه العسكرية',
        'مدينة سمو الشيخ محمد بن زايد آل نهيان التدريبية',
        'المختبرات العسكرية لمراقبة الجودة',
        'المكتب العسكري الخاص لجلالة القائد الاعلى',
        'المركز الاردني للتصميم والتطوير',
        'مجموعة الآليات والمشاغل الملكية',
        'ادارة جمعية الملكة رانيا العبدالله لرعاية العسكريين وأسرهم',
        'الشركة الوطنية للتشغيل والتدريب',
        'اتصالات جلالة القائد الاعلى',
        'ادارة مشروع التدريب الاردني البريطاني (فريق الإعارة)',
        'مركز الملك عبدالله الثاني لتدريب العمليات الخاصة KASOTC',
        'متحف الدبابات الملكي',
        'فندق القوات المسلحة'
    ];

    listContainer.innerHTML = '';

    invitingPartiesList.forEach((party, index) => {
        const item = document.createElement('div');
        item.className = 'inviting-party-item';
        item.style.cssText = `
            padding: 15px 20px;
            margin: 5px 0;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        `;

        item.innerHTML = `
            <div style="display: flex; align-items: center;">
                <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #1b4332, #2d5a3d); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                    <i class="fas fa-building" style="color: white; font-size: 16px;"></i>
                </div>
                <div>
                    <div style="font-weight: bold; color: #333; font-size: 16px;">${party}</div>
                    <div style="color: #666; font-size: 12px;">جهة حكومية</div>
                </div>
            </div>
            <i class="fas fa-chevron-left" style="color: #ccc;"></i>
        `;

        // تأثيرات التمرير
        item.addEventListener('mouseenter', function() {
            this.style.borderColor = '#007bff';
            this.style.background = 'rgba(0, 123, 255, 0.05)';
            this.style.transform = 'translateX(-5px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.borderColor = '#e9ecef';
            this.style.background = 'white';
            this.style.transform = 'translateX(0)';
        });

        // النقر لاختيار الجهة
        item.addEventListener('click', function() {
            selectInvitingParty(party);
        });

        listContainer.appendChild(item);

        // تأثير ظهور متدرج
        setTimeout(() => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'all 0.4s ease';

            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 50);
        }, index * 50);
    });
}

// فلترة جهات الدعوة في المودال
function filterModalParties(searchTerm) {
    const items = document.querySelectorAll('.inviting-party-item');

    items.forEach(item => {
        const text = item.textContent.toLowerCase();
        const search = searchTerm.toLowerCase();

        if (text.includes(search)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// اختيار جهة الدعوة
function selectInvitingParty(party) {
    console.log('✅ تم اختيار جهة الدعوة:', party);

    const input = document.getElementById('inviting_party');
    if (input) {
        input.value = party;

        // تأثير بصري
        input.style.borderColor = '#28a745';
        input.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.1)';

        setTimeout(() => {
            input.style.borderColor = '#1B4332';
            input.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';
        }, 1000);
    }

    // إغلاق المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('invitingPartiesModal'));
    if (modal) {
        modal.hide();
    }

    showSuccessMessage(`تم اختيار: ${party}`);
}

// إظهار حقل إدخال جهة مخصصة
function showCustomPartyInput() {
    const customInput = prompt('أدخل اسم الجهة الجديدة:');
    if (customInput && customInput.trim()) {
        selectInvitingParty(customInput.trim());
    }
}

// تنظيف الأحداث القديمة عند النقر خارج المودال
document.addEventListener('click', function(event) {
    // لا حاجة لهذا مع المودال الجديد
});

console.log('✅ تم تحميل إصلاحات أزرار صفحة إدخال الاجتماع');
