/**
 * نظام الإشعارات الموحد - ذهبي شفاف
 * نظام إدارة المواعيد - القوات المسلحة الأردنية
 */

// نظام الإشعارات الموحد
window.UnifiedNotifications = {
    // عرض إشعار نجاح
    showSuccess: function(message, duration = 3000) {
        this.showNotification(message, 'success', duration);
    },
    
    // عرض إشعار خطأ
    showError: function(message, duration = 4000) {
        this.showNotification(message, 'error', duration);
    },
    
    // عرض إشعار معلومات
    showInfo: function(message, duration = 3000) {
        this.showNotification(message, 'info', duration);
    },
    
    // عرض إشعار تحذير
    showWarning: function(message, duration = 3500) {
        this.showNotification(message, 'warning', duration);
    },
    
    // الوظيفة الرئيسية لعرض الإشعارات
    showNotification: function(message, type = 'success', duration = 3000) {
        try {
            console.log('عرض إشعار موحد:', message, type);
            
            // إزالة الإشعارات السابقة
            const existingNotifications = document.querySelectorAll('.unified-notification');
            existingNotifications.forEach(notification => notification.remove());

            // إنشاء الإشعار
            const notification = document.createElement('div');
            notification.className = 'unified-notification';
            
            // تحديد الألوان والأيقونات
            let bgColor, textColor, iconClass, iconColor;
            
            switch(type) {
                case 'success':
                    bgColor = 'rgba(255, 215, 0, 0.9)'; // ذهبي
                    textColor = '#000';
                    iconClass = 'fas fa-check-circle';
                    iconColor = '#28a745';
                    break;
                case 'error':
                    bgColor = 'rgba(255, 193, 7, 0.9)'; // ذهبي
                    textColor = '#212529';
                    iconClass = 'fas fa-exclamation-circle';
                    iconColor = '#856404';
                    break;
                case 'warning':
                    bgColor = 'rgba(255, 193, 7, 0.9)'; // ذهبي
                    textColor = '#212529';
                    iconClass = 'fas fa-exclamation-triangle';
                    iconColor = '#856404';
                    break;
                case 'info':
                    bgColor = 'rgba(255, 193, 7, 0.9)'; // ذهبي
                    textColor = '#212529';
                    iconClass = 'fas fa-info-circle';
                    iconColor = '#856404';
                    break;
                default:
                    bgColor = 'rgba(255, 193, 7, 0.9)'; // ذهبي
                    textColor = '#212529';
                    iconClass = 'fas fa-bell';
                    iconColor = '#856404';
            }

            // محتوى الإشعار
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <i class="${iconClass}" style="font-size: 20px; color: ${iconColor}; flex-shrink: 0;"></i>
                    <span style="flex: 1; font-weight: bold; font-size: 15px; color: #212529 !important; line-height: 1.4;">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: none; border: none; color: #212529 !important; font-size: 20px; cursor: pointer; padding: 0; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">×</button>
                </div>
            `;

            // تنسيق الإشعار
            notification.style.cssText = `
                position: fixed !important;
                top: 20px !important;
                left: 20px !important;
                background: ${bgColor} !important;
                color: #212529 !important;
                padding: 16px 20px !important;
                border-radius: 10px !important;
                box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
                z-index: 99999 !important;
                min-width: 320px !important;
                max-width: 450px !important;
                font-size: 14px !important;
                direction: rtl !important;
                text-align: right !important;
                opacity: 0 !important;
                transform: translateX(-100px) !important;
                transition: all 0.3s ease !important;
                border: 2px solid rgba(255,255,255,0.2) !important;
                display: block !important;
                visibility: visible !important;
            `;

            // إضافة الإشعار للصفحة
            document.body.appendChild(notification);

            // تأثير الظهور
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 10);

            // إزالة تلقائية
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(-100px)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);
            
        } catch (error) {
            console.error('خطأ في عرض الإشعار:', error);
            // fallback للإشعارات البسيطة
            alert(message);
        }
    }
};

// إتاحة النظام عالمياً
window.showNotification = window.UnifiedNotifications.showNotification.bind(window.UnifiedNotifications);
window.showSuccessNotification = window.UnifiedNotifications.showSuccess.bind(window.UnifiedNotifications);
window.showErrorNotification = window.UnifiedNotifications.showError.bind(window.UnifiedNotifications);
window.showInfoNotification = window.UnifiedNotifications.showInfo.bind(window.UnifiedNotifications);
window.showWarningNotification = window.UnifiedNotifications.showWarning.bind(window.UnifiedNotifications);

// للتوافق مع الأنظمة القديمة
window.NotificationUtils = window.UnifiedNotifications;

console.log('✅ تم تحميل نظام الإشعارات الموحد');
